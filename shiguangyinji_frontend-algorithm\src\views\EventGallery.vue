<template>
  <div class="gallery-bg">
    <div class="gallery-container">
      <!-- 页面标题区域 -->
      <header class="gallery-header">
        <div class="header-content">
          <h1 class="gallery-title">时光记忆长廊</h1>
          <p class="gallery-subtitle">珍藏每一个值得铭记的瞬间</p>
        </div>
        <div class="header-actions">
          <button class="make-video-btn" @click="$router.push({ name: 'make-video' })">
            <span class="btn-icon">🎬</span>
            <span>制作回忆视频</span>
          </button>
          <button class="add-memory-btn" @click="$router.push({ name: '上传事件' })">
            <span class="btn-icon">✨</span>
            <span>记录新片段</span>
          </button>
        </div>
      </header>

      <!-- 搜索和筛选区域 -->
      <section class="search-section glass">
        <!-- 关键词搜索 -->
        <div class="search-box">
          <input
            type="text"
            v-model="searchKeyword"
            placeholder="搜索回忆..."
            @input="applyFilters"
            class="search-input"
          />
          <span class="search-icon">🔍</span>
        </div>

        <!-- 时间范围选择 -->
        <div class="date-range">
          <div class="date-field">
            <label>开始日期</label>
            <input
              type="date"
              v-model="startDate"
              @change="applyFilters"
              class="date-input"
            />
          </div>
          <div class="date-separator">至</div>
          <div class="date-field">
            <label>结束日期</label>
            <input
              type="date"
              v-model="endDate"
              @change="applyFilters"
              class="date-input"
            />
          </div>
        </div>
      </section>

      <!-- 筛选条件区域 -->
      <section class="filters-section glass">
        <div class="filter-group">
          <select v-model="selectedProvince" @change="filterEvents" class="filter-select">
            <option value="">全部省份</option>
            <option v-for="province in provinces" :key="province">{{ province }}</option>
          </select>
          <select v-model="selectedCity" @change="applyFilters" class="filter-select">
            <option value="">全部城市</option>
            <option v-for="city in cities" :key="city">{{ city }}</option>
          </select>
          <select v-model="sortOrder" @change="sortEvents" class="filter-select">
            <option value="desc">时间：最新优先</option>
            <option value="asc">时间：最早优先</option>
          </select>
        </div>
        <div class="active-filters" v-if="hasActiveFilters">
          <div class="filter-tag" v-if="searchKeyword">
            关键词: {{ searchKeyword }}
            <span class="remove-filter" @click="clearKeyword">×</span>
          </div>
          <div class="filter-tag" v-if="startDate || endDate">
            时间: {{ formatDateRange }}
            <span class="remove-filter" @click="clearDates">×</span>
          </div>
          <div class="filter-tag" v-if="selectedProvince">
            {{ selectedProvince }}
            <span class="remove-filter" @click="clearProvince">×</span>
          </div>
          <div class="filter-tag" v-if="selectedCity">
            {{ selectedCity }}
            <span class="remove-filter" @click="clearCity">×</span>
          </div>
          <button class="clear-all-btn" @click="clearAllFilters">清除全部</button>
        </div>
      </section>

      <!-- 时间轴事件展示 -->
      <section class="timeline-container" v-if="filteredEvents.length > 0">
        <div class="timeline-line"></div>

        <div
          v-for="(event, index) in filteredEvents"
          :key="event.id"
          class="timeline-event"
          :class="{'right-aligned': index % 2 !== 0}"
        >
          <div class="timeline-date">
            <div class="date-bubble">{{ formatDate(event.date) }}</div>
            <div class="date-connector"></div>
          </div>

          <div class="timeline-card animate-fadeIn">
            <div class="event-image">
              <img :src="event.coverImage" alt="封面图" />
              <div class="event-location">
                <span class="location-icon">📍</span>
                <span>{{ event.province }} {{ event.city }}</span>
              </div>
            </div>
            <div class="event-content">
              <h3 class="event-title">{{ event.title }}</h3>
              <p class="event-description">{{ event.description || '记录美好时光...' }}</p>
              <div class="event-actions">
                <button class="view-btn" @click="viewDetail(event)">
                  <span class="btn-text">查看详情</span>
                  <span class="btn-icon">→</span>
                </button>
                <button class="photos-btn" @click="viewPhotos(event)">
                  <span class="btn-text">图片标注</span>
                  <span class="btn-icon">📷</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="timeline-end" v-if="!allLoaded">
          <button class="load-more-btn" @click="loadMore">
            <span class="load-icon">⏬</span>
            <span>加载更多回忆</span>
          </button>
        </div>
        <div class="timeline-end" v-else>
          <div class="end-marker">
            <span>时光旅程</span>
          </div>
        </div>
      </section>

      <!-- 无结果提示 -->
      <section class="no-results" v-if="filteredEvents.length === 0">
        <div class="no-results-content">
          <div class="no-results-icon">🔍</div>
          <h3>未找到符合条件的回忆</h3>
          <p>尝试调整搜索条件或清除筛选器</p>
          <button class="clear-all-btn" @click="clearAllFilters">清除全部筛选</button>
        </div>
      </section>
    </div>
  </div>
</template>

<script>
import provinceCityMapping from '@/assets/map/province_city_mapping.json'
import service from '@/utils/request.js'

export default {
  data() {
    return {
      events: [],  // 全部事件（从后端或本地模拟加载）
      filteredEvents: [],
      provinces: Object.keys(provinceCityMapping),
      cities: [],
      selectedProvince: '',
      selectedCity: '',
      sortOrder: 'desc',
      page: 1,
      pageSize: 8,
      allLoaded: false,
      // 新增搜索和时间范围筛选
      searchKeyword: '',
      startDate: '',
      endDate: '',
      // 用于存储筛选后但未分页的结果
      filteredBeforePaging: []
    };
  },
  computed: {
    // 判断是否有激活的筛选条件
    hasActiveFilters() {
      return this.searchKeyword || this.startDate || this.endDate ||
             this.selectedProvince || this.selectedCity;
    },
    // 格式化日期范围显示
    formatDateRange() {
      let result = '';
      if (this.startDate) {
        result += this.formatDateShort(this.startDate);
      } else {
        result += '不限';
      }

      result += ' 至 ';

      if (this.endDate) {
        result += this.formatDateShort(this.endDate);
      } else {
        result += '不限';
      }

      return result;
    }
  },
  mounted() {
    this.loadInitialEvents();
    const userEvents = JSON.parse(localStorage.getItem('userEvents') || '[]');

    // 确保用户事件有描述字段
    const processedUserEvents = userEvents.map(event => ({
      ...event,
      description: event.description || '',
      // 确保日期格式一致
      date: event.datetime ? event.datetime.split('T')[0] : (event.date || new Date().toISOString().split('T')[0])
    }));

    this.events = [...processedUserEvents, ...this.events];
    this.applyFilters();
  },
  methods: {
    loadInitialEvents() {
      
      const mockData = [
        // {
        //   id: 1,
        //   title: '出发，前往黄山！',
        //   date: '2022-09-01',
        //   description: '今天和朋友一起坐火车前往黄山，一路上风景美极了。',
        //   province: '安徽省',
        //   city: '黄山市',
        //   coverImage: new URL('@/assets/example/huoche0.png', import.meta.url).href,
        // },
        // {
        //   id: 2,
        //   title: '勇攀高峰',
        //   date: '2022-09-02',
        //   description: '爬黄山对我们老年人来说还是不容易，所幸坚持下来了！',
        //   province: '安徽省',
        //   city: '黄山市',
        //   coverImage: new URL('@/assets/example/huangshan1.png', import.meta.url).href,
        // },
        // {
        //   id: 3,
        //   title: '参加孙儿的毕业典礼',
        //   date: '2023-06-05',
        //   description: '第一次去孙儿小明的大学，没想到就是毕业典礼。',
        //   province: '北京市',
        //   city: '海淀区',
        //   coverImage: new URL('@/assets/example/graduate0.png', import.meta.url).href,
        // },
      ];
      // 从后端获取
      service({
        method: 'get',
        url: '/issue/issue/0/'
      }).then(async res => {
        const idList = res.data.event_ids || [];
        if (idList.length === 0) {
          this.events = [];
          this.applyFilters();
          return;
        }
        // 并发请求每个事件详情
        const detailPromises = idList.map(id => {
          const formData = new FormData();
          formData.append('issue_id', id); // 这里直接用 id
          return service({
            method: 'post',
            url: '/issue/issue/0/',
            headers: { 'Content-Type': 'multipart/form-data' },
            data: formData
          }).then(detailRes => {
            const d = detailRes.data;
            // 适配前端字段
            return {
              id: d.issue_id,
              title: d.title,
              date: d.date,
              description: d.description,
              province: d.location ? d.location.split(' ')[0] : '',
              city: d.location ? d.location.split(' ')[1] || '' : '',
              coverImage: (d.pictures && d.pictures.length > 0) ? 'http://127.0.0.1:8000' + d.pictures[0].url : '',
              pictures: d.pictures || [],
              author: d.author
            };
          }).catch(() => null);
        });
        const details = await Promise.all(detailPromises);
        this.events = details.filter(e => e); // 过滤掉失败的
        this.applyFilters();
      }).catch(err => {
        console.error('获取事件列表失败', err);
        this.events = [];
        this.applyFilters();
      });
    },

    // 格式化日期显示 (YYYY-MM-DD -> MM月DD日)
    formatDate(dateString) {
      if (!dateString) return '';

      const date = new Date(dateString);
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const year = date.getFullYear();

      return `${year}年${month}月${day}日`;
    },

    // 简短日期格式 (YYYY-MM-DD -> MM/DD)
    formatDateShort(dateString) {
      if (!dateString) return '';

      const parts = dateString.split('-');
      if (parts.length !== 3) return dateString;

      return `${parts[1]}/${parts[2]}`;
    },

    // 省份变更时更新城市列表
    filterEvents() {
      this.cities = this.getCitiesByProvince(this.selectedProvince);
      // 如果切换省份，清空已选城市
      if (this.selectedCity && !this.cities.includes(this.selectedCity)) {
        this.selectedCity = '';
      }
      this.applyFilters();
    },

    // 获取省份对应的城市列表
    getCitiesByProvince(province) {
      return provinceCityMapping[province] || [];
    },

    // 应用所有筛选条件
    applyFilters() {
      let result = [...this.events];

      // 关键词搜索 (标题、描述和位置)
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        result = result.filter(e =>
          (e.title && e.title.toLowerCase().includes(keyword)) ||
          (e.description && e.description.toLowerCase().includes(keyword)) ||
          (e.province && e.province.toLowerCase().includes(keyword)) ||
          (e.city && e.city.toLowerCase().includes(keyword))
        );
      }

      // 没有关键词时，直接本地筛选
      this._applyOtherFilters(result);
    },

    // 新增一个方法用于本地其他筛选
    _applyOtherFilters(result) {
      // 日期范围筛选
      if (this.startDate) {
        result = result.filter(e => new Date(e.date) >= new Date(this.startDate));
      }
      if (this.endDate) {
        // 将结束日期设为当天的23:59:59，以包含整天
        const endDateObj = new Date(this.endDate);
        endDateObj.setHours(23, 59, 59);
        result = result.filter(e => new Date(e.date) <= endDateObj);
      }

      // 地点筛选
      if (this.selectedProvince) {
        result = result.filter(e => e.province === this.selectedProvince);
      }
      if (this.selectedCity) {
        result = result.filter(e => e.city === this.selectedCity);
      }

      // 保存筛选后的结果用于分页
      this.filteredBeforePaging = result;

      // 应用排序和分页
      this.page = 1; // 重置页码
      this.sortEvents(result);
    },

    // 排序并应用分页
    sortEvents(events = this.filteredBeforePaging) {
      const sorted = [...events].sort((a, b) => {
        return this.sortOrder === 'asc'
          ? new Date(a.date) - new Date(b.date)
          : new Date(b.date) - new Date(a.date);
      });

      this.filteredEvents = sorted.slice(0, this.page * this.pageSize);
      this.allLoaded = sorted.length <= this.filteredEvents.length;
    },

    // 加载更多事件
    loadMore() {
      this.page++;
      this.sortEvents();
    },

    // 查看事件详情
    viewDetail(event) {
      this.$router.push({ name: 'event-detail', params: { id: event.id } });
    },

    // 查看事件图片标注
    viewPhotos(event) {
      this.$router.push({ name: 'EventPhotos', params: { id: event.id } });
    },

    // 清除筛选条件
    clearKeyword() {
      this.searchKeyword = '';
      this.applyFilters();
    },

    clearDates() {
      this.startDate = '';
      this.endDate = '';
      this.applyFilters();
    },

    clearProvince() {
      this.selectedProvince = '';
      this.selectedCity = '';
      this.cities = [];
      this.applyFilters();
    },

    clearCity() {
      this.selectedCity = '';
      this.applyFilters();
    },

    clearAllFilters() {
      this.searchKeyword = '';
      this.startDate = '';
      this.endDate = '';
      this.selectedProvince = '';
      this.selectedCity = '';
      this.cities = [];
      this.applyFilters();
    }
  },
};
</script>

<style scoped>
/* 导入字体 */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap');

/* 动画定义 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInLeft {
  from { opacity: 0; transform: translateX(-30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* 基础样式 */
.gallery-bg {
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #f0f4ff 0%, #e6f7ff 50%, #f0fdfa 100%);
  background-image:
    radial-gradient(circle at 10% 20%, rgba(120, 119, 198, 0.05) 0%, transparent 20%),
    radial-gradient(circle at 90% 80%, rgba(242, 202, 241, 0.08) 0%, transparent 20%),
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.9) 0%, transparent 100%),
    linear-gradient(135deg, #f0f4ff 0%, #e6f7ff 50%, #f0fdfa 100%);
  padding: 0;
  margin: 0;
  overflow-x: hidden;
  font-family: 'Noto Sans SC', sans-serif;
  position: relative;
}

.gallery-container {
  max-width: 1100px;
  margin: 0 auto;
  padding: 40px 20px 60px;
  min-height: 100vh;
  width: 100%;
}

/* 页面头部 */
.gallery-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
  position: relative;
}

.header-content {
  margin-bottom: 30px;
  animation: fadeIn 0.8s ease-out;
}

.gallery-title {
  font-size: 3rem;
  font-weight: 800;
  color: #1e3a8a;
  margin: 0 0 10px;
  letter-spacing: 2px;
  text-shadow: 0 4px 24px rgba(30, 58, 138, 0.08);
  background: linear-gradient(90deg, #1e3a8a, #3b82f6);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.gallery-subtitle {
  font-size: 1.25rem;
  color: #4b5563;
  margin: 0;
  font-weight: 400;
  letter-spacing: 1px;
}

.header-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
}

.make-video-btn, .add-memory-btn {
  padding: 12px 24px;
  font-size: 1rem;
  border: none;
  border-radius: 999px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.make-video-btn {
  background: linear-gradient(90deg, #2563eb, #3b82f6);
  color: white;
}

.add-memory-btn {
  background: linear-gradient(90deg, #10b981, #34d399);
  color: white;
}

.make-video-btn:hover, .add-memory-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.btn-icon {
  font-size: 1.2rem;
}

/* 搜索区域 */
.search-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(30, 58, 138, 0.08);
  animation: fadeIn 0.6s ease-out 0.2s both;
}

@media (min-width: 992px) {
  .search-section {
    flex-direction: row;
    align-items: flex-start;
  }
}

.glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.search-box {
  position: relative;
  flex: 1;
  margin-bottom: 10px;
}

@media (min-width: 992px) {
  .search-box {
    margin-bottom: 0;
    margin-right: 20px;
  }
}

.search-input {
  width: 80%;
  padding: 14px 20px 14px 50px;
  font-size: 1rem;
  border: 2px solid #dbeafe;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s;
}

.search-input:focus {
  outline: none;
  border-color: #60a5fa;
  box-shadow: 0 0 0 4px rgba(96, 165, 250, 0.15);
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  color: #60a5fa;
}

.date-range {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  min-width: 300px;
}

@media (max-width: 991px) {
  .date-range {
    min-width: 100%;
  }
}

.date-field {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.date-field label {
  font-size: 0.85rem;
  color: #4b5563;
  font-weight: 500;
}

.date-input {
  padding: 10px 16px;
  border: 2px solid #dbeafe;
  border-radius: 8px;
  font-size: 0.95rem;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s;
}

.date-input:focus {
  outline: none;
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.15);
}

.date-separator {
  margin: 0 5px;
  color: #6b7280;
  align-self: flex-end;
  margin-bottom: 10px;
}

/* 筛选条件区域 */
.filters-section {
  margin-bottom: 40px;
  padding: 20px;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(30, 58, 138, 0.08);
  animation: fadeIn 0.6s ease-out 0.3s both;
}

.filter-group {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 15px;
}

.filter-select {
  padding: 12px 20px;
  font-size: 0.95rem;
  border: 2px solid #dbeafe;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s;
  min-width: 150px;
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%233b82f6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  padding-right: 40px;
}

.filter-select:focus {
  outline: none;
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.15);
}

.active-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid rgba(219, 234, 254, 0.5);
}

.filter-tag {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: #dbeafe;
  color: #1e40af;
  border-radius: 999px;
  font-size: 0.85rem;
  font-weight: 500;
}

.remove-filter {
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: bold;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transition: all 0.2s;
}

.remove-filter:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: scale(1.1);
}

.clear-all-btn {
  padding: 6px 12px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 999px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.clear-all-btn:hover {
  background: #dc2626;
  transform: scale(1.05);
}

/* 时间轴样式 */
.timeline-container {
  position: relative;
  padding: 20px 0;
  margin-bottom: 40px;
}

.timeline-line {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  width: 4px;
  background: linear-gradient(to bottom, #3b82f6, #10b981);
  transform: translateX(-50%);
  border-radius: 999px;
  z-index: 1;
}

.timeline-event {
  display: flex;
  margin-bottom: 60px;
  position: relative;
  z-index: 2;
}

.timeline-event.right-aligned {
  flex-direction: row-reverse;
}

.timeline-date {
  width: 50%;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  padding-right: 30px;
}

.timeline-event.right-aligned .timeline-date {
  align-items: flex-start;
  padding-right: 0;
  padding-left: 30px;
}

.date-bubble {
  background: #3b82f6;
  color: white;
  padding: 8px 16px;
  border-radius: 999px;
  font-weight: 500;
  font-size: 0.9rem;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  margin-bottom: 10px;
}

.timeline-event.right-aligned .date-bubble {
  background: #10b981;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.date-connector {
  width: 30px;
  height: 2px;
  background: #3b82f6;
  margin-right: -30px;
}

.timeline-event.right-aligned .date-connector {
  background: #10b981;
  margin-right: 0;
  margin-left: -30px;
}

.timeline-card {
  width: 50%;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  animation: slideInRight 0.6s ease-out both;
}

.timeline-event.right-aligned .timeline-card {
  animation: slideInLeft 0.6s ease-out both;
}

.timeline-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.event-image {
  position: relative;
  height: 200px;
}

.event-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.event-location {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 6px 12px;
  border-radius: 999px;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

.location-icon {
  font-size: 1rem;
}

.event-content {
  padding: 20px;
}

.event-title {
  margin: 0 0 10px;
  font-size: 1.3rem;
  color: #1e293b;
  font-weight: 700;
}

.event-description {
  color: #64748b;
  font-size: 0.95rem;
  margin: 0 0 20px;
  line-height: 1.5;
}

.event-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.view-btn, .photos-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.photos-btn {
  background: #8b5cf6;
}

.timeline-event.right-aligned .view-btn {
  background: #10b981;
}

.timeline-event.right-aligned .photos-btn {
  background: #f59e0b;
}

.view-btn:hover {
  transform: translateX(5px);
}

.photos-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.timeline-end {
  display: flex;
  justify-content: center;
  margin-top: 40px;
  position: relative;
  z-index: 2;
}

.load-more-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(90deg, #3b82f6, #10b981);
  color: white;
  border: none;
  border-radius: 999px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.3);
}

.load-more-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.load-icon {
  font-size: 1.2rem;
}

.end-marker {
  background: linear-gradient(90deg, #3b82f6, #10b981);
  color: white;
  padding: 8px 20px;
  border-radius: 999px;
  font-weight: 600;
  font-size: 1rem;
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.3);
}

/* 无结果提示 */
.no-results {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  animation: fadeIn 0.6s ease-out both;
}

.no-results-content {
  text-align: center;
  background: white;
  padding: 40px;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.no-results-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  animation: pulse 2s infinite;
}

.no-results h3 {
  margin: 0 0 10px;
  font-size: 1.5rem;
  color: #1e293b;
}

.no-results p {
  color: #64748b;
  margin: 0 0 20px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .timeline-line {
    left: 20px;
  }

  .timeline-event, .timeline-event.right-aligned {
    flex-direction: column;
    margin-left: 40px;
  }

  .timeline-date, .timeline-event.right-aligned .timeline-date {
    width: 100%;
    align-items: flex-start;
    padding: 0;
    margin-bottom: 15px;
  }

  .date-connector, .timeline-event.right-aligned .date-connector {
    display: none;
  }

  .timeline-card, .timeline-event.right-aligned .timeline-card {
    width: 100%;
    animation: fadeIn 0.6s ease-out both;
  }

  .date-bubble, .timeline-event.right-aligned .date-bubble {
    margin-left: -40px;
  }
}
</style>