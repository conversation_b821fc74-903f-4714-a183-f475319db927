<script setup>
import commitIcon from "@/assets/icon/commit.svg";
import saveIcon from "@/assets/icon/save.svg";

import {QuillEditor} from "@vueup/vue-quill";
import { ref, reactive, onMounted, watchEffect, toRaw } from 'vue';
import '@vueup/vue-quill/dist/vue-quill.snow.css';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import {createDocument, editDocument} from '@/api/api.js';
import { useRoute } from 'vue-router';
import router from "@/router/index.js";




const route = useRoute();
const title = route.query.title || '';
const content = decodeURIComponent(route.query.content || '');


const emit = defineEmits(['update:value']);

const isListening = ref(false);
const quillEditor = ref(null);
const showDialog = ref(false);
const selectedOption = ref('');

const startRecognition = () => {
  if (isListening.value) return;
  console.log("开始语音识别...");
  recognition.start();
  isListening.value = true;
};

const exportToPDF = async () => {
  // 功能实现有问题
  if (!quillEditor.value) {
    console.error('Quill editor is not initialized');
    return;
  }

  const quill = quillEditor.value.getQuill();  // 获取 Quill 实例
  if (!quill) {
    console.error('Quill instance is not available');
    return;
  }

  // 使用 html2canvas 渲染编辑器内容为图像
  const canvas = await html2canvas(quill.root, {
    allowTaint: true,  // 允许跨域图片渲染
    useCORS: true,     // 尝试使用 CORS 加载外部图片
    backgroundColor: 'transparent',
  });

  const imgData = canvas.toDataURL('image/png');  // 转换为 PNG 格式图像数据

  const doc = new jsPDF();

  // 将图像数据添加到 PDF 中
  doc.addImage(imgData, 'PNG', 10, 10, canvas.width * 0.2, canvas.height * 0.2);  // 调整图像大小和位置

  doc.save('.pdf');
};


const editorOption = reactive({
  modules: {
    toolbar: {
      container: [
        ['bold', 'italic', 'underline', 'strike'],
        [{ color: [] }, { background: [] }],
        [{ size: ['small', false, 'large', 'huge'] }],
        [{ font: [] }],
        [{ align: [] }],
        [{ list: 'ordered' }, { list: 'bullet' }],
        ['blockquote', 'code-block'],
        ['speech', 'image', 'video', 'export'],
      ],
      handlers: {
        speech: startRecognition,
        export: exportToPDF
      },
    },
  },
  placeholder: '请输入内容...',
  theme: 'snow',
});

watchEffect(() => {
  emit('update:value', content.value);
});

let recognition;

onMounted(() => {
  if (quillEditor.value) {
    const quill = quillEditor.value.getQuill();
    if (quill) {
      console.log('Quill editor initialized');
    }
  }

  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

  if (!SpeechRecognition) {
    alert("Your browser does not support Speech Recognition. Please try Chrome.");
    return;
  }

  recognition = new SpeechRecognition();
  recognition.lang = 'zh-CN';
  recognition.interimResults = false;
  recognition.maxAlternatives = 1;

  recognition.addEventListener('result', (event) => {
    const transcript = event.results[0][0].transcript;
    insertTextToQuill(transcript);
  });

  recognition.addEventListener('end', () => {
    isListening.value = false;
  });

  recognition.addEventListener('error', (event) => {
    alert(`Error occurred: ${event.error}`);
    isListening.value = false;
  });
});

const insertTextToQuill = (text) => {
  const quill = toRaw(quillEditor.value.getQuill());
  const length = quill.getSelection()?.index || 0;
  quill.insertText(length, text);
  quill.setSelection(length + text.length);
};

const openDialog = () => {
  showDialog.value = true;
};

const closeDialog = () => {
  showDialog.value = false;
};

const selectOption = (option) => {
  selectedOption.value = option;
};

const confirmSubmit = async () => {
  const quill = quillEditor.value?.getQuill();
  if (!quill) {
    console.error('Quill editor is not initialized');
    return;
  }

  const title = document.querySelector('.article-title')?.value.trim();
  const contentHTML = quill.root.innerHTML;

  if (!title) {
    alert('标题不能为空');
    return;
  }

  if (route.query.title) {
    console.log(contentHTML)

    const response = await editDocument(route.query.id, {
      title,
      content: contentHTML
    });
    if (response.success) {
      alert(`文章已更新`);
    } else {
      alert('更新失败，请重试');
    }

    showDialog.value = false;
    selectedOption.value = '';

    router.push({ path: '/user' });
  } else {
    const response = await createDocument(title, contentHTML);

    if (response.success) {
      alert(`文章已提交`);
    } else {
      alert('提交失败，请重试');
    }

    showDialog.value = false;
    selectedOption.value = '';
  }
};

const saveDraft = () => {
  alert('草稿已保存');

};

const discard = () => {
  alert('文章已丢弃');
};
</script>

<template>
  <div class="container">
    <div class="main-box">
      <div class="editor-container">
        <input type="text" v-model="title" class="article-title" placeholder="标题" />

        <QuillEditor ref="quillEditor" content-type="html" v-model:content="content" :options="editorOption" />

        <div class="btn-container">
          <button @click="confirmSubmit" class="btn-icon">
            <img :src="commitIcon" alt="提交" />
          </button>
          <button @click="saveDraft" class="btn-icon">
            <img :src="saveIcon" alt="保存" />
          </button>

        </div>

        <div v-if="showDialog" class="dialog-overlay">
          <div class="dialog-box">
            <button class="close-btn" @click="closeDialog">
              <img src="@/assets/icon/close.svg" alt="关闭" />
            </button>
            <p>请选择文章状态：</p>
            <button class="dialog-btn" @click="selectOption('public')">公开(所有用户可见)</button>
            <button class="dialog-btn" @click="selectOption('private')">私密(仅自己可见)</button>
            <button class="dialog-btn" :disabled="!selectedOption" @click="confirmSubmit">确认</button>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<style scoped>
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  overflow-x: hidden;
}

.main-box {
  width: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.editor-container {
  width: 100%;
}

.article-title {
  width: 100%;
  padding: 12px;
  font-size: 1.5em;
  border: none;
  border-bottom: 2px solid #eee;
  outline: none;
  transition: border-color 0.3s;
  margin-bottom: 20px;
}

.article-title:focus {
  border-color: #4CAF50;
}

.btn-container {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 20px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 8px;
}

.btn-icon {
  padding: 10px 25px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.btn-icon:first-child {
  background-color: #4CAF50;
  color: white;
}

.btn-icon:first-child:hover {
  background-color: #45a049;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-icon:last-child {
  background-color: #2196F3;
  color: white;
}

.btn-icon:last-child:hover {
  background-color: #1976D2;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-icon img {
  width: 24px;
  height: 24px;
}

.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog-box {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
}

.dialog-btn {
  display: block;
  width: 100%;
  padding: 12px;
  margin: 10px 0;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 16px;
  font-weight: 500;
}

.dialog-btn:first-child {
  background-color: #4CAF50;
  color: white;
}

.dialog-btn:first-child:hover {
  background-color: #45a049;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.dialog-btn:nth-child(2) {
  background-color: #2196F3;
  color: white;
}

.dialog-btn:nth-child(2):hover {
  background-color: #1976D2;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.dialog-btn:last-child {
  background-color: #f0f0f0;
  color: #333;
}

.dialog-btn:last-child:hover {
  background-color: #e0e0e0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  cursor: pointer;
}

.close-btn img {
  width: 20px;
  height: 20px;
}
</style>
