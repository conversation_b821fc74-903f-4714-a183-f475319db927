:root {
  /* Main color palette */
  --primary-color: #4a6572;
  --primary-light: #7a93a0;
  --primary-dark: #1e3b47;
  --accent-color: #899a8c;
  --accent-light: #b9cabb;
  --accent-dark: #5c6b5e;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-light: #ffffff;
  --background-light: #f8f9fa;
  --background-white: #ffffff;
  --background-card: #ffffff;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --border-radius: 10px;
  --transition-speed: 0.3s;

  /* Typography */
  font-family: 'ShangGuM', Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.6;
  font-weight: 400;
  font-size: 16px;

  /* System */
  color-scheme: light;
  color: var(--text-primary);
  background-color: var(--background-light);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: var(--primary-color);
  text-decoration: inherit;
  transition: color var(--transition-speed) ease;
}

a:hover {
  color: var(--primary-dark);
}

body {
  margin: 0;
  display: flex;
  min-width: 320px;
  min-height: 100vh;
  width: 100%;
  background-color: var(--background-light);
  background-image: url('@/assets/images/test.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-blend-mode: overlay;
  opacity: 0.97;
  overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'ShangGuB', sans-serif;
  line-height: 1.2;
  margin-top: 0;
  margin-bottom: 0.5em;
  color: var(--text-primary);
}

h1 {
  font-size: 2.5rem;
  font-weight: 700;
}

h2 {
  font-size: 2rem;
  font-weight: 600;
}

h3 {
  font-size: 1.75rem;
  font-weight: 600;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

/* Buttons */
button {
  border-radius: var(--border-radius);
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1rem;
  font-weight: 500;
  font-family: inherit;
  background-color: var(--primary-color);
  color: var(--text-light);
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  box-shadow: 0 2px 4px var(--shadow-color);
}

button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-color);
}

button:focus,
button:focus-visible {
  outline: 3px solid var(--primary-light);
  outline-offset: 2px;
}

button.secondary {
  background-color: var(--accent-color);
}

button.secondary:hover {
  background-color: var(--accent-dark);
}

/* Cards */
.card {
  background-color: var(--background-card);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 6px var(--shadow-color);
  padding: 1.5rem;
  transition: transform var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px var(--shadow-color);
}

/* Layout */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -0.5rem;
}

.col {
  flex: 1;
  padding: 0 0.5rem;
}

/* App container */
#app {
  width: 100%;
  margin: 0 auto;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.5s ease-in;
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

/* Responsive */
@media (max-width: 768px) {
  :root {
    font-size: 14px;
  }

  .container {
    padding: 0 0.5rem;
  }
}
