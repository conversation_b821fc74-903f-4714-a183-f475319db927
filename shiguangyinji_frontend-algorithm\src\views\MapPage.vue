<template>
  <div id="map-container" ref="mapContainer"></div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

const mapContainer = ref(null);

onMounted(() => {
  // 初始化地图，限制在中国范围内
  const map = L.map(mapContainer.value, {
    maxBounds: [
      [3.86, 73.66],
      [53.55, 135.05]
    ],
    maxBoundsViscosity: 0.5
  }).setView([35, 105], 5);

  // 使用高德地图瓦片
  L.tileLayer('https://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}', {
    subdomains: ['1', '2', '3', '4'],
    attribution: '高德地图'
  }).addTo(map);

  // 中国主要城市数据
  const cities = [
    {
      name: "北京",
      position: [39.9042, 116.4074],
      image: "https://s3.bmp.ovh/imgs/2025/04/13/d1d9358aff0861d2.webp",
      description: "中国首都，政治文化中心"
    },
    {
      name: "上海",
      position: [31.2304, 121.4737],
      image: "https://example.com/shanghai.jpg",
      description: "中国经济金融中心"
    },
    // 更多城市...
  ];

  // 添加自定义图标
  const redIcon = L.icon({
    iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png',
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34]
  });

  // 添加城市标记
  cities.forEach(city => {
    L.marker(city.position, { icon: redIcon })
      .addTo(map)
      .bindPopup(`
        <div class="city-popup">
          <h3>${city.name}</h3>
          <img src="${city.image}" alt="${city.name}">
          <p>${city.description}</p>
        </div>
      `);
  });
});
</script>

<style>
#map-container {
  height: 800px;
  width: 1200px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.city-popup {
  max-width: 250px;
}
.city-popup h3 {
  margin-top: 0;
  color: #d00;
}
.city-popup img {
  width: 100%;
  height: auto;
  margin: 5px 0;
}
.city-popup p {
  margin-bottom: 0;
}
</style>


