/**
 * 前端错误处理工具
 * 用于统一处理后端返回的错误和警告
 */

/**
 * 处理API响应，区分错误和警告
 * @param {Object} response - axios响应对象
 * @param {Object} options - 处理选项
 * @returns {Object} 处理后的数据
 */
export function handleApiResponse(response, options = {}) {
  const {
    showWarnings = true,
    throwOnWarnings = false,
    defaultErrorMessage = '操作失败，请稍后重试'
  } = options

  try {
    const data = response.data

    // 检查是否有警告
    if (data && data.warnings) {
      if (showWarnings) {
        console.warn('后端警告:', data.warnings)
        
        // 可以选择显示用户友好的警告消息
        if (Array.isArray(data.warnings)) {
          data.warnings.forEach(warning => {
            console.warn('警告:', warning)
          })
        } else {
          console.warn('警告:', data.warnings)
        }
      }

      // 根据配置决定是否将警告当作错误处理
      if (throwOnWarnings) {
        throw new Error(Array.isArray(data.warnings) ? data.warnings.join('; ') : data.warnings)
      }
    }

    // 检查是否有错误
    if (data && data.error) {
      throw new Error(data.error)
    }

    // 返回处理后的数据
    return data
  } catch (error) {
    console.error('处理API响应时出错:', error)
    throw error
  }
}

/**
 * 处理API错误
 * @param {Error} error - 错误对象
 * @param {Object} options - 处理选项
 * @returns {Object} 错误信息
 */
export function handleApiError(error, options = {}) {
  const {
    showUserMessage = true,
    defaultMessage = '操作失败，请稍后重试',
    logError = true
  } = options

  let errorMessage = defaultMessage
  let errorCode = null
  let errorDetails = null

  if (error.response) {
    // 服务器返回了错误状态码
    const { status, data } = error.response
    errorCode = status

    if (logError) {
      console.error(`HTTP ${status} 错误:`, data)
    }

    // 提取错误消息
    if (data) {
      if (typeof data === 'string') {
        errorMessage = data
      } else if (data.error) {
        errorMessage = data.error
      } else if (data.message) {
        errorMessage = data.message
      } else if (data.detail) {
        errorMessage = data.detail
      }
      
      errorDetails = data
    }

    // 根据状态码提供更具体的错误消息
    switch (status) {
      case 400:
        errorMessage = data?.error || '请求参数错误'
        break
      case 401:
        errorMessage = '请先登录'
        break
      case 403:
        errorMessage = '权限不足'
        break
      case 404:
        errorMessage = '请求的资源不存在'
        break
      case 500:
        errorMessage = '服务器内部错误，请稍后重试'
        break
      case 502:
      case 503:
      case 504:
        errorMessage = '服务暂时不可用，请稍后重试'
        break
    }
  } else if (error.request) {
    // 请求已发出但没有收到响应
    errorMessage = '网络连接失败，请检查网络设置'
    if (logError) {
      console.error('网络错误:', error.request)
    }
  } else {
    // 其他错误
    errorMessage = error.message || defaultMessage
    if (logError) {
      console.error('请求配置错误:', error.message)
    }
  }

  const errorInfo = {
    message: errorMessage,
    code: errorCode,
    details: errorDetails,
    originalError: error
  }

  if (showUserMessage && errorMessage) {
    // 这里可以集成消息提示组件
    console.error('用户错误消息:', errorMessage)
  }

  return errorInfo
}

/**
 * 安全的API调用包装器
 * @param {Function} apiCall - API调用函数
 * @param {Object} options - 选项
 * @returns {Promise} API调用结果
 */
export async function safeApiCall(apiCall, options = {}) {
  const {
    retries = 0,
    retryDelay = 1000,
    fallbackData = null,
    onError = null,
    onWarning = null
  } = options

  let lastError = null

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const response = await apiCall()
      
      // 处理响应
      const data = handleApiResponse(response, {
        showWarnings: true,
        throwOnWarnings: false
      })

      // 检查警告
      if (data && data.warnings && onWarning) {
        onWarning(data.warnings)
      }

      return data
    } catch (error) {
      lastError = error
      
      // 如果还有重试次数，等待后重试
      if (attempt < retries) {
        console.warn(`API调用失败，${retryDelay}ms后进行第${attempt + 1}次重试...`)
        await new Promise(resolve => setTimeout(resolve, retryDelay))
        continue
      }

      // 处理错误
      const errorInfo = handleApiError(error, {
        showUserMessage: false,
        logError: true
      })

      if (onError) {
        onError(errorInfo)
      }

      // 如果有回退数据，返回回退数据而不是抛出错误
      if (fallbackData !== null) {
        console.warn('API调用失败，使用回退数据:', fallbackData)
        return fallbackData
      }

      throw error
    }
  }
}

/**
 * 创建带有错误处理的API调用函数
 * @param {Function} apiFunction - 原始API函数
 * @param {Object} defaultOptions - 默认选项
 * @returns {Function} 包装后的API函数
 */
export function createSafeApiCall(apiFunction, defaultOptions = {}) {
  return async (...args) => {
    return safeApiCall(() => apiFunction(...args), defaultOptions)
  }
}

/**
 * 检查响应是否包含警告
 * @param {Object} response - 响应对象
 * @returns {boolean} 是否包含警告
 */
export function hasWarnings(response) {
  return response && response.data && response.data.warnings
}

/**
 * 提取响应中的警告信息
 * @param {Object} response - 响应对象
 * @returns {Array|string|null} 警告信息
 */
export function extractWarnings(response) {
  if (!hasWarnings(response)) {
    return null
  }
  return response.data.warnings
}

export default {
  handleApiResponse,
  handleApiError,
  safeApiCall,
  createSafeApiCall,
  hasWarnings,
  extractWarnings
}
