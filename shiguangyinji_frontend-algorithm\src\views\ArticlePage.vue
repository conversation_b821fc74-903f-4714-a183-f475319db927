<template>
  <div class="page-container">
    <!-- 返回按钮 -->
    <div class="back-button" @click="goBack">
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M19 12H5M12 19l-7-7 7-7"/>
      </svg>
      <span>返回</span>
    </div>

    <!-- 页面内容 -->
    <div class="content-container">
      <!-- 左侧图片部分，如果有图片则显示图片 -->
<!--      <div v-if="articleData.imageSrc" class="image-section">-->
<!--        <img :src="articleData.imageSrc" alt="文章图片" />-->
<!--      </div>-->

      <!-- 右侧的文章内容 -->
      <div class="text-section" >
        <!-- 用户头像和用户名 -->
        <div class="header">
          <img :src="authorData.avatar_url" alt="作者头像" class="avatar"/>
          <span class="user-name">{{ authorData.nickname }}</span>
<!--          <button @click="toggleFollow" class="follow-button">-->
<!--            {{ isFollowing ? '已关注' : '关注' }}-->
<!--          </button>-->
        </div>

        <!-- 文章标题 -->
        <h2 class="article-title">{{ title }}</h2>

        <!-- 文章内容 -->
        <div class="article-content" v-html="content"></div>

        <!-- 点赞和收藏按钮 -->
        <div class="action-buttons">
          <button @click="like" class="like-button" v-if="!isLiked">
            <img src="@/assets/icon/like.svg" alt="点赞" /> 点赞 {{ likes }}
          </button>
          <button @click="like" class="like-button" v-if="isLiked" style="background-color: #b8dec1">
            <img src="@/assets/icon/like.svg" alt="点赞" /> 点赞 {{ likes }}
          </button>
          <button @click="collect" class="collect-button" v-if="!isFavorited">
            <img src="@/assets/icon/collect.svg" alt="收藏" /> 收藏
          </button>
          <button @click="collect" class="collect-button" v-if="isFavorited" style="background-color: #ffdead">
            <img src="@/assets/icon/collect.svg" alt="收藏" /> 收藏
          </button>
        </div>

        <!-- 评论区 -->
        <div class="comments-section">
          <h3 class="comments-title">评论 ({{ comments.length }})</h3>
          <ul>
            <li v-for="(comment, index) in comments" :key="index" class="comment-item">
              <div class="comment-content">
                <div class="comment-header">
                  <img :src="comment.avatar_url" alt="用户头像" class="comment-avatar" />
                  <span class="comment-user-name">{{ comment.nickname }}</span>
                </div>
                <p class="comment-text">{{ comment.content }}</p>
              </div>
            </li>
          </ul>
        </div>
        <div class="comment-input-container">
          <input
            type="text"
            v-model="newComment"
            placeholder="分享你的感受和回忆..."
            @keyup.enter="addComment"
            class="comment-input"
          />
          <button @click="addComment" class="comment-submit-button">发表评论</button>
        </div>
      </div>
    </div>
  </div>
</template>


<script setup>
import {onMounted, ref} from 'vue';
import testImage from "@/assets/test.jpg";
import testImage1 from "@/assets/covers/tibet.jpg";
import testImage2 from "@/assets/covers/graduation.jpg";
import testImage3 from "@/assets/covers/family.jpg";
import testImage4 from "@/assets/covers/beach.jpg";
import testImage5 from "@/assets/covers/mountain.jpg";
import avatarImage from "@/assets/avatar.png";
import {useRoute} from "vue-router";
import {
  addFavorite,
  checkUserFavorite, getArticleComments,
  getDocById,
  getDocLikeCount,
  GetUserById,
  getUserLikeStatus,
  likeDoc, postArticleComment
} from "@/api/api.js";
import router from "@/router/index.js";

const route = useRoute();
const articleData = ref({
  imageSrc: testImage,
  userAvatar: testImage,
  userName: '加载中...',
});
const title = ref('');
const content = ref('');

const likes = ref(0);
const newComment = ref('');
const isFollowing = ref(false);
const isLiked = ref(false);
const isFavorited = ref(false);
const comments = ref([]);

const toggleFollow = () => {
  isFollowing.value = !isFollowing.value;
};

const like = async () => {
    try {
      const docId = route.query.id;
      await likeDoc(docId);
      if (!isLiked.value) {
        likes.value++;
        isLiked.value = true;
      } else {
        likes.value--;
        isLiked.value = false
      }
    } catch (error) {
      console.error('点赞失败:', error);
    }
};
const collect = async () => {
  try {
    const docId = route.query.id;
    await addFavorite(docId);
    isFavorited.value = !isFavorited.value;
  } catch (error) {
    console.error('收藏失败:', error);
  }
};


const authorData = ref({});

// 示例文章数据
const sampleArticles = {
  1: {
    title: '西藏之旅：追寻心中的净土',
    content: `
      <h2>高原之上的心灵洗礼</h2>
      <p>这次西藏之行让我深深震撼，从拉萨的布达拉宫到纳木错的湖光山色，每一处风景都是大自然的杰作。在海拔4000多米的高原上，我感受到了前所未有的宁静与纯净。</p>

      <h3>1. 布达拉宫的庄严</h3>
      <p>站在布达拉宫前，仰望这座千年古建筑，内心涌起一种莫名的敬畏。红白相间的宫墙在蓝天白云的映衬下显得格外庄严，仿佛能听到历史的回响。</p>

      <h3>2. 纳木错的纯净</h3>
      <p>纳木错的湖水清澈如镜，倒映着雪山和蓝天。湖边的玛尼堆承载着无数人的祈愿，微风吹过，经幡飘扬，那一刻心灵得到了前所未有的宁静。</p>

      <h3>3. 高原反应与坚持</h3>
      <p>虽然高原反应让旅途充满挑战，但正是这种挑战让我更加珍惜每一个美好的瞬间。当看到第一缕阳光洒在雪山之巅时，所有的辛苦都化作了感动的泪水。</p>

      <img src="${testImage1}" alt="西藏美景" style="max-width: 100%; margin: 20px 0;">
    `,
    author: {
      nickname: '旅行者小明',
      avatar_url: avatarImage,
      introduction: '热爱旅行的摄影师，用镜头记录世界的美好'
    },
    likes: 328,
    isLiked: false,
    isFavorited: false,
    comments: [
      {
        user_id: 101,
        nickname: '旅行爱好者',
        avatar_url: avatarImage,
        content: '看了你的分享，我也想去西藏了！那种心灵的震撼一定很难忘吧。'
      },
      {
        user_id: 102,
        nickname: '摄影师小李',
        avatar_url: avatarImage,
        content: '西藏真的是摄影师的天堂，每一帧都是大片！期待看到更多你的作品。'
      }
    ]
  },
  2: {
    title: '毕业季：青春不散场的合影',
    content: `
      <h2>四年时光的美好回忆</h2>
      <p>四年大学时光即将结束，和最亲爱的室友们在樱花盛开的校园里拍下了最后一张合影。那些一起熬夜复习、一起哭笑的日子，都定格在这张照片里。</p>

      <h3>1. 初入校园的青涩</h3>
      <p>还记得大一刚入学时的懵懂模样，拖着行李箱走进宿舍，第一次见到室友时的紧张和兴奋。那时的我们青涩而纯真，对未来充满了无限憧憬。</p>

      <h3>2. 一起奋斗的岁月</h3>
      <p>四年来，我们一起在图书馆熬夜复习，一起为考试焦虑，一起为成绩欢呼。每一次的挫折和成功，都有彼此的陪伴和支持。</p>

      <h3>3. 即将分别的不舍</h3>
      <p>虽然即将各奔东西，但这份友谊将是我们心中永远的珍宝。无论走到哪里，这些美好的回忆都会是我们前行路上最温暖的力量。愿友谊长存，青春不老。</p>

      <img src="${testImage2}" alt="毕业合影" style="max-width: 100%; margin: 20px 0;">
    `,
    author: {
      nickname: '青春记录者小红',
      avatar_url: avatarImage,
      introduction: '用文字记录青春，用镜头定格美好'
    },
    likes: 456,
    isLiked: false,
    isFavorited: false
  },
  3: {
    title: '家庭聚餐：温暖的时光印记',
    content: `
      <h2>家的温暖与爱的传承</h2>
      <p>每年春节的家庭聚餐都是最温馨的时刻，三代人围坐在一起，分享着一年来的点点滴滴。奶奶慈祥的笑容，爸妈忙碌的身影，都是我心中最珍贵的回忆。</p>

      <h3>1. 奶奶的拿手菜</h3>
      <p>餐桌上摆满了奶奶亲手做的拿手菜，每一道菜都承载着满满的爱意。红烧肉、糖醋排骨、白切鸡...这些熟悉的味道，是家的味道，是爱的味道。</p>

      <h3>2. 三代人的欢声笑语</h3>
      <p>小侄子在旁边调皮捣蛋，逗得全家人哈哈大笑。爷爷讲着过去的故事，爸爸分享着工作的趣事，妈妈忙前忙后地照顾着每一个人。</p>

      <h3>3. 平凡中的珍贵</h3>
      <p>这些平凡却珍贵的时光，构成了我们家庭最美好的记忆。家人在一起的时光总是过得特别快，但这份温暖会一直伴随着我们。无论走到哪里，家永远是我们心中最温暖的港湾。</p>

      <img src="${testImage3}" alt="家庭聚餐" style="max-width: 100%; margin: 20px 0;">
    `,
    author: {
      nickname: '家庭记录员小刚',
      avatar_url: avatarImage,
      introduction: '用心记录家庭的每一个温馨时刻'
    },
    likes: 289,
    isLiked: false,
    isFavorited: false
  },
  4: {
    title: '海边日落：与爱人的浪漫时光',
    content: `
      <h2>爱情最美的样子</h2>
      <p>在三亚的海边，我和她手牵手看着夕阳西下，海浪轻抚着沙滩，微风吹过发梢。那一刻，时间仿佛静止了，只有我们两个人和这片金色的海洋。</p>

      <h3>1. 海边的第一次相遇</h3>
      <p>我们在沙滩上留下足迹，在海边拾贝壳，看着远方的渔船慢慢归来。她的笑容比阳光还要灿烂，那份纯真和美好深深地打动了我。</p>

      <h3>2. 日落时分的浪漫</h3>
      <p>当太阳完全沉入海平面的那一刻，整个天空都被染成了橙红色，美得让人屏息。我们静静地坐在沙滩上，感受着彼此的温暖，那份宁静和美好让人永生难忘。</p>

      <h3>3. 爱情的真谛</h3>
      <p>这是我们第一次一起看海，也是我们爱情路上最浪漫的时刻。那份美好，那份感动，将永远珍藏在我们心中。爱情最美的样子，就是陪伴，就是在最美的时光里遇见最对的人。</p>

      <img src="${testImage4}" alt="海边落日" style="max-width: 100%; margin: 20px 0;">
    `,
    author: {
      nickname: '浪漫主义者小李',
      avatar_url: avatarImage,
      introduction: '相信爱情，记录生活中的每一个浪漫瞬间'
    },
    likes: 356,
    isLiked: false,
    isFavorited: false
  },
  5: {
    title: '登山日记：征服人生第一座高峰',
    content: `
      <h2>坚持就是胜利</h2>
      <p>经过6小时的艰难攀登，终于站在了泰山之巅。虽然双腿酸痛，但看到日出那一刻的壮丽景色，所有的疲惫都烟消云散。这次登山让我明白，坚持就是胜利。</p>

      <h3>1. 凌晨的出发</h3>
      <p>凌晨3点就开始登山，山路崎岖，夜色朦胧。头灯照亮前方的路，每一步都需要小心翼翼。虽然疲惫，但心中的目标让我们坚持前行。</p>

      <h3>2. 中途的挑战</h3>
      <p>爬到半山腰时，体力开始透支，双腿如灌铅般沉重。看着还有一半的路程，内心开始动摇。但想到山顶的日出，想到这次挑战的意义，我咬牙继续向上。</p>

      <h3>3. 登顶的喜悦</h3>
      <p>当第一缕阳光洒在山顶时，那种成就感无法言喻。站在泰山之巅，俯瞰群山，感受着"会当凌绝顶，一览众山小"的豪迈。这次登山不仅征服了高峰，更征服了自己。</p>

      <img src="${testImage5}" alt="泰山日出" style="max-width: 100%; margin: 20px 0;">
    `,
    author: {
      nickname: '户外爱好者小王',
      avatar_url: avatarImage,
      introduction: '热爱户外运动，用脚步丈量世界的美好'
    },
    likes: 198,
    isLiked: false,
    isFavorited: false
  }
};

// 示例评论数据
const sampleComments = {
  1: [
    {
      user_id: 101,
      nickname: '旅行爱好者',
      avatar_url: avatarImage,
      content: '看了你的分享，我也想去西藏了！那种心灵的震撼一定很难忘吧。'
    },
    {
      user_id: 102,
      nickname: '摄影师小李',
      avatar_url: avatarImage,
      content: '西藏真的是摄影师的天堂，每一帧都是大片！期待看到更多你的作品。'
    },
    {
      user_id: 103,
      nickname: '背包客阿强',
      avatar_url: avatarImage,
      content: '高原反应确实很难受，但为了那份美景，一切都值得！'
    }
  ],
  2: [
    {
      user_id: 104,
      nickname: '同窗好友',
      avatar_url: avatarImage,
      content: '看到这张合影眼泪都要出来了，那些美好的大学时光真的一去不复返了。'
    },
    {
      user_id: 105,
      nickname: '毕业生小张',
      avatar_url: avatarImage,
      content: '友谊万岁！愿我们都能在各自的道路上发光发热，友谊长存！'
    }
  ],
  3: [
    {
      user_id: 106,
      nickname: '温馨家庭',
      avatar_url: avatarImage,
      content: '家庭聚餐真的是最幸福的时光，看着就觉得很温暖。珍惜和家人在一起的每一刻。'
    },
    {
      user_id: 107,
      nickname: '孝顺儿子',
      avatar_url: avatarImage,
      content: '奶奶的手艺一定很棒！家的味道是世界上最美好的味道。'
    }
  ],
  4: [
    {
      user_id: 108,
      nickname: '浪漫情侣',
      avatar_url: avatarImage,
      content: '好羡慕你们的爱情！海边日落真的太浪漫了，爱情最美的样子就是陪伴。'
    },
    {
      user_id: 109,
      nickname: '单身狗小王',
      avatar_url: avatarImage,
      content: '看得我都想谈恋爱了，什么时候我也能有这样浪漫的时光呢？'
    }
  ],
  5: [
    {
      user_id: 110,
      nickname: '登山达人',
      avatar_url: avatarImage,
      content: '泰山日出确实震撼！登山不仅锻炼身体，更能磨练意志。坚持就是胜利！'
    },
    {
      user_id: 111,
      nickname: '户外新手',
      avatar_url: avatarImage,
      content: '看了你的分享很受鼓舞，我也想挑战一下自己，去征服人生第一座高峰！'
    }
  ]
};

onMounted(async () => {
  try {
    const docId = route.query.id;
    // 使用示例数据
    const article = sampleArticles[docId];
    if (article) {
      title.value = article.title;
      content.value = article.content;
      authorData.value = article.author;
      likes.value = article.likes;
      isLiked.value = article.isLiked;
      isFavorited.value = article.isFavorited;
      comments.value = sampleComments[docId] || [];
    } else {
      console.error("未找到对应的文章");
    }
  } catch (error) {
    console.error("加载文章失败", error);
  }
});

// 跳转作者主页
const gotoUser = (userId) => {
  console.log(userId)
  router.push({
    name: '个人主页',
    query: {
      id: userId
    }
  });
}

async function loadComments(docId) {
  try {
    const commentsRes = await getArticleComments(docId);
    comments.value = commentsRes.comments;
    await addUserInfoToComments();
    console.log(comments.value);
  } catch (error) {
    console.error("加载评论失败");
  }
}

const addUserInfoToComments = async () => {
  for (const comment of comments.value) {
    try {
      const userInfo = await GetUserById(comment.user_id);
      comment.nickname = userInfo.nickname;
      comment.avatar_url = userInfo.avatar_url;
    } catch (error) {
      console.error(`获取用户 ${comment.user_id} 信息失败`, error);
    }
  }
};

async function submitComment(docId, content) {
    const newComment = await postArticleComment(docId, content);
    console.log("成功提交评论:", newComment);
}


const addComment = async () => {
  if (newComment.value.trim()) {
    const docId = route.query.id;
    const content = newComment.value.trim();

    // 添加新评论到示例数据
    const newCommentObj = {
      user_id: 999, // 示例用户ID
      nickname: '当前用户',
      avatar_url: avatarImage,
      content: content
    };

    if (!sampleComments[docId]) {
      sampleComments[docId] = [];
    }
    sampleComments[docId].push(newCommentObj);

    // 更新显示
    comments.value = sampleComments[docId];
    newComment.value = '';
  }
};

// 返回上一页
const goBack = () => {
  // 优先返回上一页，如果没有历史记录则跳转到首页
  if (window.history.length > 1) {
    router.go(-1);
  } else {
    router.push('/index');
  }
};

</script>


<style scoped>
.page-container {
  padding: 20px;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.content-container {
  display: flex;
  flex-direction: row;
  gap: 20px;
  align-items: flex-start;
  justify-content: space-between;
}

.image-section img {
  max-width: 500px;
  width: 100%;
  height: auto;
  object-fit: cover;
}

.text-section {
  flex: 1;
  padding: 40px;
  background-color: #fff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  min-height: 60vh;
  height: auto;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  user-select: none;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}

.user-name {
  font-weight: bold;
}

.article-title {
  font-size: 1.5em;
  margin: 15px 0;
}

.article-content {
  font-size: 1em;
  margin: 15px 0;
  line-height: 1.6;
  max-width: 100%;
}

.article-content img {
  max-width: 100%;
  height: auto;
  margin: 20px 0;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.like-button,
.collect-button,
.unlike-button {
  display: flex;
  align-items: center;
  border: none;
  font-size: 1em;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  color: #555;
  transition: background-color 0.2s;
}

.like-button,
.collect-button {
  background-color: #f0f0f0;
}

.unlike-button {
  background-color: rgba(104, 143, 113, 0.74);
}

.like-button img,
.collect-button img {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}

.like-button:hover,
.collect-button:hover {
  background-color: #f1f1f1;
}

.like-button:hover img,
.collect-button:hover img {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

.comments-section {
  margin-top: 20px;
  max-height: 300px;
  overflow-y: auto;
}

.comments-section ul {
  list-style: none;
  padding: 0;
}

.comment-item {
  display: flex;
  margin-bottom: 10px;
  align-items: flex-start;
}

.comment-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 10px; /* 头像和用户名之间的间距 */
}

.comment-content {
  max-width: 500px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.comment-user-name {
  font-weight: bold;
}

.comment-text {
  margin-top: 5px;
  word-wrap: break-word;
}

.text-section input {
  width: 95%;
  padding: 8px;
  margin-top: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: transparent;
  color: #242424;
  outline: none;
}

.follow-button {
  margin-left: 15px;
  background-color: #000000;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.7em;
  transition: background-color 0.3s;
  outline: none;
}

.follow-button:hover {
  background-color: #252525;
}

.comments-section {
  max-height: 200px; /* 设置固定高度 */
  overflow-y: auto; /* 内容超出时产生垂直滚动条 */
  padding-right: 10px; /* 给滚动条腾出一点空间 */
}

.comments-section ul {
  list-style: none;
  padding: 0;
  margin: 10px 0;
  word-wrap: break-word; /* 允许长单词或URL在单词中断点换行 */
  overflow-wrap: break-word;
}

/* 美化滚动条 */
.comments-section::-webkit-scrollbar {
  width: 10px;
}

.comments-section::-webkit-scrollbar-thumb {
  background: white;
  border-radius: 10px;
}

.comments-section::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.back-button {
  position: fixed;
  top: 20px;
  left: 200px;  /* 从20px改为200px，向右移动 */
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 100;
}

.back-button:hover {
  background-color: #f0f0f0;
  transform: translateX(-2px);
}

.back-button svg {
  width: 20px;
  height: 20px;
}

.back-button span {
  font-size: 14px;
  color: #333;
}

.comments-title {
  font-size: 1.2em;
  margin-bottom: 15px;
  color: #333;
}

.comment-input-container {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 8px;
}

.comment-input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  outline: none;
  font-size: 14px;
}

.comment-submit-button {
  padding: 10px 20px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.comment-submit-button:hover {
  background-color: #45a049;
}

</style>
