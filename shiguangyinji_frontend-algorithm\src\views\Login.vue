<template>
  <div class="page-background">
    <div class="container">
      <div class="form-box" :style="{ transform: isRegistering ? 'translateX(80%)' : 'translateX(0%)' }"
        :class="{ 'register-bg': isRegistering, 'login-bg': !isRegistering }">
        <!-- 注册 -->
        <div class="register-box" :class="{ hidden: !isRegistering }">
          <h1>注册</h1>
          <div class="input-group">
            <img src="@/assets/icon/user.png" alt="用户名图标" class="input-icon" />
            <input type="text" placeholder="用户名" v-model="registerData.username" class="custom-input" />
          </div>
          <div v-if="validationErrors.username" class="error-message">{{ validationErrors.username }}</div>

          <div class="input-group">
            <img src="@/assets/icon/phone.png" alt="手机号图标" class="input-icon" />
            <input type="text" placeholder="手机号" v-model="registerData.phone" class="custom-input" />
          </div>
          <div v-if="validationErrors.phone" class="error-message">{{ validationErrors.phone }}</div>

          <div class="input-group">
            <img src="@/assets/icon/password.png" alt="密码图标" class="input-icon" />
            <input type="password" placeholder="密码" v-model="registerData.password" class="custom-input" />
          </div>
          <div v-if="validationErrors.password" class="error-message">{{ validationErrors.password }}</div>

          <div class="input-group">
            <img src="@/assets/icon/confirm.png" alt="确认密码图标" class="input-icon" />
            <input type="password" placeholder="确认密码" v-model="registerData.confirm_password" class="custom-input" />
          </div>
          <div v-if="validationErrors.confirm_password" class="error-message">{{ validationErrors.confirm_password }}</div>

          <button @click="register" :disabled="isLoading">
            <span v-if="isLoading" class="loading-spinner"></span>
            <span v-else>注册</span>
          </button>
        </div>
        <!-- 登录 -->
        <div class="login-box" :class="{ hidden: isRegistering }">
          <h1>登录</h1>
          <div class="input-group">
            <img src="@/assets/icon/user.png" alt="用户名图标" class="input-icon" />
            <input type="text" placeholder="用户名" v-model="loginData.username" />
          </div>
          <div v-if="validationErrors.loginUsername" class="error-message">{{ validationErrors.loginUsername }}</div>

          <div class="input-group">
            <img src="@/assets/icon/password.png" alt="密码图标" class="input-icon" />
            <input type="password" placeholder="密码" v-model="loginData.password" />
          </div>
          <div v-if="validationErrors.loginPassword" class="error-message">{{ validationErrors.loginPassword }}</div>

          <button @click="login" :disabled="isLoading">
            <span v-if="isLoading" class="loading-spinner"></span>
            <span v-else>登录</span>
          </button>
        </div>
      </div>

      <div class="con-box left">
        <h2>WELCOME</h2>
        <img src="@/assets/vue.svg" alt="" />
        <p>已有账号</p>
        <button @click="switchToLogin">去登录</button>
      </div>

      <div class="con-box right">
        <h2>WELCOME</h2>
        <img src="@/assets/vue.svg" alt="" />
        <p>没有账号？</p>
        <button @click="switchToRegister">去注册</button>
      </div>
    </div>
  </div>
</template>

<script>
import { Login, Register, CheckUsernameExist, CheckPhoneExist } from "@/api/api";

export default {
  data() {
    return {
      isRegistering: false,
      isLoading: false,
      loginData: {
        username: '',
        password: ''
      },
      registerData: {
        username: '',
        phone: '',
        password: '',
        confirm_password: '',
        name: '',
        birthday: '',
        gender: ''
      },
      validationErrors: {
        username: '',
        phone: '',
        password: '',
        confirm_password: '',
        loginUsername: '',
        loginPassword: ''
      }
    };
  },
  methods: {
    switchToLogin() {
      this.isRegistering = false;
      this.clearValidationErrors();
    },
    switchToRegister() {
      this.isRegistering = true;
      this.clearValidationErrors();
    },

    clearValidationErrors() {
      this.validationErrors = {
        username: '',
        phone: '',
        password: '',
        confirm_password: '',
        loginUsername: '',
        loginPassword: ''
      };
    },

    validatePassword(password) {
      if (!password) {
        return { valid: false, message: '请输入密码' };
      }

      if (password.length < 8) {
        return { valid: false, message: '密码长度至少为8位' };
      }

      // 检查密码复杂度
      const hasUpperCase = /[A-Z]/.test(password);
      const hasLowerCase = /[a-z]/.test(password);
      const hasNumbers = /\d/.test(password);
      const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

      let strength = 0;
      if (hasUpperCase) strength++;
      if (hasLowerCase) strength++;
      if (hasNumbers) strength++;
      if (hasSpecialChar) strength++;

      if (strength < 2) {
        return {
          valid: false,
          message: '密码强度不足，建议包含大小写字母、数字和特殊字符'
        };
      }

      return { valid: true };
    },

    async login() {
      // 清除之前的验证错误
      this.clearValidationErrors();

      // 验证用户名
      if (!this.loginData.username) {
        this.validationErrors.loginUsername = '请输入用户名';
        return;
      }

      // 验证密码
      if (!this.loginData.password) {
        this.validationErrors.loginPassword = '请输入密码';
        return;
      }

      try {
        this.isLoading = true;
        const response = await Login(this.loginData);
        console.log("登录成功:", response.data);

        // 适配
        localStorage.setItem('access_token', response.data.access);
        if (response.data.refresh) {
          localStorage.setItem('refresh_token', response.data.refresh);
        }

        this.$store.dispatch('login', response.data.access);
        this.$router.push('/index');
      } catch (error) {
        console.error("登录失败:", error.response?.data || error.message);
        const errorMessage = error.response?.data?.error || '用户名或密码错误';
        this.validationErrors.loginPassword = errorMessage;
      } finally {
        this.isLoading = false;
      }
    },

    async register() {
      // 清除之前的验证错误
      this.clearValidationErrors();

      // 验证用户名
      if (!this.registerData.username) {
        this.validationErrors.username = '请输入用户名';
        return;
      }

      // 验证手机号
      const phoneRegex = /^\d{11}$/;
      if (!this.registerData.phone) {
        this.validationErrors.phone = '请输入手机号';
        return;
      } else if (!phoneRegex.test(this.registerData.phone)) {
        this.validationErrors.phone = '手机号必须为11位数字';
        return;
      }

      // 验证密码
      const passwordValidation = this.validatePassword(this.registerData.password);
      if (!passwordValidation.valid) {
        this.validationErrors.password = passwordValidation.message;
        return;
      }

      // 验证确认密码
      if (this.registerData.password !== this.registerData.confirm_password) {
        this.validationErrors.confirm_password = '密码与确认密码不匹配';
        return;
      }

      try {
        this.isLoading = true;

        // 检查用户名是否存在
        const checkResponse = await CheckUsernameExist(this.registerData.username);
        if (checkResponse.exists) {
          this.validationErrors.username = '用户名已存在，请选择其他用户名';
          return;
        }

        // 检查手机号是否存在
        const phoneResponse = await CheckPhoneExist(this.registerData.phone);
        if (phoneResponse.exists) {
          this.validationErrors.phone = '手机号已被注册，请使用其他手机号';
          return;
        }

        // 提交注册
        const response = await Register({
          ...this.registerData
        });

        console.log("注册成功:", response);

        // 显示成功消息并切换到登录页面
        alert("注册成功，请登录");
        this.switchToLogin();
      } catch (error) {
        console.error("注册失败:", error.response?.data || error.message);
        const errorMessage = error.response?.data?.error || '注册失败，请稍后重试';
        alert(errorMessage);
      } finally {
        this.isLoading = false;
      }
    }
  }
};
</script>

<style scoped>
*{
  margin: 0;
  padding: 0;
}

.page-background {
  background-image: url('@/assets/images/background.jpg');
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;

  width: 100%;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  border-radius: 20px;
}

.container{
  background-color: #e0eee2;
  width: 90%;
  max-width: 650px;
  min-height: 415px;
  border-radius: 20px;
  box-shadow: 0 10px 25px rgba(0,0,0,0.15);
  position: relative;
  margin: 50px auto;
}

.form-box {
  position: absolute;
  top: -10%;
  left: 5%;
  /* background-color: #899a8c; */
  width: 320px;
  height: 500px;
  border-radius: 20px;
  box-shadow: 2px 0 10px rgba(0,0,0,0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
  /* 动画过渡 加速后减速 */
  transition: 0.5s ease-in-out;
  user-select: none;
}

.register-bg {
  background-image: url('@/assets/images/sea.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.login-bg {
  background-image: url('@/assets/images/mountain.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.register-box,.login-box{
  /* 弹性布局 垂直排列 */
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  user-select: none;
}
.hidden{
  display: none;
  transition: 0.5s;
}
h1{
  text-align: center;
  margin-bottom: 25px;
  text-transform: uppercase;
  color: #fff;
  letter-spacing: 5px;
  font-size: 2.5em;
  user-select: none;
}

input{
  background-color: transparent;
  width: 70%;
  color: #fff;
  border: none;
  /* 下边框样式 */
  border-bottom: 1px solid rgba(255,255,255,0.4);
  padding: 10px 0;
  text-indent: 10px;
  margin: 8px 0;
  font-size: 14px;
  letter-spacing: 2px;
}

input::placeholder{
  color: #fff;
}

input:focus{
  color: #444343;
  outline: none;
  border-bottom: 1px solid #444343;
  transition: 0.5s;
}

input:focus::placeholder{
  opacity: 0;
}

.form-box button{
  width: 70%;
  margin-top: 30px;
  background-color: #f6f6f6;
  outline: none;
  border-radius: 20px;
  padding: 13px;
  color: #536555;
  letter-spacing: 2px;
  border: none;
  cursor: pointer;
  position: relative;
}

.form-box button:hover:not(:disabled){
  background-color: #536555;
  color: #f6f6f6;
  transition: background-color 0.5s ease;
}

.form-box button:disabled {
  background-color: #cccccc;
  color: #888888;
  cursor: not-allowed;
}

.con-box{
  width: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  user-select: none;
}
.con-box.left{
  left: -2%;
}
.con-box.right{
  right: -2%;
}
.con-box h2{
  color: #8e9aaf;
  font-size: 25px;
  font-weight: bold;
  letter-spacing: 3px;
  text-align: center;
  margin-bottom: 4px;
  font-family: 'ShangGuB', sans-serif;
  user-select: none;
}
.con-box p{
  font-size: 12px;
  letter-spacing: 2px;
  color: #8e9aaf;
  text-align: center;
  user-select: none;
}
.con-box span{
  color: #536555;
  user-select: none;
}

.con-box img{
  width: 150px;
  height: 150px;
  opacity: 0.9;
  margin: 40px 0;
  user-select: none;
}

.con-box button{
  margin-top: 3%;
  background-color: #fff;
  color: #536555;
  border: 1px solid #536555;
  padding: 6px 10px;
  border-radius: 5px;
  letter-spacing: 1px;
  outline: none;
  cursor: pointer;
  user-select: none;
}
.con-box button:hover{
  background-color: #536555;
  color: #fff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    width: 95%;
  }

  .form-box {
    width: 90%;
    left: 5%;
    height: auto;
    padding: 30px 0;
  }

  .con-box {
    display: none;
  }

  h1 {
    font-size: 2em;
  }

  .form-box button {
    width: 80%;
  }
}

.forgot-password {
  color: #000000;
  cursor: pointer;
  text-decoration: underline;
  margin-top: 10px;
  font-size: 0.6em;
}

.input-group {
  display: flex;
  align-items: center;
  margin: 8px 0;
}

.input-icon {
  width: 30px;
  height: 30px;
  margin-right: 5px;
  object-fit: contain;
  filter: brightness(0.9);
}

/* 错误消息样式 */
.error-message {
  color: #ff4d4f;
  font-size: 12px;
  margin: 2px 0 8px 0;
  width: 70%;
  text-align: left;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 4px;
  backdrop-filter: blur(2px);
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #536555;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>
