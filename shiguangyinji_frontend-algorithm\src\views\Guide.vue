<template>
    <div class="full-page" @wheel.passive="handleScroll">
        <!-- 引导页 -->
        <section class="guide-container" v-show="currentPage === 0">
            <header class="header">欢迎来到时光日记</header>

            <main class="main">
                <p class="login-tip">记录美好，分享感动</p>
                <button v-if="!isLoggedIn" class="login-button" @click="goLogin">登录</button>
                <button v-else class="login-button" @click="goHome">进入应用</button>
            </main>

            <footer class="footer">
                <p class="description">{{ displayedText }}</p>
                <p v-if="!isLoggedIn">还没有账号？<a href="#" @click.prevent="goLogin">立即注册</a></p>
                <p v-else>欢迎回来！开始您的时光记录之旅</p>
            </footer>
        </section>

        <!-- 第二页 -->
        <section class="intro-container" v-show="currentPage === 1">
            <h1 class="intro-title">表达自我</h1>
            <div class="category-list">
                <div v-for="item in categories" :key="item.name" class="category-item">
                    <img :src="item.icon" class="icon" />
                    <div class="name">{{ item.name }}</div>
                </div>
            </div>
            <p class="tips">
                每一次表达都有人关注，每一个兴趣都能找到同好
            </p>
        </section>

        <!-- 第三页 -->
        <section class="feature-container" v-show="currentPage === 2">
            <h1 class="feature-title">轻松记录回忆</h1>
            <div class="feature-list">
                <div class="feature-item">
                    <img src="@/assets/icon/photo.svg" class="icon" />
                    <p>AI 图片处理</p>
                </div>
                <div class="feature-item">
                    <img src="@/assets/icon/news.svg" class="icon" />
                    <p>AI 文章生成</p>
                </div>
                <div class="feature-item">
                    <img src="@/assets/icon/movie.svg" class="icon" />
                    <p>AI 视频创作</p>
                </div>
            </div>
            <p class="feature-tips">只需一键，轻松记录、编辑、生成属于你的专属回忆</p>
        </section>
    </div>
</template>


<script setup>
import { onMounted, ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

const router = useRouter()
const store = useStore()
const currentPage = ref(0)

// 检查用户是否已登录
const isLoggedIn = computed(() => store.getters.isLoggedIn)
const categories = [
    { name: '科技', icon: new URL('@/assets/icon/microscope.svg', import.meta.url).href },
    { name: '运动', icon: new URL('@/assets/icon/ping-pong.svg', import.meta.url).href },
    { name: '健康', icon: new URL('@/assets/icon/first-aid-kit.svg', import.meta.url).href },
    { name: '旅游', icon: new URL('@/assets/icon/plane-tilt.svg', import.meta.url).href },
    { name: '美食', icon: new URL('@/assets/icon/cheese.svg', import.meta.url).href },
    { name: '感情', icon: new URL('@/assets/icon/hearts.svg', import.meta.url).href },
    { name: '艺术', icon: new URL('@/assets/icon/brush.svg', import.meta.url).href },
    { name: '文学', icon: new URL('@/assets/icon/writing.svg', import.meta.url).href },
    { name: '摄影', icon: new URL('@/assets/icon/camera.svg', import.meta.url).href },
]

function goLogin() {
    router.push('/login')
}

function goHome() {
    router.push('/chinamap')
}

function handleScroll(e) {
  if (e.deltaY > 20 && currentPage.value < 2) {
    currentPage.value++
  } else if (e.deltaY < -20 && currentPage.value > 0) {
    currentPage.value--
  }
}


// 打字机逻辑
const fullText = '时光日记 是一款帮助您记录生活点点滴滴的应用，您可以随时随地记录心情、照片和想法。'
const displayedText = ref('')

onMounted(async () => {
    // 初始化登录状态
    if (!store.getters.isInitialized) {
        try {
            await store.dispatch('initLoginState');
        } catch (error) {
            console.error('初始化登录状态失败:', error);
        }
    }

    // 打字机效果
    let index = 0
    const speed = 120

    function type() {
        if (index <= fullText.length) {
            displayedText.value = fullText.slice(0, index)
            index++
            setTimeout(type, speed)
        }
    }

    type()
})
</script>


<style scoped>
* {
    user-select: none;
}

.full-page {
    height: 100vh;
    overflow: hidden;
    scroll-behavior: smooth;
    transition: transform 0.6s ease;
}

.guide-container::before {
    content: '';
    position: absolute;
    inset: 0;
    background-color: rgba(255, 255, 255, 0.35);
    z-index: 0;
}

.guide-container>* {
    position: relative;
    z-index: 1;
}

.guide-container {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 95vh;
    text-align: center;
    color: #333;
    background-image: url('@/assets/images/sea.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;

    border-radius: 30px;
    overflow: hidden;
    margin: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    animation: fadeIn 1s ease forwards;
    opacity: 0;
    transform: translateY(20px);
}

@keyframes fadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.header,
.main,
.footer {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeUp 0.8s ease forwards;
}

.header {
    animation-delay: 0.3s;
}

.main {
    animation-delay: 0.6s;
}

.footer {
    animation-delay: 0.9s;
}

@keyframes fadeUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}


.header {
    font-family: 'ChangErFeiBai', sans-serif;
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    font-size: 3.0rem;
    padding: 2rem 1rem 1rem;
    font-weight: bold;
    color: #000;
}

.main {
    flex: 2;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.login-tip {
    font-family: 'ChangErFeiBai', sans-serif;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    /* color: white; */
}

.login-button {
    padding: 0.6rem 1.5rem;
    font-size: 1rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: 0.3s;
}

.login-button:hover {
    background-color: var(--primary-dark);
}

.footer {
    flex: 1;
    padding: 0.5rem;
    font-size: 0.9rem;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    background-color: #7a93a0;
    border-top: 1px solid #7a93a0;
    color: #333;
}

.footer a {
    color: #ffffff;
    text-decoration: none;
}

.description {
    margin-top: 2rem;
    color: #333;
    max-width: 520px;
    font-size: 1.2em;
    margin-bottom: 1.5rem;
    user-select: text;

}

/* 第二页 */
.intro-container {
    height: 95vh;
    background: linear-gradient(to bottom right, #fefefe, #e9f0ff);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.6s ease;

    border-radius: 30px;
    overflow: hidden;
    margin: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    background-image: url('@/assets/backgrounds/phone.jpeg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.intro-container::before {
    content: '';
    position: absolute;
    inset: 0;
    background-color: rgba(255, 255, 255, 0.5);
    z-index: 0;
}

.intro-container>* {
    position: relative;
    z-index: 1;
}

.intro-title,
.category-list,
.tips {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeUp 0.8s ease forwards;
}

.intro-title {
    animation-delay: 0.3s;
}

.category-list {
    animation-delay: 0.6s;
}

.tips {
    animation-delay: 0.9s;
}

@keyframes fadeUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.intro-title {
    font-family: 'ChangErFeiBai', sans-serif;
    font-size: 3.0rem;
    margin-bottom: 1.5rem;
    font-weight: bold;
    color: #222;
}

.category-list {
    margin-top: 1rem;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
}

.category-item {
    padding: 0.6rem 1.2rem;
    /* background-color: #409eff; */
    color: #333;
    border-radius: 20px;
    font-size: 1rem;
    transition: transform 0.3s;
}

.category-item:hover {
    transform: scale(1.2);
}

.icon {
    width: 32px;
    height: 32px;
    /* margin-bottom: 0.5rem; */
}

.name {
    font-size: 1rem;
    text-align: center;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.tips {
    margin-top: 2.5rem;
    color: #424242;
    max-width: 520px;
    font-size: 1.3em;
    margin-bottom: 1.5rem;
    user-select: text;
}

/* 第三页 */
.feature-container {
    height: 95vh;
    background: linear-gradient(to bottom right, #fffefc, #e4f7ff);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.6s ease;

    border-radius: 30px;
    overflow: hidden;
    margin: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    background-image: url('@/assets/backgrounds/ai.jpeg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
}

.feature-container::before {
    content: '';
    position: absolute;
    inset: 0;
    background-color: rgba(255, 255, 255, 0.5);
    z-index: 0;
}

.feature-container > * {
    position: relative;
    z-index: 1;
}

.feature-title {
    font-family: 'ChangErFeiBai', sans-serif;
    font-size: 3rem;
    font-weight: bold;
    color: #222;
    margin-bottom: 1.5rem;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeUp 0.8s ease forwards;
    animation-delay: 0.3s;
}

.feature-list {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 2rem;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeUp 0.8s ease forwards;
    animation-delay: 0.6s;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 1.1rem;
    color: #333;
    padding: 1rem;
    border-radius: 1rem;
    transition: transform 0.3s;
    cursor: default;
}

.feature-item p {
    color: #333;
}

.feature-item:hover {
    transform: scale(1.1);
}

.feature-tips {
    font-size: 1.2rem;
    color: #444;
    max-width: 600px;
    text-align: center;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeUp 0.8s ease forwards;
    animation-delay: 0.9s;
}
</style>
