<script setup>
import { onMounted, ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';

const router = useRouter();
const store = useStore();

import {
  GetUserProfile,
  GetUserAvatar,
  GetUserCharacter,
  CreateUserCharacter,
  UpdateUserCharacter,
  UpdateUserProfile
} from "@/api/api.js";

// 页面状态
const isCompleteMode = ref(true); // 是否为信息补全模式
const showWelcome = ref(true); // 是否显示欢迎提示

let isEditing = ref(false);
let isLoading = ref(false);
let errorMessage = ref('');
let successMessage = ref('');

// 用户基本信息 (来自user表)
const userInfo = ref({
  username: '',
  email: '',
  phone: '',
  nickname: '',
  introduction: '',
  avatar: '@/assets/example/user.png'
});

// 用户角色信息 (来自character表)
const characterInfo = ref({
  realName: '',
  gender: '',
  birthday: '',
  realAvatar: null,
  realAvatarPreview: '@/assets/example/user.png'
});

// 编辑时的用户信息
const editUserInfo = ref({
  // user表字段
  nickname: '',
  introduction: '',
  phone: '',
  avatar: null,
  avatarPreview: '@/assets/example/user.png',

  // character表字段 (必填)
  realName: '',
  gender: '',
  birthday: '',
  realAvatar: null,
  realAvatarPreview: '@/assets/example/user.png'
});

// 表单验证错误
const formErrors = ref({
  nickname: '',
  introduction: '',
  phone: '',
  avatar: '',
  realName: '',
  gender: '',
  birthday: '',
  realAvatar: ''
});

// 性别选项
const genderOptions = [
  { value: '', label: '请选择性别' },
  { value: 'male', label: '男' },
  { value: 'female', label: '女' }
];

// 检查是否需要补全信息
const needsCompletion = computed(() => {
  return !characterInfo.value.realName ||
         !characterInfo.value.gender ||
         !characterInfo.value.realAvatar;
});

// 关闭欢迎提示
const closeWelcome = () => {
  showWelcome.value = false;
};

onMounted(async () => {
  await loadUserProfile();
});

// 加载用户资料
const loadUserProfile = async () => {
  try {
    isLoading.value = true;
    errorMessage.value = '';

    // 获取用户基本资料 (user表)
    const profileData = await GetUserProfile();
    console.log('获取到的用户资料:', profileData); // 调试信息

    userInfo.value.username = profileData.username || '';
    userInfo.value.email = profileData.email || '';
    userInfo.value.phone = profileData.phone || '';
    userInfo.value.nickname = profileData.nickname || '';
    userInfo.value.introduction = profileData.introduction || '';

    console.log('设置后的用户信息:', userInfo.value); // 调试信息

    // 获取用户头像
    try {
      const avatarData = await GetUserAvatar();
      userInfo.value.avatar = avatarData.avatar_url || '@/assets/example/user.png';
    } catch (avatarError) {
      console.log("获取头像失败，使用默认头像");
      userInfo.value.avatar = '@/assets/example/user.png';
    }

    // 获取character表中的用户角色信息
    try {
      const characterResponse = await GetUserCharacter();
      if (characterResponse.status === 'success') {
        const characterData = characterResponse.data;
        characterInfo.value.realName = characterData.name || '';
        characterInfo.value.gender = characterData.gender || '';
        characterInfo.value.birthday = characterData.birthday || '';
        characterInfo.value.realAvatar = characterData.avatar || null;
        characterInfo.value.realAvatarPreview = characterData.avatar || '@/assets/example/user.png';

        // 如果有完整的角色信息，则不是补全模式
        if (characterData.name && characterData.gender && characterData.avatar) {
          isCompleteMode.value = false;
          showWelcome.value = false;
        }
      }
    } catch (characterError) {
      console.log("用户角色信息不存在，需要补全");
      // 保持默认值和补全模式
      characterInfo.value.realName = '';
      characterInfo.value.gender = '';
      characterInfo.value.birthday = '';
      characterInfo.value.realAvatar = null;
      characterInfo.value.realAvatarPreview = '@/assets/example/user.png';
      isCompleteMode.value = true;
      showWelcome.value = true;
    }

    // 更新 Vuex 存储中的用户信息
    store.dispatch('updateUserInfo', {
      nickname: userInfo.value.nickname,
      avatar: userInfo.value.avatar,
      isAdmin: profileData.is_staff || profileData.is_superuser
    });

  } catch (error) {
    console.error("加载用户资料失败:", error);
    errorMessage.value = "加载用户资料失败，请检查网络连接";
    setTimeout(() => { errorMessage.value = ''; }, 3000);
  } finally {
    isLoading.value = false;
  }
};


// 开始编辑用户信息
const startEdit = () => {
  isEditing.value = true;

  // 复制当前用户信息到编辑表单
  // user表字段
  editUserInfo.value.nickname = userInfo.value.nickname;
  editUserInfo.value.introduction = userInfo.value.introduction;
  editUserInfo.value.phone = userInfo.value.phone;
  editUserInfo.value.avatar = null;
  editUserInfo.value.avatarPreview = userInfo.value.avatar;

  // character表字段
  editUserInfo.value.realName = characterInfo.value.realName;
  editUserInfo.value.gender = characterInfo.value.gender;
  editUserInfo.value.birthday = characterInfo.value.birthday;
  editUserInfo.value.realAvatar = null;
  editUserInfo.value.realAvatarPreview = characterInfo.value.realAvatarPreview;

  // 清除之前的错误
  clearFormErrors();
};

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false;
  clearFormErrors();
};

// 清除表单错误
const clearFormErrors = () => {
  formErrors.value = {
    nickname: '',
    introduction: '',
    phone: '',
    avatar: '',
    realName: '',
    gender: '',
    birthday: '',
    realAvatar: ''
  };
};

// 处理用户头像文件选择
const handleAvatarChange = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // 验证文件类型
  if (!file.type.match('image.*')) {
    formErrors.value.avatar = '请选择图片文件';
    return;
  }

  // 验证文件大小（最大2MB）
  if (file.size > 2 * 1024 * 1024) {
    formErrors.value.avatar = '图片大小不能超过2MB';
    return;
  }

  // 清除错误
  formErrors.value.avatar = '';

  // 设置文件和预览
  editUserInfo.value.avatar = file;

  // 创建预览
  const reader = new FileReader();
  reader.onload = (e) => {
    editUserInfo.value.avatarPreview = e.target.result;
  };
  reader.readAsDataURL(file);
};

// 处理真人头像文件选择
const handleRealAvatarChange = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // 验证文件类型
  if (!file.type.match('image.*')) {
    formErrors.value.realAvatar = '请选择图片文件';
    return;
  }

  // 验证文件大小（最大5MB）
  if (file.size > 5 * 1024 * 1024) {
    formErrors.value.realAvatar = '图片大小不能超过5MB';
    return;
  }

  // 清除错误
  formErrors.value.realAvatar = '';

  // 设置文件和预览
  editUserInfo.value.realAvatar = file;

  // 创建预览
  const reader = new FileReader();
  reader.onload = (e) => {
    editUserInfo.value.realAvatarPreview = e.target.result;
  };
  reader.readAsDataURL(file);
};

// 验证表单
const validateForm = () => {
  let isValid = true;
  clearFormErrors();

  // 验证昵称（可选）
  if (editUserInfo.value.nickname && editUserInfo.value.nickname.length > 20) {
    formErrors.value.nickname = '昵称不能超过20个字符';
    isValid = false;
  }

  // 验证个性签名（可选）
  if (editUserInfo.value.introduction && editUserInfo.value.introduction.length > 200) {
    formErrors.value.introduction = '个性签名不能超过200个字符';
    isValid = false;
  }

  // 验证手机号（可选）
  if (editUserInfo.value.phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(editUserInfo.value.phone)) {
      formErrors.value.phone = '请输入正确的手机号格式';
      isValid = false;
    }
  }

  // 验证真实姓名（必填）
  if (!editUserInfo.value.realName.trim()) {
    formErrors.value.realName = '请输入真实姓名';
    isValid = false;
  } else if (editUserInfo.value.realName.length > 10) {
    formErrors.value.realName = '真实姓名不能超过10个字符';
    isValid = false;
  }

  // 验证性别（必填）
  if (!editUserInfo.value.gender) {
    formErrors.value.gender = '请选择性别';
    isValid = false;
  }

  // 验证真人头像（必填）
  if (!editUserInfo.value.realAvatar && !characterInfo.value.realAvatar) {
    formErrors.value.realAvatar = '请上传真人头像';
    isValid = false;
  }

  // 验证生日格式（可选）
  if (editUserInfo.value.birthday) {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(editUserInfo.value.birthday)) {
      formErrors.value.birthday = '请输入正确的日期格式';
      isValid = false;
    } else {
      const date = new Date(editUserInfo.value.birthday);
      const today = new Date();
      if (date > today) {
        formErrors.value.birthday = '生日不能是未来日期';
        isValid = false;
      }
    }
  }

  return isValid;
};

// 保存用户信息
const saveUserInfo = async () => {
  // 验证表单
  if (!validateForm()) {
    return;
  }

  try {
    isLoading.value = true;
    errorMessage.value = '';
    successMessage.value = '';

    // 构建用户信息FormData
    const userFormData = new FormData();

    // 添加文本字段（包括空字符串）
    userFormData.append('nickname', editUserInfo.value.nickname || '');
    userFormData.append('introduction', editUserInfo.value.introduction || '');
    userFormData.append('phone', editUserInfo.value.phone || '');

    // 只有选择了新头像才添加
    if (editUserInfo.value.avatar) {
      userFormData.append('avatar', editUserInfo.value.avatar);
    }

    console.log('构建的用户FormData:');
    for (let [key, value] of userFormData.entries()) {
      console.log(key, ':', value);
    }

    console.log('编辑表单数据:', {
      nickname: editUserInfo.value.nickname,
      introduction: editUserInfo.value.introduction,
      phone: editUserInfo.value.phone,
      avatar: editUserInfo.value.avatar ? 'File selected' : 'No file'
    });

    // 构建角色信息FormData
    const characterFormData = new FormData();
    characterFormData.append('name', editUserInfo.value.realName);
    characterFormData.append('gender', editUserInfo.value.gender);
    if (editUserInfo.value.birthday) {
      characterFormData.append('birthday', editUserInfo.value.birthday);
    }
    if (editUserInfo.value.realAvatar) {
      characterFormData.append('avatar', editUserInfo.value.realAvatar);
    }

    // 保存用户基本信息
    let userUpdateSuccess = false;
    try {
      const userResponse = await UpdateUserProfile(userFormData);
      console.log('用户基本信息更新成功:', userResponse);
      userUpdateSuccess = true;
    } catch (userError) {
      console.error('用户基本信息更新失败:', userError);
      console.error('错误详情:', userError.response?.data);
      console.error('错误状态:', userError.response?.status);
      console.error('完整错误对象:', userError);
      // 用户信息更新失败，但不阻止角色信息更新
      const errorMsg = userError.response?.data?.message ||
                      userError.response?.data?.error ||
                      JSON.stringify(userError.response?.data) ||
                      userError.message;
      errorMessage.value = `用户信息更新失败: ${errorMsg}`;
    }

    // 保存角色信息
    let characterResponse;
    if (characterInfo.value.realName) {
      // 更新现有角色信息
      characterResponse = await UpdateUserCharacter(characterFormData);
    } else {
      // 创建新的角色信息
      characterResponse = await CreateUserCharacter(characterFormData);
    }

    // 处理响应
    if (characterResponse.status === 'success') {
      successMessage.value = "个人信息保存成功！人脸数据已提取。";
    } else if (characterResponse.status === 'warning') {
      successMessage.value = characterResponse.message;
    }

    // 更新本地数据
    userInfo.value.nickname = editUserInfo.value.nickname || userInfo.value.nickname;
    userInfo.value.introduction = editUserInfo.value.introduction || userInfo.value.introduction;
    userInfo.value.phone = editUserInfo.value.phone || userInfo.value.phone;

    characterInfo.value.realName = editUserInfo.value.realName;
    characterInfo.value.gender = editUserInfo.value.gender;
    characterInfo.value.birthday = editUserInfo.value.birthday || characterInfo.value.birthday;

    if (editUserInfo.value.avatar) {
      userInfo.value.avatar = editUserInfo.value.avatarPreview;
    }
    if (editUserInfo.value.realAvatar) {
      characterInfo.value.realAvatar = editUserInfo.value.realAvatarPreview;
    }

    // 如果是补全模式且必填项都已填写，切换到正常模式
    if (isCompleteMode.value && characterInfo.value.realName &&
        characterInfo.value.gender && characterInfo.value.realAvatar) {
      isCompleteMode.value = false;
      showWelcome.value = false;
    }

    // 更新 Vuex 存储中的用户信息
    store.dispatch('updateUserInfo', {
      nickname: userInfo.value.nickname,
      avatar: userInfo.value.avatar
    });

    setTimeout(() => { successMessage.value = ''; }, 5000);
    isEditing.value = false;
  } catch (error) {
    console.error("保存个人信息失败:", error);
    errorMessage.value = "保存失败，请稍后再试";
    setTimeout(() => { errorMessage.value = ''; }, 3000);
  } finally {
    isLoading.value = false;
  }
};

// 获取性别显示文本
const getGenderText = (gender) => {
  const option = genderOptions.find(opt => opt.value === gender);
  return option ? option.label : '未设置';
};

</script>



<template>
  <div class="user-profile-container">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>加载中...</p>
    </div>

    <!-- 欢迎提示 -->
    <div v-if="showWelcome && isCompleteMode" class="welcome-banner">
      <div class="welcome-content">
        <div class="welcome-icon">🎉</div>
        <div class="welcome-text">
          <h2>欢迎来到时光印记！</h2>
          <p>为了更好地为您服务，请先完善您的个人信息。带 <span class="required">*</span> 的为必填项。</p>
        </div>
        <button @click="closeWelcome" class="close-welcome">×</button>
      </div>
    </div>

    <!-- 错误消息 -->
    <div v-if="errorMessage" class="message-box error">
      <p>{{ errorMessage }}</p>
    </div>

    <!-- 成功消息 -->
    <div v-if="successMessage" class="message-box success">
      <p>{{ successMessage }}</p>
    </div>

    <!-- 个人信息展示区域 -->
    <div class="profile-content">
      <!-- 头像和基本信息 -->
      <div class="profile-header">
        <div class="avatar-section">
          <img :src="userInfo.avatar" alt="用户头像" class="user-avatar"/>
        </div>
        <div class="basic-info">
          <h1 class="username">{{ userInfo.username || '用户' }}</h1>
          <p class="nickname">{{ userInfo.nickname || '未设置昵称' }}</p>
          <p class="email">{{ userInfo.email || '未设置邮箱' }}</p>
        </div>
        <div class="action-buttons">
          <button @click="startEdit" class="edit-btn">
            <span class="btn-icon">✏️</span>
            <span>{{ isCompleteMode ? '完善信息' : '编辑资料' }}</span>
          </button>
        </div>
      </div>

      <!-- 详细信息卡片 -->
      <div class="info-cards">
        <!-- 用户基本信息卡片 -->
        <div class="info-card">
          <div class="card-header">
            <h3>👤 账户信息</h3>
          </div>
          <div class="card-content">
            <div class="info-item">
              <label>用户名：</label>
              <span>{{ userInfo.username || '未设置' }}</span>
            </div>
            <div class="info-item">
              <label>邮箱：</label>
              <span>{{ userInfo.email || '未设置' }}</span>
            </div>
            <div class="info-item">
              <label>手机号：</label>
              <span>{{ userInfo.phone || '未设置' }}</span>
            </div>
            <div class="info-item">
              <label>昵称：</label>
              <span>{{ userInfo.nickname || '未设置' }}</span>
            </div>
            <div class="info-item">
              <label>个性签名：</label>
              <span>{{ userInfo.introduction || '未设置' }}</span>
            </div>
          </div>
        </div>

        <!-- 真实身份信息卡片 -->
        <div class="info-card">
          <div class="card-header">
            <h3>🆔 真实身份信息</h3>
            <span class="required-note">完善后可使用人脸识别功能</span>
          </div>
          <div class="card-content">
            <div class="info-item">
              <label>真实姓名：<span class="required">*</span></label>
              <span>{{ characterInfo.realName || '未设置' }}</span>
            </div>
            <div class="info-item">
              <label>性别：<span class="required">*</span></label>
              <span>{{ getGenderText(characterInfo.gender) }}</span>
            </div>
            <div class="info-item">
              <label>生日：</label>
              <span>{{ characterInfo.birthday || '未设置' }}</span>
            </div>
            <div class="info-item">
              <label>真人头像：<span class="required">*</span></label>
              <span>{{ characterInfo.realAvatar ? '已上传' : '未上传' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 编辑模态框 -->
  <div class="modal" v-if="isEditing">
    <div class="modal-content">
      <div class="modal-header">
        <h2>✏️ {{ isCompleteMode ? '完善个人信息' : '编辑个人资料' }}</h2>
        <button @click="cancelEdit" class="close-btn">×</button>
      </div>

      <form @submit.prevent="saveUserInfo" class="edit-form">
        <!-- 账户信息区域 -->
        <div class="form-section">
          <h3 class="section-title">👤 账户信息</h3>

          <!-- 用户头像编辑 -->
          <div class="form-group avatar-group">
            <label>用户头像</label>
            <div class="avatar-edit-section">
              <div class="avatar-preview">
                <img :src="editUserInfo.avatarPreview" alt="头像预览" class="preview-image"/>
              </div>
              <div class="avatar-upload">
                <input type="file" id="avatar-input" @change="handleAvatarChange" accept="image/*" class="file-input"/>
                <label for="avatar-input" class="upload-btn">
                  <span class="btn-icon">📷</span>
                  <span>选择头像</span>
                </label>
                <p class="upload-hint">支持 JPG、PNG 格式，不超过 2MB</p>
              </div>
            </div>
            <div v-if="formErrors.avatar" class="form-error">{{ formErrors.avatar }}</div>
          </div>

          <!-- 基本信息编辑 -->
          <div class="form-row">
            <div class="form-group">
              <label for="nickname">昵称</label>
              <input
                v-model="editUserInfo.nickname"
                id="nickname"
                type="text"
                maxlength="20"
                placeholder="请输入昵称"
                class="form-input"
              />
              <div v-if="formErrors.nickname" class="form-error">{{ formErrors.nickname }}</div>
            </div>

            <div class="form-group">
              <label for="phone">手机号</label>
              <input
                v-model="editUserInfo.phone"
                id="phone"
                type="tel"
                maxlength="11"
                placeholder="请输入手机号"
                class="form-input"
              />
              <div v-if="formErrors.phone" class="form-error">{{ formErrors.phone }}</div>
            </div>
          </div>

          <div class="form-group">
            <label for="introduction">个性签名</label>
            <textarea
              v-model="editUserInfo.introduction"
              id="introduction"
              maxlength="200"
              placeholder="写点什么介绍一下自己吧..."
              class="form-textarea"
              rows="3"
            ></textarea>
            <div v-if="formErrors.introduction" class="form-error">{{ formErrors.introduction }}</div>
          </div>
        </div>

        <!-- 真实身份信息区域 -->
        <div class="form-section">
          <h3 class="section-title">🆔 真实身份信息 <span class="required-note">（用于人脸识别功能）</span></h3>

          <!-- 真人头像编辑 -->
          <div class="form-group avatar-group">
            <label>真人头像 <span class="required">*</span></label>
            <div class="avatar-edit-section">
              <div class="avatar-preview">
                <img :src="editUserInfo.realAvatarPreview" alt="真人头像预览" class="preview-image"/>
              </div>
              <div class="avatar-upload">
                <input type="file" id="real-avatar-input" @change="handleRealAvatarChange" accept="image/*" class="file-input"/>
                <label for="real-avatar-input" class="upload-btn">
                  <span class="btn-icon">📷</span>
                  <span>选择真人头像</span>
                </label>
                <p class="upload-hint">请上传清晰的正面照片，支持 JPG、PNG 格式，不超过 5MB</p>
              </div>
            </div>
            <div v-if="formErrors.realAvatar" class="form-error">{{ formErrors.realAvatar }}</div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="realName">真实姓名 <span class="required">*</span></label>
              <input
                v-model="editUserInfo.realName"
                id="realName"
                type="text"
                maxlength="10"
                placeholder="请输入真实姓名"
                class="form-input"
              />
              <div v-if="formErrors.realName" class="form-error">{{ formErrors.realName }}</div>
            </div>

            <div class="form-group">
              <label for="gender">性别 <span class="required">*</span></label>
              <select v-model="editUserInfo.gender" id="gender" class="form-select">
                <option v-for="option in genderOptions" :key="option.value" :value="option.value">
                  {{ option.label }}
                </option>
              </select>
              <div v-if="formErrors.gender" class="form-error">{{ formErrors.gender }}</div>
            </div>
          </div>

          <div class="form-group">
            <label for="birthday">生日</label>
            <input
              v-model="editUserInfo.birthday"
              id="birthday"
              type="date"
              class="form-input"
            />
            <div v-if="formErrors.birthday" class="form-error">{{ formErrors.birthday }}</div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <button type="button" @click="cancelEdit" class="btn-cancel">
            <span class="btn-icon">❌</span>
            <span>取消</span>
          </button>
          <button type="submit" :disabled="isLoading" class="btn-save">
            <span v-if="isLoading" class="loading-spinner-small"></span>
            <span v-else class="btn-icon">💾</span>
            <span v-if="!isLoading">{{ isCompleteMode ? '完成设置' : '保存' }}</span>
          </button>
        </div>
      </form>
    </div>
  </div>

</template>

<style scoped>
/* 主容器样式 */
.user-profile-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20px;
  position: relative;
}

/* 欢迎横幅样式 */
.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  overflow: hidden;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.welcome-content {
  display: flex;
  align-items: center;
  padding: 20px 30px;
  color: white;
  position: relative;
}

.welcome-icon {
  font-size: 3em;
  margin-right: 20px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.welcome-text h2 {
  margin: 0 0 8px 0;
  font-size: 1.5em;
  font-weight: 600;
}

.welcome-text p {
  margin: 0;
  font-size: 1em;
  opacity: 0.9;
  line-height: 1.5;
}

.close-welcome {
  position: absolute;
  top: 15px;
  right: 20px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 1.5em;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-welcome:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.profile-content {
  max-width: 1200px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(94, 96, 132, 0.37);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  overflow: hidden;
}

/* 头部区域 */
.profile-header {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
  padding: 40px;
  display: flex;
  align-items: center;
  gap: 30px;
  flex-wrap: wrap;
}

.avatar-section {
  flex-shrink: 0;
}

.user-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
}

.basic-info {
  flex: 1;
  min-width: 200px;
}

.username {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nickname {
  font-size: 18px;
  opacity: 0.9;
  margin: 0;
  font-weight: 300;
}

.email {
  font-size: 14px;
  opacity: 0.8;
  margin: 5px 0 0 0;
  font-weight: 300;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.edit-btn, .admin-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  font-size: 14px;
  white-space: nowrap;
}

.edit-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.edit-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.admin-btn {
  background: rgba(255, 193, 7, 0.9);
  color: #333;
}

.admin-btn:hover {
  background: rgba(255, 193, 7, 1);
  transform: translateY(-2px);
}

.btn-icon {
  font-size: 16px;
}

/* 信息卡片区域 */
.info-cards {
  padding: 40px;
}

.info-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e8e8e8;
}

.card-header {
  background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20px 30px;
  border-bottom: 1px solid #e8e8e8;
}

.card-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: space-between;
  width: 100%;
}

.required-note {
  font-size: 0.8em;
  color: #666;
  font-weight: normal;
  opacity: 0.8;
}

.card-content {
  padding: 30px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item label {
  font-weight: 600;
  color: #555;
  width: 100px;
  flex-shrink: 0;
}

.info-item span {
  color: #333;
  flex: 1;
}

/* 编辑模态框 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: white;
  border-radius: 20px;
  width: 600px;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
  padding: 25px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 20px 20px 0 0;
}

.modal-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 28px;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.edit-form {
  padding: 30px;
}

/* 表单区域样式 */
.form-section {
  margin-bottom: 40px;
  padding: 25px;
  background: #f8f9fa;
  border-radius: 15px;
  border: 1px solid #e9ecef;
}

.section-title {
  margin: 0 0 25px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 10px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

/* 文本域样式 */
.form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  font-size: 14px;
  transition: all 0.3s ease;
  box-sizing: border-box;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.form-textarea:focus {
  outline: none;
  border-color: #6c757d;
  box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.1);
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
  font-size: 14px;
}

.required {
  color: #e53935;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-input, .form-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  font-size: 14px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus, .form-select:focus {
  outline: none;
  border-color: #6c757d;
  box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.1);
}

.form-error {
  color: #e53935;
  font-size: 12px;
  margin-top: 5px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.form-error::before {
  content: "⚠️";
  font-size: 12px;
}

/* 头像编辑区域 */
.avatar-group {
  margin-bottom: 30px;
}

.avatar-edit-section {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-top: 10px;
}

.avatar-preview {
  flex-shrink: 0;
}

.preview-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #e0e0e0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.avatar-upload {
  flex: 1;
}

.file-input {
  display: none;
}

.upload-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  font-size: 14px;
}

.upload-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
}

.upload-hint {
  font-size: 12px;
  color: #666;
  margin-top: 8px;
  margin-bottom: 0;
}

/* 操作按钮 */
.form-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.btn-cancel, .btn-save {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  border: none;
}

.btn-cancel {
  background: #f5f5f5;
  color: #666;
  border: 2px solid #e0e0e0;
}

.btn-cancel:hover {
  background: #e8e8e8;
  border-color: #ccc;
}

.btn-save {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
  position: relative;
}

.btn-save:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
}

.btn-save:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(108, 117, 125, 0.2);
  border-radius: 50%;
  border-top-color: #6c757d;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loading-spinner-small {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-overlay p {
  color: #6c757d;
  font-size: 16px;
  font-weight: 500;
}

/* 消息框 */
.message-box {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 16px 24px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.message-box p {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.message-box.error {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  border-left: 4px solid #e53935;
  color: #c62828;
}

.message-box.success {
  background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
  border-left: 4px solid #43a047;
  color: #2e7d32;
}

@keyframes slideIn {
  from {
    transform: translateX(100%) scale(0.8);
    opacity: 0;
  }
  to {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-header {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .action-buttons {
    flex-direction: row;
    justify-content: center;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .avatar-edit-section {
    flex-direction: column;
    text-align: center;
  }

  .modal-content {
    width: 95vw;
    margin: 10px;
  }

  .username {
    font-size: 24px;
  }

  .info-cards {
    padding: 20px;
  }
}
</style>
