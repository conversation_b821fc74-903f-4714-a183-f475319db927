import json
import pdb


def load_geojson(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        return json.load(file)


def extract_prov_city_dict(prov_geojson_data, city_geojson_data):
    prov_dict = {}
    city_dict = {}

    # 建立省份 adcode -> name 映射
    for feature in prov_geojson_data['features']:
        properties = feature['properties']
        adcode = properties.get('adcode')
        name = properties.get('name')
        if adcode and name:
            prov_dict[adcode] = name

    # 遍历城市数据，按 parent 找到所属省份
    for feature in city_geojson_data['features']:
        properties = feature['properties']
        city_name = properties.get('name')
        parent_adcode = properties.get('parent', {}).get('adcode')

        if parent_adcode in prov_dict and city_name:
            province_name = prov_dict[parent_adcode]
            if province_name not in city_dict:
                city_dict[province_name] = []
            if city_name not in city_dict[province_name]:
                city_dict[province_name].append(city_name)

    return city_dict


# 主程序部分
prov_geojson_file_path = 'E:/postgraduate/project/shiguangyinji/shiguangyinji_frontend-algorithm/src/assets/map/province.json'
city_geojson_file_path = 'E:/postgraduate/project/shiguangyinji/shiguangyinji_frontend-algorithm/src/assets/map/city.json'

prov_geojson_data = load_geojson(prov_geojson_file_path)
city_geojson_data = load_geojson(city_geojson_file_path)

prov_city_dict = extract_prov_city_dict(prov_geojson_data, city_geojson_data)

# 保存为 JSON 文件
output_file = 'E:/postgraduate/project/shiguangyinji/shiguangyinji_frontend-algorithm/src/assets/map/province_city_mapping.json'
with open(output_file, 'w', encoding='utf-8') as f:
    json.dump(prov_city_dict, f, ensure_ascii=False, indent=4)

print(f"省份与城市映射已保存至 {output_file}")