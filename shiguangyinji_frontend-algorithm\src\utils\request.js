import axios from 'axios'
import router from '@/router' // 确保引入你的 vue-router 实例

const service = axios.create({
  baseURL: 'http://127.0.0.1:8000/api/',
  timeout: 100000
})

function isAuthApi(url = '') {
  return (
    url.includes('/api/token/') || // 登录
    url.includes('usermanage/register') || // 注册
    url.includes('usermanage/login') || // 登录
    url.includes('usermanage/check-username') || // 检查用户名
    url.includes('usermanage/check-phone') || // 检查手机号
    url.includes('/api/token/refresh/') // 刷新
  )
}

// 请求拦截器：自动加 access_token
service.interceptors.request.use(
  config => {
    if (!isAuthApi(config.url)) {
      const token = localStorage.getItem('access_token')
      //console.log('token', token)
      if (token) {
        config.headers['Authorization'] = `Bearer ${token}`
      }
    }
    return config
  },
  error => Promise.reject(error)
)

// 响应拦截器：自动刷新 token
let isRefreshing = false
let requests = []

service.interceptors.response.use(
  response => {
    // 检查响应中是否包含警告信息
    if (response.data && response.data.warnings) {
      console.warn('后端警告:', response.data.warnings)
    }

    // 即使有警告，只要状态码是成功的，就返回响应
    return response
  },
  async error => {
    const originalRequest = error.config
    // 判断是否 access token 过期（401 且未重试过）
    if (
      error.response &&
      error.response.status === 401 &&
      !originalRequest._retry
    ) {
      originalRequest._retry = true
      // 避免多次刷新
      if (!isRefreshing) {
        isRefreshing = true
        const refreshToken = localStorage.getItem('refresh_token')
        if (refreshToken) {
          try {
            // 调用后端刷新接口（以 DRF SimpleJWT 为例）
            const res = await axios.post('http://127.0.0.1:8000/api/token/refresh/', {
              refresh: refreshToken
            })
            const newAccess = res.data.access
            localStorage.setItem('access_token', newAccess)
            service.defaults.headers['Authorization'] = `Bearer ${newAccess}`
            isRefreshing = false
            // 重新执行所有等待的请求
            requests.forEach(cb => cb(newAccess))
            requests = []
            // 重新发起原请求
            originalRequest.headers['Authorization'] = `Bearer ${newAccess}`
            return service(originalRequest)
          } catch (refreshError) {
            // refresh 也失败，跳转引导页
            localStorage.removeItem('access_token')
            localStorage.removeItem('refresh_token')
            isRefreshing = false
            requests = []
            router.push('/guide')
            return Promise.reject(refreshError)
          }
        } else {
          // 没有 refresh token，跳转引导页
          router.push('/guide')
        }
      } else {
        // 正在刷新，返回一个 Promise 等待刷新完成
        return new Promise(resolve => {
          requests.push(token => {
            originalRequest.headers['Authorization'] = `Bearer ${token}`
            resolve(service(originalRequest))
          })
        })
      }
    }

    // 增强错误处理：区分不同类型的错误
    if (error.response) {
      // 服务器返回了错误状态码
      const { status, data } = error.response

      // 处理特定的错误状态码
      switch (status) {
        case 400:
          console.warn('请求参数错误:', data)
          break
        case 403:
          console.warn('权限不足:', data)
          break
        case 404:
          console.warn('资源不存在:', data)
          break
        case 500:
          console.error('服务器内部错误:', data)
          break
        default:
          console.warn(`HTTP ${status} 错误:`, data)
      }

      // 如果响应包含警告但不是致命错误，可以选择不抛出异常
      if (data && data.warnings && !data.error) {
        console.warn('后端警告（非致命）:', data.warnings)
        // 返回一个包含警告的成功响应
        return Promise.resolve({
          data: data,
          status: status,
          statusText: error.response.statusText,
          headers: error.response.headers,
          config: error.config,
          request: error.request
        })
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      console.error('网络错误或服务器无响应:', error.request)
    } else {
      // 其他错误
      console.error('请求配置错误:', error.message)
    }

    return Promise.reject(error)
  }
)

export default service