// 前端异步视频生成功能示例
// 适用于Vue.js或React等前端框架

class VideoGenerationManager {
    constructor(baseURL = '/api/video') {
        this.baseURL = baseURL;
        this.currentVideoId = null;
        this.statusCheckInterval = null;
        this.onStatusUpdate = null;
        this.onComplete = null;
        this.onError = null;
    }

    /**
     * 开始生成视频
     * @param {Array} pictures - 图片ID列表
     * @param {string} title - 视频标题
     * @param {string} description - 视频描述
     * @param {number} videoId - 视频ID（覆盖时提供）
     */
    async startGeneration(pictures, title = '', description = '', videoId = null) {
        try {
            const requestData = {
                pictures: pictures,
                title: title,
                description: description
            };

            if (videoId) {
                requestData.video_id = videoId;
            }

            const response = await fetch(this.baseURL + '/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + this.getToken()
                },
                body: JSON.stringify(requestData)
            });

            const result = await response.json();

            if (response.ok) {
                this.currentVideoId = result.video_id;
                console.log('视频生成任务已启动:', result);
                
                // 开始轮询状态
                this.startStatusPolling();
                
                return {
                    success: true,
                    videoId: result.video_id,
                    message: result.message
                };
            } else {
                throw new Error(result.error || '启动视频生成失败');
            }

        } catch (error) {
            console.error('启动视频生成时出错:', error);
            if (this.onError) {
                this.onError(error.message);
            }
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 开始状态轮询
     */
    startStatusPolling() {
        if (this.statusCheckInterval) {
            clearInterval(this.statusCheckInterval);
        }

        this.statusCheckInterval = setInterval(async () => {
            await this.checkStatus();
        }, 2000); // 每2秒检查一次状态
    }

    /**
     * 停止状态轮询
     */
    stopStatusPolling() {
        if (this.statusCheckInterval) {
            clearInterval(this.statusCheckInterval);
            this.statusCheckInterval = null;
        }
    }

    /**
     * 检查视频生成状态
     */
    async checkStatus() {
        if (!this.currentVideoId) return;

        try {
            const response = await fetch(
                `${this.baseURL}/status/?video_id=${this.currentVideoId}`,
                {
                    headers: {
                        'Authorization': 'Bearer ' + this.getToken()
                    }
                }
            );

            const result = await response.json();

            if (response.ok) {
                console.log('视频状态:', result);

                // 触发状态更新回调
                if (this.onStatusUpdate) {
                    this.onStatusUpdate(result);
                }

                // 检查是否完成
                if (result.status === 'completed') {
                    this.stopStatusPolling();
                    if (this.onComplete) {
                        this.onComplete(result);
                    }
                } else if (result.status === 'failed') {
                    this.stopStatusPolling();
                    if (this.onError) {
                        this.onError(result.error_message || '视频生成失败');
                    }
                }

            } else {
                console.error('检查状态失败:', result);
            }

        } catch (error) {
            console.error('检查状态时出错:', error);
        }
    }

    /**
     * 保存视频（用户确认满意后调用）
     */
    async saveVideo(videoId = null) {
        const targetVideoId = videoId || this.currentVideoId;
        if (!targetVideoId) {
            throw new Error('没有可保存的视频');
        }

        try {
            const response = await fetch(this.baseURL + '/save/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + this.getToken()
                },
                body: JSON.stringify({
                    video_id: targetVideoId
                })
            });

            const result = await response.json();

            if (response.ok) {
                console.log('视频保存成功:', result);
                return {
                    success: true,
                    message: result.message,
                    videoUrl: result.video_url
                };
            } else {
                throw new Error(result.error || '保存视频失败');
            }

        } catch (error) {
            console.error('保存视频时出错:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 获取认证token
     */
    getToken() {
        // 这里需要根据您的认证方式来实现
        return localStorage.getItem('authToken') || '';
    }

    /**
     * 设置状态更新回调
     */
    setOnStatusUpdate(callback) {
        this.onStatusUpdate = callback;
    }

    /**
     * 设置完成回调
     */
    setOnComplete(callback) {
        this.onComplete = callback;
    }

    /**
     * 设置错误回调
     */
    setOnError(callback) {
        this.onError = callback;
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.stopStatusPolling();
        this.currentVideoId = null;
        this.onStatusUpdate = null;
        this.onComplete = null;
        this.onError = null;
    }
}

// Vue.js 组件使用示例
const VideoGenerationComponent = {
    data() {
        return {
            videoManager: new VideoGenerationManager(),
            isGenerating: false,
            progress: 0,
            status: '',
            statusMessage: '',
            videoUrl: null,
            selectedPictures: [1, 2, 3], // 示例图片ID
            videoTitle: '我的回忆',
            videoDescription: '美好的时光'
        };
    },

    mounted() {
        // 设置回调函数
        this.videoManager.setOnStatusUpdate((status) => {
            this.progress = status.progress;
            this.status = status.status;
            this.statusMessage = this.getStatusMessage(status.status, status.progress);
        });

        this.videoManager.setOnComplete((result) => {
            this.isGenerating = false;
            this.videoUrl = result.video_url;
            this.statusMessage = '视频生成完成！';
        });

        this.videoManager.setOnError((error) => {
            this.isGenerating = false;
            this.statusMessage = `生成失败: ${error}`;
        });
    },

    methods: {
        async startVideoGeneration() {
            this.isGenerating = true;
            this.progress = 0;
            this.videoUrl = null;
            this.statusMessage = '正在努力制作轨迹视频...';

            const result = await this.videoManager.startGeneration(
                this.selectedPictures,
                this.videoTitle,
                this.videoDescription
            );

            if (!result.success) {
                this.isGenerating = false;
                this.statusMessage = `启动失败: ${result.error}`;
            }
        },

        async regenerateVideo() {
            // 重新生成（覆盖现有视频）
            if (this.videoManager.currentVideoId) {
                await this.startVideoGeneration();
            }
        },

        async saveVideo() {
            const result = await this.videoManager.saveVideo();
            if (result.success) {
                this.statusMessage = '视频已保存！';
                // 这里可以刷新页面或跳转到其他页面
                this.$router.push('/video-list');
            } else {
                this.statusMessage = `保存失败: ${result.error}`;
            }
        },

        getStatusMessage(status, progress) {
            const messages = {
                'pending': '等待开始...',
                'processing': `正在努力制作轨迹视频... ${progress}%`,
                'completed': '视频生成完成！',
                'failed': '生成失败'
            };
            return messages[status] || '未知状态';
        }
    },

    beforeUnmount() {
        // 清理资源
        this.videoManager.cleanup();
    }
};

// 使用说明
console.log(`
异步视频生成功能使用说明：

1. 用户点击"生成视频"按钮
   - 调用 startVideoGeneration()
   - 显示"正在努力制作轨迹视频"

2. 系统自动轮询状态
   - 每2秒检查一次进度
   - 更新进度条和状态信息

3. 用户可以切换页面
   - 轮询会继续进行
   - 回到页面时状态保持同步

4. 视频生成完成
   - 显示预览
   - 用户可以选择保存或重新生成

5. 用户点击保存
   - 调用 saveVideo()
   - 刷新页面或跳转

API接口：
- POST /api/video/ - 启动视频生成
- GET /api/video/status/?video_id=123 - 查询状态
- POST /api/video/save/ - 保存视频
`);
