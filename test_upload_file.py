#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的 upload_file 函数
"""

import sys
import os
import tempfile
sys.path.append('shiguangyinji_backend-algorithm')

from utils.edit import upload_file

def create_test_file():
    """创建一个测试文件"""
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write("这是一个测试文件\n")
        f.write(f"创建时间: {os.path.getmtime(f.name)}\n")
        f.write("用于测试Gitee文件覆盖功能\n")
        return f.name

def test_upload_file():
    """测试upload_file函数"""
    print("开始测试 upload_file 函数...")
    print("=" * 50)
    
    # 创建测试文件
    test_file_path = create_test_file()
    print(f"✓ 创建测试文件: {test_file_path}")
    
    try:
        # 第一次上传（创建文件）
        print("\n1. 第一次上传（创建文件）:")
        file_name = "test/upload_test.txt"
        result_url = upload_file(file_name, test_file_path)
        
        if result_url:
            print(f"✓ 文件上传成功，URL: {result_url}")
        else:
            print("✗ 文件上传失败")
            return False
        
        # 修改测试文件内容
        with open(test_file_path, 'a', encoding='utf-8') as f:
            f.write(f"更新时间: {os.path.getmtime(test_file_path)}\n")
            f.write("这是更新后的内容\n")
        
        # 第二次上传（覆盖文件）
        print("\n2. 第二次上传（覆盖文件）:")
        result_url2 = upload_file(file_name, test_file_path)
        
        if result_url2:
            print(f"✓ 文件覆盖成功，URL: {result_url2}")
        else:
            print("✗ 文件覆盖失败")
            return False
        
        # 验证URL是否相同
        if result_url == result_url2:
            print("✓ 两次上传返回的URL相同，符合预期")
        else:
            print("⚠ 两次上传返回的URL不同")
            print(f"  第一次: {result_url}")
            print(f"  第二次: {result_url2}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        return False
    
    finally:
        # 清理测试文件
        try:
            os.unlink(test_file_path)
            print(f"\n✓ 清理测试文件: {test_file_path}")
        except:
            pass

def test_error_handling():
    """测试错误处理"""
    print("\n测试错误处理...")
    print("=" * 30)
    
    # 测试不存在的文件
    try:
        result = upload_file("test/nonexistent.txt", "/path/to/nonexistent/file.txt")
        if result is None:
            print("✓ 正确处理了不存在的文件")
        else:
            print("✗ 未正确处理不存在的文件")
    except Exception as e:
        print(f"✓ 正确抛出异常: {e}")

if __name__ == "__main__":
    print("Gitee 文件上传覆盖功能测试")
    print("=" * 60)
    
    # 主要功能测试
    success = test_upload_file()
    
    # 错误处理测试
    test_error_handling()
    
    print("\n" + "=" * 60)
    if success:
        print("✓ 测试通过！upload_file 函数支持文件覆盖功能。")
        print("\n主要改进:")
        print("- ✓ 自动检测文件是否已存在")
        print("- ✓ 获取现有文件的SHA值")
        print("- ✓ 使用PUT请求进行创建或覆盖")
        print("- ✓ 返回文件的访问URL")
        print("- ✓ 改进的错误处理和日志输出")
    else:
        print("✗ 测试失败！请检查 upload_file 函数。")
    
    print("\n使用说明:")
    print("1. 确保您的Gitee access_token有效")
    print("2. 确保仓库 'lin-lingwww/resource' 存在且有写权限")
    print("3. 现在可以放心使用，不用担心重复文件问题")
