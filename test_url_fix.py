#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试URL修复
"""

import requests
import json

# 测试配置
BASE_URL = "http://localhost:8000"

def test_url_endpoints():
    """测试所有视频相关的URL端点"""
    print("测试视频API URL端点...")
    print("=" * 50)
    
    # 测试的端点
    endpoints = [
        ("POST", f"{BASE_URL}/api/video/", "新的主要端点"),
        ("POST", f"{BASE_URL}/api/video/create/", "兼容的create端点"),
        ("GET", f"{BASE_URL}/api/video/status/", "状态查询端点"),
        ("POST", f"{BASE_URL}/api/video/save/", "保存确认端点"),
    ]
    
    headers = {
        'Content-Type': 'application/json',
        # 注意：这里需要替换为实际的认证token
        'Authorization': 'Bearer your_token_here'
    }
    
    test_data = {
        'pictures': [1, 2, 3],
        'title': 'URL测试视频',
        'description': '测试URL修复功能'
    }
    
    for method, url, description in endpoints:
        print(f"\n测试: {method} {url}")
        print(f"描述: {description}")
        
        try:
            if method == "POST":
                if "status" in url:
                    # 状态查询需要GET方法
                    response = requests.get(url + "?video_id=1", headers=headers)
                elif "save" in url:
                    # 保存端点
                    response = requests.post(url, json={'video_id': 1}, headers=headers)
                else:
                    # 视频生成端点
                    response = requests.post(url, json=test_data, headers=headers)
            else:
                response = requests.get(url, headers=headers)
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 404:
                print("❌ URL不存在 - 需要检查路由配置")
            elif response.status_code == 401:
                print("✅ URL存在但需要认证 - 路由配置正确")
            elif response.status_code == 400:
                print("✅ URL存在但参数错误 - 路由配置正确")
            elif response.status_code in [200, 201, 202]:
                print("✅ URL正常工作")
            else:
                print(f"⚠️  其他状态码: {response.status_code}")
                
            # 尝试解析响应
            try:
                result = response.json()
                if 'error' in result:
                    print(f"错误信息: {result['error']}")
                else:
                    print("响应正常")
            except:
                print("响应不是JSON格式")
                
        except requests.exceptions.ConnectionError:
            print("❌ 连接失败 - 请确保Django服务器正在运行")
        except Exception as e:
            print(f"❌ 测试失败: {e}")

def show_current_url_structure():
    """显示当前的URL结构"""
    print("\n当前URL结构:")
    print("=" * 50)
    print("主项目URL配置 (shiguangyinji/urls.py):")
    print("  path('api/video/', include('video.urls'))")
    print()
    print("Video应用URL配置 (video/urls.py):")
    print("  path('', VideoManagement.as_view())           → /api/video/")
    print("  path('create/', VideoManagement.as_view())    → /api/video/create/")
    print("  path('status/', VideoStatusView.as_view())    → /api/video/status/")
    print("  path('save/', VideoSaveView.as_view())        → /api/video/save/")
    print()
    print("可用的完整URL:")
    print("  POST   /api/video/         - 视频生成 (新)")
    print("  POST   /api/video/create/  - 视频生成 (兼容旧版)")
    print("  GET    /api/video/         - 获取视频列表")
    print("  GET    /api/video/status/  - 查询视频状态")
    print("  POST   /api/video/save/    - 保存视频")
    print("  DELETE /api/video/         - 删除视频")

def show_migration_guide():
    """显示前端迁移指南"""
    print("\n前端迁移指南:")
    print("=" * 50)
    print("旧的前端调用:")
    print("  POST /api/video/create/  ← 这个仍然可以使用")
    print()
    print("推荐的新调用:")
    print("  POST /api/video/         ← 推荐使用这个")
    print()
    print("新增的异步功能:")
    print("  GET  /api/video/status/?video_id=123  ← 查询生成状态")
    print("  POST /api/video/save/                 ← 用户确认保存")
    print()
    print("前端代码示例:")
    print("""
// 旧的同步方式 (仍然支持)
const response = await fetch('/api/video/create/', {
    method: 'POST',
    body: JSON.stringify({pictures: [1,2,3]})
});

// 新的异步方式 (推荐)
const response = await fetch('/api/video/', {
    method: 'POST', 
    body: JSON.stringify({pictures: [1,2,3]})
});
// 然后轮询状态
const status = await fetch('/api/video/status/?video_id=123');
""")

if __name__ == "__main__":
    print("视频API URL修复测试")
    print("=" * 60)
    
    # 显示URL结构
    show_current_url_structure()
    
    # 测试URL端点
    test_url_endpoints()
    
    # 显示迁移指南
    show_migration_guide()
    
    print("\n" + "=" * 60)
    print("修复总结:")
    print("✅ 添加了 /api/video/create/ 路径以保持向后兼容")
    print("✅ 保留了 /api/video/ 作为主要端点")
    print("✅ 新增了异步功能相关的端点")
    print("✅ 前端代码无需立即修改，可以继续使用旧路径")
    print()
    print("建议:")
    print("1. 当前可以继续使用 /api/video/create/")
    print("2. 逐步迁移到新的异步API")
    print("3. 最终统一使用 /api/video/ 端点")
