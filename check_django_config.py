#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Django配置
"""

import sys
import os

# 设置Django环境
project_dir = 'shiguangyinji_backend-algorithm'
sys.path.insert(0, project_dir)
os.chdir(project_dir)  # 切换到项目目录，这样secrets.yml路径就正确了
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shiguangyinji.settings')

def check_django_setup():
    """检查Django基本设置"""
    print("检查Django配置...")
    print("=" * 50)

    try:
        import django
        print(f"✅ Django版本: {django.get_version()}")

        # 设置Django
        django.setup()
        print("✅ Django设置成功")

        # 检查设置
        from django.conf import settings
        print(f"✅ 项目名称: {settings.ROOT_URLCONF}")
        print(f"✅ 数据库: {settings.DATABASES['default']['ENGINE']}")

        return True

    except ImportError as e:
        print(f"❌ Django导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ Django设置失败: {e}")
        return False

def check_video_app():
    """检查video应用配置"""
    print("\n检查video应用...")
    print("=" * 50)

    try:
        # 检查模型
        from video.models import Video, VideoPicture
        print("✅ Video模型导入成功")
        print("✅ VideoPicture模型导入成功")

        # 检查视图
        from video.views import VideoManagement, VideoStatusView, VideoSaveView
        print("✅ VideoManagement视图导入成功")
        print("✅ VideoStatusView视图导入成功")
        print("✅ VideoSaveView视图导入成功")

        # 检查任务模块
        from video.tasks import video_task_manager
        print("✅ 任务管理器导入成功")

        return True

    except ImportError as e:
        print(f"❌ video应用导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ video应用检查失败: {e}")
        return False

def check_url_patterns():
    """检查URL配置"""
    print("\n检查URL配置...")
    print("=" * 50)

    try:
        from django.urls import reverse

        # 测试URL反向解析
        urls_to_test = [
            'video_management',
            'video_create',
            'video_status',
            'video_save'
        ]

        for url_name in urls_to_test:
            try:
                url = reverse(url_name)
                print(f"✅ {url_name}: {url}")
            except Exception as e:
                print(f"❌ {url_name}: {e}")

        return True

    except Exception as e:
        print(f"❌ URL配置检查失败: {e}")
        return False

def check_dependencies():
    """检查依赖项"""
    print("\n检查依赖项...")
    print("=" * 50)

    dependencies = [
        'django',
        'djangorestframework',
        'djangorestframework_simplejwt',
        'requests',
        'pillow',
    ]

    missing_deps = []

    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep}")
        except ImportError:
            print(f"❌ {dep} - 未安装")
            missing_deps.append(dep)

    if missing_deps:
        print(f"\n缺少依赖项: {', '.join(missing_deps)}")
        print("请运行: pip install " + " ".join(missing_deps))
        return False

    return True

def check_model_fields():
    """检查模型字段"""
    print("\n检查Video模型字段...")
    print("=" * 50)

    try:
        from video.models import Video

        # 检查新添加的字段
        required_fields = [
            'title', 'description', 'event_title',
            'status', 'progress', 'error_message',
            'started_at', 'completed_at'
        ]

        model_fields = [field.name for field in Video._meta.fields]

        for field in required_fields:
            if field in model_fields:
                print(f"✅ {field} 字段存在")
            else:
                print(f"❌ {field} 字段缺失")

        # 检查状态选择
        status_field = Video._meta.get_field('status')
        if hasattr(status_field, 'choices') and status_field.choices:
            print("✅ status字段有choices配置")
            for choice in status_field.choices:
                print(f"   - {choice[0]}: {choice[1]}")
        else:
            print("❌ status字段缺少choices配置")

        return True

    except Exception as e:
        print(f"❌ 模型字段检查失败: {e}")
        return False

def run_django_check():
    """运行Django内置检查"""
    print("\n运行Django内置检查...")
    print("=" * 50)

    try:
        from django.core.management import execute_from_command_line

        # 模拟运行 manage.py check
        import io
        import contextlib

        # 捕获输出
        output = io.StringIO()
        with contextlib.redirect_stdout(output), contextlib.redirect_stderr(output):
            try:
                execute_from_command_line(['manage.py', 'check'])
                result = output.getvalue()
                if result.strip():
                    print("Django检查输出:")
                    print(result)
                else:
                    print("✅ Django检查通过，没有发现问题")
                return True
            except SystemExit as e:
                if e.code == 0:
                    print("✅ Django检查通过")
                    return True
                else:
                    print(f"❌ Django检查失败，退出码: {e.code}")
                    result = output.getvalue()
                    if result:
                        print("错误输出:")
                        print(result)
                    return False

    except Exception as e:
        print(f"❌ 运行Django检查时出错: {e}")
        return False

if __name__ == "__main__":
    print("Django项目配置检查")
    print("=" * 60)

    all_checks_passed = True

    # 运行各项检查
    checks = [
        ("Django基本配置", check_django_setup),
        ("依赖项检查", check_dependencies),
        ("Video应用检查", check_video_app),
        ("URL配置检查", check_url_patterns),
        ("模型字段检查", check_model_fields),
        ("Django内置检查", run_django_check),
    ]

    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            if not check_func():
                all_checks_passed = False
        except Exception as e:
            print(f"❌ {check_name}执行失败: {e}")
            all_checks_passed = False

    print("\n" + "=" * 60)
    if all_checks_passed:
        print("✅ 所有检查通过！项目配置正常")
        print("\n可以尝试启动服务器:")
        print("python manage.py runserver")
    else:
        print("❌ 发现配置问题，请根据上述提示进行修复")

    print("\n如果需要应用数据库迁移:")
    print("python manage.py makemigrations")
    print("python manage.py migrate")
