import { createRouter, createWebHashHistory } from "vue-router";
import store from '@/store/store.js';

const routes = [
    {
        path: '/',
        redirect: '/guide'
    },
    {
        path: '/guide',
        name: '引导页',
        component: () => import('@/views/Guide.vue'),
        meta: { requiresAuth: false }
    },
    {
        path: '/login',
        name: '登录',
        component: () => import('@/views/Login.vue'),
        meta: { requiresAuth: false, hideForLoggedIn: true }
    },
    {
        path: '/',
        component: () => import('@/views/View.vue'),
        children: [
            {
                path: 'index',
                name: '首页',
                component: () => import('@/views/HomePage.vue'),
                meta: { requiresAuth: false }
            },
            {
                path: 'editor',
                name: '文档编辑器',
                component: () => import('@/views/EditorPage.vue'),
                meta: { requiresAuth: true }
            },
            {
                path: 'user',
                name: '个人主页',
                component: () => import('@/views/UserPage.vue'),
                meta: { requiresAuth: true }
            },
            {
                path: 'admin',
                name: '管理员',
                component: () => import('@/views/AdminPage.vue'),
                meta: { requiresAuth: true, requiresAdmin: true }
            },
            {
                path: 'message',
                name: '通知',
                component: () => import('@/views/Message.vue'),
                meta: { requiresAuth: true }
            },
            {
                path: 'groups',
                name: '兴趣组',
                component: () => import('@/views/GroupsPage.vue'),
                meta: { requiresAuth: true }
            },
            {
                path: 'agent',
                name: '智能写作',
                component: () => import('@/views/AgentPage.vue'),
                meta: { requiresAuth: true }
            },
            {
                path: 'doc',
                name: '文章页面',
                component: () => import('@/views/ArticlePage.vue'),
                meta: { requiresAuth: true }
            },
            {
                path: 'map',
                name: '地图',
                component: () => import('@/views/MapPage.vue'),
                meta: { requiresAuth: false }
            },
            {
                path: 'chinamap',
                name: '我的足迹',
                component: () => import('@/views/ChinaMapView.vue'),
                meta: { requiresAuth: false }
            },
            {
                path: 'uploadevent',
                name: '上传事件',
                component: () => import('@/views/UploadEventPage.vue'),
                meta: { requiresAuth: true }
            },
            {
                path: 'eventgallery',
                name: '事件列表',
                component: () => import('@/views/EventGallery.vue'),
                meta: { requiresAuth: true }
            },
            {
                path: '/event/:id',
                name: 'event-detail',
                component: () => import('@/views/EventDetailPage.vue'),
                meta: { requiresAuth: true }
            },
            {
                path: '/make-video',
                name: 'make-video',
                component: () => import('@/views/MakeVideoPage.vue'),
                meta: { requiresAuth: true }
            },
            {
                path: '/video-gallery',
                name: 'video-gallery',
                component: () => import('@/views/VideoGallery.vue'),
                meta: { requiresAuth: true }
            },
            {
                path: '/friends',
                name: '好友',
                component: () => import('@/views/FriendsPage.vue'),
                meta: { requiresAuth: true }
            },
            {
                path: '/event-photos/:id',
                name: 'EventPhotos',
                component: () => import('@/views/EventPhotosPage.vue'),
                meta: { requiresAuth: true }
            },
        ]
    }
];

const router = createRouter({
    history: createWebHashHistory(),
    routes
});

// 全局前置守卫
router.beforeEach(async (to, _from, next) => {
    // 检查是否需要登录
    const requiresAuth = to.meta.requiresAuth;
    const hideForLoggedIn = to.meta.hideForLoggedIn;
    const requiresAdmin = to.meta.requiresAdmin;

    // 确保store已经初始化
    if (!store.getters.isInitialized) {
        try {
            await store.dispatch('initLoginState');
        } catch (error) {
            console.error('初始化登录状态失败:', error);
        }
    }

    // 获取登录状态
    const isLoggedIn = store.getters.isLoggedIn;
    const token = localStorage.getItem('access_token');

    console.log('路由守卫检查:', {
        to: to.path,
        requiresAuth,
        hideForLoggedIn,
        requiresAdmin,
        isLoggedIn,
        hasToken: !!token,
        isInitialized: store.getters.isInitialized
    });

    // 如果页面需要登录但用户未登录
    if (requiresAuth && !isLoggedIn) {
        console.log('需要登录，重定向到登录页');
        next('/login');
        return;
    }

    // 如果用户已登录但访问登录页，重定向到首页
    if (hideForLoggedIn && isLoggedIn) {
        console.log('已登录用户访问登录页，重定向到首页');
        next('/chinamap');
        return;
    }

    // 如果需要管理员权限
    if (requiresAdmin && !store.getters.isAdmin) {
        console.log('需要管理员权限，拒绝访问');
        next('/chinamap');
        return;
    }

    next();
});

export default router