import { createStore } from 'vuex';

const store = createStore({
    state: {
        checkLogin: false,
        isInitialized: false, // 添加初始化状态标记
        userInfo: {
            username: '',
            nickname: '',
            avatar: '',
            isAdmin: false
        }
    },
    mutations: {
        setLoginState(state, isLoggedIn) {
            state.checkLogin = isLoggedIn;
        },
        setInitialized(state, isInitialized) {
            state.isInitialized = isInitialized;
        },
        setUserInfo(state, userInfo) {
            state.userInfo = { ...state.userInfo, ...userInfo };
        },
        clearUserInfo(state) {
            state.userInfo = {
                username: '',
                nickname: '',
                avatar: '',
                isAdmin: false
            };
            state.isInitialized = false;
        }
    },
    actions: {
        login({ commit }, token) {
            // 保存 token 到 localStorage（与request.js保持一致）
            localStorage.setItem('access_token', token);
            commit('setLoginState', true);
        },
        logout({ commit }) {
            // 清除所有 token
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');

            // 更新状态
            commit('setLoginState', false);
            commit('clearUserInfo');
        },
        // 更新用户信息
        updateUserInfo({ commit }, userInfo) {
            commit('setUserInfo', userInfo);
        },
        // 初始化登录状态
        async initLoginState({ commit, dispatch, state }) {
            // 如果已经初始化过，直接返回
            if (state.isInitialized) {
                return Promise.resolve();
            }

            try {
                const token = localStorage.getItem('access_token');
                if (token) {
                    try {
                        // 验证token有效性并获取用户信息
                        await dispatch('verifyToken');
                        await dispatch('loadUserInfo');
                        commit('setLoginState', true);
                    } catch (error) {
                        console.error('Token验证失败:', error);
                        dispatch('logout');
                    }
                } else {
                    commit('setLoginState', false);
                    commit('clearUserInfo');
                }
            } catch (error) {
                console.error('初始化登录状态失败:', error);
                commit('setLoginState', false);
                commit('clearUserInfo');
            } finally {
                commit('setInitialized', true);
            }
        },
        // 验证token有效性
        async verifyToken({ commit }) {
            const token = localStorage.getItem('access_token');
            if (!token) {
                throw new Error('No token found');
            }

            try {
                // 动态导入API函数以避免循环依赖
                const { VerifyToken } = await import('@/api/api.js');
                const response = await VerifyToken();

                if (response.data.valid) {
                    // 更新用户信息
                    commit('setUserInfo', {
                        username: response.data.username,
                        isAdmin: response.data.is_admin
                    });
                    return Promise.resolve();
                } else {
                    throw new Error('Token invalid');
                }
            } catch (error) {
                // Token无效，清除登录状态
                commit('setLoginState', false);
                commit('clearUserInfo');
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                throw error;
            }
        },
        // 加载用户信息
        async loadUserInfo({ commit }) {
            try {
                // 动态导入API函数以避免循环依赖
                const { GetUserProfile, GetUserAvatar } = await import('@/api/api.js');

                // 获取用户资料
                const profileData = await GetUserProfile();
                commit('setUserInfo', {
                    username: profileData.username || '',
                    nickname: profileData.nickname || '',
                    isAdmin: profileData.is_staff || profileData.is_superuser || false
                });

                // 获取用户头像
                try {
                    const avatarData = await GetUserAvatar();
                    if (avatarData && avatarData.avatar_url) {
                        commit('setUserInfo', {
                            avatar: avatarData.avatar_url
                        });
                    }
                } catch (avatarError) {
                    console.log('获取头像失败，使用默认头像');
                }
            } catch (error) {
                console.error('加载用户信息失败:', error);
                throw error;
            }
        }
    },
    getters: {
        isLoggedIn: state => state.checkLogin,
        isInitialized: state => state.isInitialized,
        isAdmin: state => state.userInfo.isAdmin,
        userAvatar: state => state.userInfo.avatar,
        userNickname: state => state.userInfo.nickname || state.userInfo.username
    }
});

export default store;
