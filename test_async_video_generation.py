#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试异步视频生成功能
"""

import requests
import json
import time
import threading

# 测试配置
BASE_URL = "http://localhost:8000"
VIDEO_API = f"{BASE_URL}/api/video/"
STATUS_API = f"{BASE_URL}/api/video/status/"
SAVE_API = f"{BASE_URL}/api/video/save/"

class AsyncVideoTester:
    def __init__(self):
        self.token = "your_token_here"  # 替换为实际的token
        self.headers = {
            'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json'
        }
        self.current_video_id = None
        self.status_check_running = False

    def test_async_video_generation(self):
        """测试异步视频生成完整流程"""
        print("开始测试异步视频生成功能...")
        print("=" * 60)

        # 1. 启动视频生成任务
        print("1. 启动视频生成任务...")
        result = self.start_video_generation()
        if not result:
            return False

        # 2. 模拟用户切换页面（停止状态检查）
        print("\n2. 模拟用户切换到其他页面...")
        time.sleep(5)
        self.stop_status_check()
        print("   用户已切换到其他页面，状态检查已停止")

        # 3. 模拟用户回到串联回忆页面（恢复状态检查）
        print("\n3. 模拟用户回到串联回忆页面...")
        time.sleep(3)
        self.resume_status_check()

        # 4. 等待视频生成完成
        print("\n4. 等待视频生成完成...")
        completed_status = self.wait_for_completion()
        if not completed_status:
            return False

        # 5. 测试保存功能
        print("\n5. 测试视频保存功能...")
        save_result = self.save_video()
        
        return save_result

    def start_video_generation(self):
        """启动视频生成"""
        test_data = {
            'pictures': [1, 2, 3],
            'title': '异步测试视频',
            'description': '测试异步视频生成功能'
        }

        try:
            response = requests.post(VIDEO_API, json=test_data, headers=self.headers)
            
            if response.status_code == 202:  # 202 Accepted
                result = response.json()
                self.current_video_id = result['video_id']
                print(f"✓ 视频生成任务启动成功!")
                print(f"  视频ID: {result['video_id']}")
                print(f"  状态: {result['status']}")
                print(f"  消息: {result['message']}")
                
                # 开始状态检查
                self.start_status_check()
                return True
            else:
                print(f"✗ 启动失败: {response.status_code}")
                print(f"  错误: {response.text}")
                return False

        except Exception as e:
            print(f"✗ 启动时出现异常: {e}")
            return False

    def start_status_check(self):
        """开始状态检查线程"""
        self.status_check_running = True
        self.status_thread = threading.Thread(target=self._status_check_worker, daemon=True)
        self.status_thread.start()
        print("   状态检查线程已启动")

    def stop_status_check(self):
        """停止状态检查"""
        self.status_check_running = False
        print("   状态检查已停止")

    def resume_status_check(self):
        """恢复状态检查"""
        if not self.status_check_running:
            self.start_status_check()
            print("   状态检查已恢复")

    def _status_check_worker(self):
        """状态检查工作线程"""
        while self.status_check_running and self.current_video_id:
            try:
                response = requests.get(
                    f"{STATUS_API}?video_id={self.current_video_id}",
                    headers=self.headers
                )
                
                if response.ok:
                    status = response.json()
                    print(f"   状态更新: {status['status']} - {status['progress']}%")
                    
                    if status['status'] in ['completed', 'failed']:
                        self.status_check_running = False
                        break
                
                time.sleep(2)  # 每2秒检查一次
                
            except Exception as e:
                print(f"   状态检查出错: {e}")
                time.sleep(5)  # 出错时等待更长时间

    def wait_for_completion(self):
        """等待视频生成完成"""
        max_wait_time = 300  # 最大等待5分钟
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            try:
                response = requests.get(
                    f"{STATUS_API}?video_id={self.current_video_id}",
                    headers=self.headers
                )
                
                if response.ok:
                    status = response.json()
                    
                    if status['status'] == 'completed':
                        print(f"✓ 视频生成完成!")
                        print(f"  视频URL: {status['video_url']}")
                        print(f"  完成时间: {status['completed_at']}")
                        return status
                    elif status['status'] == 'failed':
                        print(f"✗ 视频生成失败!")
                        print(f"  错误信息: {status['error_message']}")
                        return None
                    else:
                        print(f"  当前状态: {status['status']} - {status['progress']}%")
                
                time.sleep(5)
                
            except Exception as e:
                print(f"  检查完成状态时出错: {e}")
                time.sleep(5)
        
        print("✗ 等待超时，视频生成可能失败")
        return None

    def save_video(self):
        """保存视频"""
        try:
            save_data = {'video_id': self.current_video_id}
            response = requests.post(SAVE_API, json=save_data, headers=self.headers)
            
            if response.ok:
                result = response.json()
                print(f"✓ 视频保存成功!")
                print(f"  消息: {result['message']}")
                print(f"  视频URL: {result['video_url']}")
                return True
            else:
                print(f"✗ 保存失败: {response.status_code}")
                print(f"  错误: {response.text}")
                return False

        except Exception as e:
            print(f"✗ 保存时出现异常: {e}")
            return False

    def test_regeneration(self):
        """测试重新生成功能"""
        print("\n" + "=" * 60)
        print("测试重新生成功能...")
        
        if not self.current_video_id:
            print("✗ 没有可重新生成的视频")
            return False

        test_data = {
            'pictures': [1, 2, 3, 4, 5],  # 添加更多图片
            'title': '重新生成的视频',
            'description': '测试视频重新生成功能',
            'video_id': self.current_video_id  # 提供video_id进行覆盖
        }

        try:
            response = requests.post(VIDEO_API, json=test_data, headers=self.headers)
            
            if response.status_code == 202:
                result = response.json()
                print(f"✓ 视频重新生成任务启动成功!")
                print(f"  视频ID: {result['video_id']}")
                print(f"  消息: {result['message']}")
                return True
            else:
                print(f"✗ 重新生成启动失败: {response.status_code}")
                return False

        except Exception as e:
            print(f"✗ 重新生成时出现异常: {e}")
            return False

def test_api_endpoints():
    """测试API端点"""
    print("API端点测试:")
    print("=" * 40)
    
    endpoints = [
        ("POST", VIDEO_API, "启动视频生成"),
        ("GET", STATUS_API, "查询视频状态"),
        ("POST", SAVE_API, "保存视频"),
        ("GET", VIDEO_API, "获取视频列表"),
        ("DELETE", VIDEO_API, "删除视频")
    ]
    
    for method, url, description in endpoints:
        print(f"{method:6} {url:40} - {description}")

if __name__ == "__main__":
    print("异步视频生成功能测试")
    print("=" * 60)
    
    # 显示API端点
    test_api_endpoints()
    print()
    
    # 创建测试实例
    tester = AsyncVideoTester()
    
    try:
        # 执行主要测试
        success = tester.test_async_video_generation()
        
        if success:
            # 测试重新生成功能
            tester.test_regeneration()
        
        print("\n" + "=" * 60)
        print("测试完成!")
        
        print("\n主要功能验证:")
        print("✓ 异步视频生成 - 用户不需要等待")
        print("✓ 状态轮询 - 实时更新进度")
        print("✓ 页面切换 - 状态保持同步")
        print("✓ 视频覆盖 - 重新生成时覆盖原文件")
        print("✓ 用户确认 - 满意后才保存")
        
        print("\n用户体验:")
        print("1. 点击生成 → 立即显示'正在努力制作轨迹视频'")
        print("2. 可以切换页面 → 后台继续生成")
        print("3. 回到页面 → 状态同步显示")
        print("4. 生成完成 → 显示预览")
        print("5. 点击保存 → 确认保存并刷新")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
    finally:
        # 清理
        tester.stop_status_check()
        print("测试资源已清理")
