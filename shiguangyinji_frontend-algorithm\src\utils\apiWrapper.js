/**
 * API包装器示例
 * 展示如何在组件中使用新的错误处理机制
 */

import { safeApiCall, handleApiError } from './errorHandler.js'
import * as api from '@/api/api.js'

/**
 * 安全的用户资料获取
 * @param {Object} options - 选项
 * @returns {Promise} 用户资料数据
 */
export async function safeGetUserProfile(options = {}) {
  return safeApiCall(
    () => api.GetUserProfile(),
    {
      retries: 2,
      retryDelay: 1000,
      fallbackData: {
        nickname: '用户',
        username: 'user',
        introduction: '',
        article_count: 0,
        fans_count: 0
      },
      onError: (errorInfo) => {
        console.error('获取用户资料失败:', errorInfo.message)
        // 可以在这里显示用户友好的错误消息
      },
      onWarning: (warnings) => {
        console.warn('用户资料警告:', warnings)
        // 可以在这里处理警告，比如显示提示信息
      },
      ...options
    }
  )
}

/**
 * 安全的文档创建
 * @param {string} title - 标题
 * @param {string} content - 内容
 * @param {Object} options - 选项
 * @returns {Promise} 创建结果
 */
export async function safeCreateDocument(title, content, options = {}) {
  return safeApiCall(
    () => api.createDocument(title, content),
    {
      retries: 1,
      retryDelay: 2000,
      onError: (errorInfo) => {
        console.error('创建文档失败:', errorInfo.message)
        // 根据错误类型显示不同的提示
        if (errorInfo.code === 401) {
          // 未登录
          alert('请先登录后再创建文档')
        } else if (errorInfo.code === 403) {
          // 权限不足
          alert('您没有权限创建文档')
        } else {
          alert('创建文档失败，请稍后重试')
        }
      },
      onWarning: (warnings) => {
        console.warn('创建文档警告:', warnings)
        // 显示警告但不阻止操作
        if (Array.isArray(warnings)) {
          warnings.forEach(warning => {
            console.warn('警告:', warning)
          })
        }
      },
      ...options
    }
  )
}

/**
 * 安全的登录
 * @param {Object} loginData - 登录数据
 * @param {Object} options - 选项
 * @returns {Promise} 登录结果
 */
export async function safeLogin(loginData, options = {}) {
  return safeApiCall(
    () => api.Login(loginData),
    {
      retries: 0, // 登录不重试
      onError: (errorInfo) => {
        console.error('登录失败:', errorInfo.message)
        
        // 根据错误类型提供具体的错误信息
        let userMessage = '登录失败'
        
        if (errorInfo.code === 400) {
          userMessage = '用户名或密码错误'
        } else if (errorInfo.code === 429) {
          userMessage = '登录尝试过于频繁，请稍后再试'
        } else if (errorInfo.details && errorInfo.details.error) {
          userMessage = errorInfo.details.error
        }
        
        // 这里可以设置到组件的错误状态中
        if (options.onLoginError) {
          options.onLoginError(userMessage)
        }
      },
      onWarning: (warnings) => {
        console.warn('登录警告:', warnings)
        // 登录警告可能包含安全提示等
        if (options.onLoginWarning) {
          options.onLoginWarning(warnings)
        }
      },
      ...options
    }
  )
}

/**
 * 批量API调用的错误处理
 * @param {Array} apiCalls - API调用数组
 * @param {Object} options - 选项
 * @returns {Promise} 结果数组
 */
export async function safeBatchApiCalls(apiCalls, options = {}) {
  const {
    continueOnError = true,
    maxConcurrent = 3
  } = options

  const results = []
  const errors = []

  // 分批处理以避免过多并发请求
  for (let i = 0; i < apiCalls.length; i += maxConcurrent) {
    const batch = apiCalls.slice(i, i + maxConcurrent)
    
    const batchPromises = batch.map(async (apiCall, index) => {
      try {
        const result = await safeApiCall(apiCall, {
          retries: 1,
          onError: (errorInfo) => {
            console.error(`批量API调用 ${i + index} 失败:`, errorInfo.message)
            errors.push({
              index: i + index,
              error: errorInfo
            })
          }
        })
        return { success: true, data: result, index: i + index }
      } catch (error) {
        if (continueOnError) {
          return { success: false, error, index: i + index }
        } else {
          throw error
        }
      }
    })

    const batchResults = await Promise.all(batchPromises)
    results.push(...batchResults)
  }

  return {
    results,
    errors,
    successCount: results.filter(r => r.success).length,
    errorCount: errors.length
  }
}

/**
 * 创建带有错误处理的Vue组合式函数
 * @param {Function} apiFunction - API函数
 * @param {Object} defaultOptions - 默认选项
 * @returns {Function} 组合式函数
 */
export function useApiWithErrorHandling(apiFunction, defaultOptions = {}) {
  const loading = ref(false)
  const error = ref(null)
  const data = ref(null)
  const warnings = ref([])

  const execute = async (...args) => {
    loading.value = true
    error.value = null
    warnings.value = []

    try {
      const result = await safeApiCall(
        () => apiFunction(...args),
        {
          onError: (errorInfo) => {
            error.value = errorInfo
          },
          onWarning: (warningList) => {
            warnings.value = Array.isArray(warningList) ? warningList : [warningList]
          },
          ...defaultOptions
        }
      )
      
      data.value = result
      return result
    } catch (err) {
      error.value = handleApiError(err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const reset = () => {
    loading.value = false
    error.value = null
    data.value = null
    warnings.value = []
  }

  return {
    loading: readonly(loading),
    error: readonly(error),
    data: readonly(data),
    warnings: readonly(warnings),
    execute,
    reset
  }
}

export default {
  safeGetUserProfile,
  safeCreateDocument,
  safeLogin,
  safeBatchApiCalls,
  useApiWithErrorHandling
}
