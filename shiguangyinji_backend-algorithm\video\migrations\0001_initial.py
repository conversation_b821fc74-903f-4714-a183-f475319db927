# Generated by Django 4.2.20 on 2025-05-12 09:23

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("issue", "0002_picture_relations"),
    ]

    operations = [
        migrations.CreateModel(
            name="Video",
            fields=[
                (
                    "video_id",
                    models.AutoField(db_column="id", primary_key=True, serialize=False),
                ),
                ("script_url", models.CharField(blank=True, max_length=255, null=True)),
                ("video_url", models.CharField(blank=True, max_length=255, null=True)),
                ("upload_at", models.DateTimeField(auto_now_add=True)),
                (
                    "owner",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="VideoPicture",
            fields=[
                (
                    "video_picture_id",
                    models.AutoField(db_column="id", primary_key=True, serialize=False),
                ),
                (
                    "picture",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="issue.picture",
                    ),
                ),
                (
                    "video",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="video.video",
                    ),
                ),
            ],
            options={
                "db_table": "video_picture",
                "managed": True,
            },
        ),
    ]
