import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import router from './router/index.js';
import store from './store/store.js';

// 创建应用实例
const app = createApp(App);

// 使用路由和状态管理
app.use(router);
app.use(store);

// 初始化应用
async function initApp() {
    try {
        // 初始化 Vuex 状态 (在应用挂载前)
        await store.dispatch('initLoginState');
        console.log('应用初始化完成');
    } catch (error) {
        console.error('应用初始化失败:', error);
    } finally {
        // 无论初始化成功与否，都挂载应用
        app.mount('#app');
    }
}

// 启动应用
initApp();
