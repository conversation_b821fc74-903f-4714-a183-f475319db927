# 前端错误处理解决方案

## 问题描述

后端warning导致前端报错的常见情况：
1. 后端返回警告信息但HTTP状态码不是200
2. 响应格式包含警告字段，前端解析时出错
3. axios拦截器将警告当作错误处理

## 解决方案

### 1. 增强的响应拦截器

在 `utils/request.js` 中增强了响应拦截器：

```javascript
service.interceptors.response.use(
  response => {
    // 检查响应中是否包含警告信息
    if (response.data && response.data.warnings) {
      console.warn('后端警告:', response.data.warnings)
    }
    
    // 即使有警告，只要状态码是成功的，就返回响应
    return response
  },
  async error => {
    // 增强错误处理：区分不同类型的错误
    if (error.response) {
      const { status, data } = error.response
      
      // 如果响应包含警告但不是致命错误，可以选择不抛出异常
      if (data && data.warnings && !data.error) {
        console.warn('后端警告（非致命）:', data.warnings)
        // 返回一个包含警告的成功响应
        return Promise.resolve({
          data: data,
          status: status,
          // ... 其他响应字段
        })
      }
    }
    
    return Promise.reject(error)
  }
)
```

### 2. 错误处理工具函数

创建了 `utils/errorHandler.js` 提供统一的错误处理：

#### 主要功能

1. **handleApiResponse** - 处理API响应，区分错误和警告
2. **handleApiError** - 统一错误处理逻辑
3. **safeApiCall** - 安全的API调用包装器
4. **createSafeApiCall** - 创建带错误处理的API函数

#### 使用示例

```javascript
import { safeApiCall, handleApiResponse } from '@/utils/errorHandler.js'

// 方式1：使用safeApiCall包装
const userData = await safeApiCall(
  () => api.GetUserProfile(),
  {
    retries: 2,
    fallbackData: { nickname: '默认用户' },
    onWarning: (warnings) => {
      console.warn('用户数据警告:', warnings)
    }
  }
)

// 方式2：手动处理响应
try {
  const response = await api.GetUserProfile()
  const data = handleApiResponse(response, {
    showWarnings: true,
    throwOnWarnings: false
  })
} catch (error) {
  const errorInfo = handleApiError(error)
  console.error('错误:', errorInfo.message)
}
```

### 3. Vue组合式函数

提供了 `useApiWithErrorHandling` 组合式函数：

```javascript
import { useApiWithErrorHandling } from '@/utils/apiWrapper.js'

// 在组件中使用
const {
  loading,
  error,
  data,
  warnings,
  execute,
  reset
} = useApiWithErrorHandling(api.GetUserProfile, {
  retries: 1,
  fallbackData: null
})

// 执行API调用
await execute()

// 检查结果
if (error.value) {
  console.error('错误:', error.value.message)
}

if (warnings.value.length > 0) {
  console.warn('警告:', warnings.value)
}
```

### 4. 组件中的使用示例

```vue
<template>
  <div>
    <div v-if="loading">加载中...</div>
    <div v-else-if="error" class="error">
      {{ error.message }}
    </div>
    <div v-else-if="warnings.length > 0" class="warning">
      警告: {{ warnings.join(', ') }}
    </div>
    <div v-else>
      <!-- 正常内容 -->
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { safeGetUserProfile } from '@/utils/apiWrapper.js'

const loading = ref(false)
const error = ref(null)
const warnings = ref([])
const userData = ref(null)

onMounted(async () => {
  loading.value = true
  
  try {
    userData.value = await safeGetUserProfile({
      onError: (errorInfo) => {
        error.value = errorInfo
      },
      onWarning: (warningList) => {
        warnings.value = Array.isArray(warningList) ? warningList : [warningList]
      }
    })
  } catch (err) {
    console.error('获取用户数据失败:', err)
  } finally {
    loading.value = false
  }
})
</script>
```

## 配置选项

### 错误处理选项

```javascript
const options = {
  // 是否显示警告
  showWarnings: true,
  
  // 是否将警告当作错误处理
  throwOnWarnings: false,
  
  // 重试次数
  retries: 2,
  
  // 重试延迟（毫秒）
  retryDelay: 1000,
  
  // 回退数据
  fallbackData: null,
  
  // 错误回调
  onError: (errorInfo) => {
    console.error('API错误:', errorInfo.message)
  },
  
  // 警告回调
  onWarning: (warnings) => {
    console.warn('API警告:', warnings)
  }
}
```

## 最佳实践

### 1. 区分警告和错误

```javascript
// 后端响应格式建议
{
  "data": { /* 实际数据 */ },
  "warnings": ["这是一个警告信息"],  // 非致命问题
  "error": null                      // 致命错误
}

// 或者
{
  "data": { /* 实际数据 */ },
  "error": "这是一个错误信息"        // 致命错误，应该中断操作
}
```

### 2. 渐进式错误处理

```javascript
// 1. 基础使用：直接调用API
const data = await api.GetUserProfile()

// 2. 添加错误处理
try {
  const data = await api.GetUserProfile()
} catch (error) {
  console.error('错误:', error)
}

// 3. 使用安全包装器
const data = await safeApiCall(() => api.GetUserProfile())

// 4. 完整的错误和警告处理
const data = await safeApiCall(
  () => api.GetUserProfile(),
  {
    onError: handleError,
    onWarning: handleWarning,
    fallbackData: defaultUserData
  }
)
```

### 3. 全局错误处理

```javascript
// 在main.js中设置全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err, info)
  
  // 可以发送错误报告到监控服务
  // sendErrorReport(err, vm, info)
}
```

## 常见问题解决

### 1. 后端返回警告但前端报错

**原因**: axios将非200状态码当作错误处理

**解决**: 在响应拦截器中检查是否为非致命警告

```javascript
if (data && data.warnings && !data.error) {
  // 将警告转换为成功响应
  return Promise.resolve(response)
}
```

### 2. 警告信息格式不统一

**解决**: 统一处理不同格式的警告

```javascript
function normalizeWarnings(warnings) {
  if (!warnings) return []
  if (typeof warnings === 'string') return [warnings]
  if (Array.isArray(warnings)) return warnings
  return [String(warnings)]
}
```

### 3. 网络错误和服务器错误混淆

**解决**: 在错误处理中明确区分

```javascript
if (error.response) {
  // 服务器返回了错误状态码
} else if (error.request) {
  // 请求发出但没有响应（网络问题）
} else {
  // 请求配置错误
}
```

## 总结

通过这套错误处理方案：

1. **统一处理**: 所有API调用都有一致的错误处理逻辑
2. **区分警告和错误**: 警告不会中断用户操作
3. **用户友好**: 提供清晰的错误信息和回退机制
4. **开发友好**: 详细的日志和调试信息
5. **可扩展**: 易于添加新的错误处理策略

这样可以有效解决后端warning导致前端报错的问题，同时提升整体的用户体验。
