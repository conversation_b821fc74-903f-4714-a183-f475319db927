# 串联回忆视频生成API文档

## 概述

串联回忆功能允许用户选择多张图片，生成包含这些图片的时光回忆视频。支持视频的生成、覆盖、查看和删除操作。

## 数据库变更

### Video模型新增字段

```python
class Video(models.Model):
    # 原有字段...
    title = models.CharField(max_length=255, blank=True, null=True)  # 视频标题
    description = models.TextField(blank=True, null=True)  # 内容方向描述
    event_title = models.CharField(max_length=255, blank=True, null=True)  # 选择事件的标题
```

## API接口

### 1. 生成/覆盖视频

**接口地址:** `POST /api/video/`

**功能:** 根据图片ID列表生成视频，支持覆盖已存在的视频

**请求参数:**
```json
{
    "pictures": [1, 2, 3, 4],  // 图片ID列表 (必需)
    "title": "西湖游记",       // 视频标题 (可选)
    "description": "和家人一起游览西湖的美好回忆",  // 内容描述 (可选)
    "video_id": 123           // 视频ID，提供时进行覆盖 (可选)
}
```

**响应示例:**
```json
{
    "video_id": 123,
    "video_url": "/api/media/video/video_1_1703123456.mp4",
    "script_url": "/api/media/video/video_1_1703123456_script.json",
    "title": "西湖游记",
    "description": "和家人一起游览西湖的美好回忆",
    "event_title": "西湖游玩 | 家庭聚餐",
    "message": "视频生成成功"  // 或 "视频覆盖成功"
}
```

**状态码:**
- `201`: 新视频创建成功
- `200`: 视频覆盖成功
- `400`: 请求参数错误
- `401`: 用户未认证
- `404`: 图片或视频不存在
- `500`: 视频生成失败

### 2. 获取视频列表

**接口地址:** `GET /api/video/`

**功能:** 获取当前用户的所有视频列表

**响应示例:**
```json
{
    "videos": [
        {
            "video_id": 123,
            "title": "西湖游记",
            "description": "和家人一起游览西湖的美好回忆",
            "event_title": "西湖游玩 | 家庭聚餐",
            "video_url": "/api/media/video/video_1_1703123456.mp4",
            "upload_at": "2024-12-21T10:30:00Z"
        }
    ]
}
```

### 3. 获取视频详情

**接口地址:** `GET /api/video/?video_id=<id>`

**功能:** 获取指定视频的详细信息

**请求参数:**
- `video_id`: 视频ID (URL参数)

**响应示例:**
```json
{
    "video_id": 123,
    "title": "西湖游记",
    "description": "和家人一起游览西湖的美好回忆",
    "event_title": "西湖游玩 | 家庭聚餐",
    "video_url": "/api/media/video/video_1_1703123456.mp4",
    "script_url": "/api/media/video/video_1_1703123456_script.json",
    "upload_at": "2024-12-21T10:30:00Z",
    "picture_ids": [1, 2, 3, 4]
}
```

### 4. 删除视频

**接口地址:** `DELETE /api/video/?video_id=<id>`

**功能:** 删除指定的视频及其相关文件

**请求参数:**
- `video_id`: 视频ID (URL参数)

**响应示例:**
```json
{
    "message": "Video deleted successfully"
}
```

## 功能特性

### 1. 视频覆盖机制

- **新建模式**: 不提供 `video_id` 时，创建新视频
- **覆盖模式**: 提供 `video_id` 时，覆盖现有视频
- **文件管理**: 覆盖时使用相同的文件名，确保URL不变
- **数据更新**: 更新视频的标题、描述等信息

### 2. 数据关联

- **事件标题**: 自动从选择的图片所属事件中提取标题
- **图片关联**: 通过 `VideoPicture` 表维护视频与图片的关系
- **用户隔离**: 每个用户只能操作自己的视频

### 3. 文件处理

- **自动目录**: 自动创建 `media/video/` 目录
- **文件命名**: 使用 `video_{user_id}_{timestamp}` 格式
- **脚本保存**: 同时保存视频脚本文件供后续使用
- **文件清理**: 删除视频时同时删除相关文件

## 错误处理

### 常见错误码

- `400 Bad Request`: 
  - 缺少必需参数
  - 图片ID列表格式错误
  - 图片列表为空

- `401 Unauthorized`:
  - 用户未登录
  - Token无效或过期

- `404 Not Found`:
  - 指定的图片不存在
  - 指定的视频不存在
  - 用户角色信息不存在

- `500 Internal Server Error`:
  - 视频生成过程失败
  - 文件系统错误
  - AI服务调用失败

## 使用流程

### 1. 生成新视频

```javascript
// 前端调用示例
const response = await fetch('/api/video/', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        pictures: [1, 2, 3],
        title: '我的回忆',
        description: '美好的时光'
    })
});

const result = await response.json();
console.log('视频生成成功:', result.video_url);
```

### 2. 覆盖现有视频

```javascript
// 用户不满意，重新生成
const response = await fetch('/api/video/', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        pictures: [1, 2, 3, 4, 5],  // 添加更多图片
        title: '我的回忆 - 完整版',
        description: '更完整的美好时光',
        video_id: 123  // 覆盖现有视频
    })
});
```

## 注意事项

1. **认证要求**: 所有接口都需要用户认证
2. **权限控制**: 用户只能操作自己的视频
3. **文件大小**: 注意视频文件可能较大，需要合理的超时设置
4. **并发处理**: 视频生成是耗时操作，建议使用异步处理
5. **存储空间**: 定期清理无用的视频文件以节省存储空间

## 部署要求

1. **数据库迁移**: 运行 `python manage.py migrate` 应用新字段
2. **媒体目录**: 确保 `MEDIA_ROOT/video/` 目录可写
3. **AI服务**: 确保视频生成相关的AI服务正常运行
4. **存储空间**: 预留足够的磁盘空间存储视频文件
