# Generated by Django 5.2.1 on 2025-05-27 11:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("video", "0003_video_add_fields"),
    ]

    operations = [
        migrations.CreateModel(
            name="VideoTask",
            fields=[
                (
                    "task_id",
                    models.Char<PERSON>ield(max_length=255, primary_key=True, serialize=False),
                ),
                ("video_path", models.CharField(blank=True, max_length=255, null=True)),
                ("status", models.CharField(blank=True, max_length=50, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "db_table": "video_task",
                "managed": True,
            },
        ),
    ]
