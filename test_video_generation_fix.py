#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试视频生成修复效果
"""

import requests
import json
import time

# 测试配置
BASE_URL = "http://localhost:8000"
VIDEO_API = f"{BASE_URL}/api/video/create/"
STATUS_API = f"{BASE_URL}/api/video/status/"

def test_video_generation():
    """测试修复后的视频生成功能"""
    print("测试修复后的视频生成功能")
    print("=" * 60)
    
    # 测试数据
    test_data = {
        'pictures': [4],  # 使用一个图片ID进行测试
        'title': '修复测试视频',
        'description': '测试路径修复和函数调用修复'
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer your_token_here'  # 替换为实际token
    }
    
    try:
        print("1. 启动视频生成...")
        response = requests.post(VIDEO_API, json=test_data, headers=headers)
        
        if response.status_code == 202:
            result = response.json()
            video_id = result['video_id']
            print(f"✅ 视频生成任务启动成功!")
            print(f"   视频ID: {video_id}")
            print(f"   状态: {result['status']}")
            
            # 监控生成过程
            print("\n2. 监控生成过程...")
            monitor_generation(video_id, headers)
            
        else:
            print(f"❌ 启动失败: {response.status_code}")
            print(f"   错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def monitor_generation(video_id, headers):
    """监控视频生成过程"""
    max_wait_time = 300  # 最大等待5分钟
    start_time = time.time()
    last_progress = -1
    
    while time.time() - start_time < max_wait_time:
        try:
            response = requests.get(
                f"{STATUS_API}?video_id={video_id}",
                headers=headers
            )
            
            if response.ok:
                status = response.json()
                current_progress = status.get('progress', 0)
                
                # 只在进度变化时打印
                if current_progress != last_progress:
                    print(f"   进度: {current_progress}% - 状态: {status['status']}")
                    last_progress = current_progress
                
                if status['status'] == 'completed':
                    print(f"✅ 视频生成完成!")
                    print(f"   视频URL: {status.get('video_url', 'N/A')}")
                    print(f"   完成时间: {status.get('completed_at', 'N/A')}")
                    return True
                    
                elif status['status'] == 'failed':
                    print(f"❌ 视频生成失败!")
                    print(f"   错误信息: {status.get('error_message', 'Unknown error')}")
                    return False
            
            time.sleep(2)  # 每2秒检查一次
            
        except Exception as e:
            print(f"   检查状态时出错: {e}")
            time.sleep(5)
    
    print("⏰ 等待超时")
    return False

def show_fix_summary():
    """显示修复总结"""
    print("\n修复总结:")
    print("=" * 60)
    print("已修复的问题:")
    print("1. ✅ 路径重复拼接问题")
    print("   - 修复了 _process_pictures 中的路径处理")
    print("   - 修复了 _get_protagonist 中的头像路径处理")
    print("   - 修复了 judge_video 中的图片路径处理")
    print()
    print("2. ✅ generate_video_from_json 参数错误")
    print("   - 确认函数期望脚本数据作为第一个参数")
    print("   - 修复了函数调用方式")
    print()
    print("3. ✅ 错误处理改进")
    print("   - 添加了详细的错误信息和堆栈跟踪")
    print("   - 添加了文件存在性检查")
    print("   - 添加了路径验证逻辑")
    print()
    print("修复的具体内容:")
    print("- tasks.py: 路径处理逻辑优化")
    print("- edit.py: judge_video 函数路径修复")
    print("- 添加了更好的调试信息")
    print()
    print("预期效果:")
    print("- 不再出现路径重复拼接")
    print("- API调用错误应该减少")
    print("- 'str' object has no attribute 'get' 错误应该解决")

if __name__ == "__main__":
    print("视频生成修复测试")
    print("=" * 60)
    
    # 显示修复总结
    show_fix_summary()
    
    print("\n" + "=" * 60)
    print("开始测试...")
    
    # 运行测试
    test_video_generation()
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print()
    print("如果仍有问题，请检查:")
    print("1. 图片文件是否存在于正确路径")
    print("2. AI服务API密钥是否配置正确")
    print("3. 网络连接是否正常")
    print("4. 服务器日志中的详细错误信息")
