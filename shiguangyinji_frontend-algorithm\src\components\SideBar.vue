<template>
  <div class="sidebar">
    <div class="sidebar-header">
      <div class="logo-container">
        <img src="@/assets/images/test.png" alt="Logo" class="logo-img" />
        <h1 class="logo-text">时光印记</h1>
      </div>
    </div>

    <div class="menu-container">
      <div class="menu-section">
        <h2 class="menu-title">首页</h2>
        <ul class="menu-list">
          <li
            @click="setActiveItem('我的足迹', '/chinamap')"
            :class="{ active: activeItem === '我的足迹' }"
            class="menu-item"
          >
            <div class="menu-icon">
              <img src="@/assets/icon/map.svg" alt="我的足迹" />
            </div>
            <span class="menu-text">我的足迹</span>
          </li>
        </ul>
      </div>

      <div class="menu-section">
        <h2 class="menu-title">岁月笔记</h2>
        <ul class="menu-list">
          <li
            @click="setActiveItem('上传事件', '/uploadevent')"
            :class="{ active: activeItem === '上传事件' }"
            class="menu-item"
          >
            <div class="menu-icon">
              <img src="@/assets/icon/upload.svg" alt="拾忆锚点" />
            </div>
            <span class="menu-text">拾忆锚点</span>
          </li>

          <li
            @click="setActiveItem('事件列表', '/eventgallery')"
            :class="{ active: activeItem === '事件列表' }"
            class="menu-item"
          >
            <div class="menu-icon">
              <img src="@/assets/icon/list.svg" alt="记忆回廊" />
            </div>
            <span class="menu-text">记忆回廊</span>
          </li>
        </ul>
      </div>

      <div class="menu-section">
        <h2 class="menu-title">轨迹相册</h2>
        <ul class="menu-list">
          <li
            @click="setActiveItem('制作视频', '/make-video')"
            :class="{ active: activeItem === '制作视频' }"
            class="menu-item"
          >
            <div class="menu-icon">
              <img src="@/assets/icon/edit.svg" alt="串联回忆" />
            </div>
            <span class="menu-text">串联回忆</span>
          </li>

          <li
            @click="setActiveItem('视频轨迹', '/video-gallery')"
            :class="{ active: activeItem === '视频轨迹' }"
            class="menu-item"
          >
            <div class="menu-icon">
              <img src="@/assets/icon/list.svg" alt="轨迹放映" />
            </div>
            <span class="menu-text">轨迹放映</span>
          </li>
        </ul>
      </div>

      <div class="menu-section">
        <h2 class="menu-title">社交</h2>
        <ul class="menu-list">
          <li
            @click="setActiveItem('好友', '/friends')"
            :class="{ active: activeItem === '好友' }"
            class="menu-item"
          >
            <div class="menu-icon">
              <img src="@/assets/icon/user.svg" alt="好友" />
            </div>
            <span class="menu-text">我的好友</span>
          </li>

          <li
            @click="setActiveItem('首页', '/index')"
            :class="{ active: activeItem === '首页' }"
            class="menu-item"
          >
            <div class="menu-icon">
              <img src="@/assets/icon/home.svg" alt="我的推荐" />
            </div>
            <span class="menu-text">我的推荐</span>
          </li>

          <li
            @click="setActiveItem('智能体', '/agent')"
            :class="{ active: activeItem === '智能体' }"
            class="menu-item"
          >
            <div class="menu-icon">
              <img src="@/assets/icon/ai.svg" alt="时光引路人" />
            </div>
            <span class="menu-text">时光记录员</span>
          </li>
        </ul>
      </div>
    </div>

    <div class="user-section">
      <div class="user-profile" @click="setActiveItem('', '/user')">
        <img :src="userAvatar" alt="用户头像" class="user-avatar" />
        <div class="user-info">
          <span class="user-name">{{ userName }}</span>
          <span v-if="checkLogin" class="user-status">点击查看个人中心</span>
        </div>
      </div>

      <!-- 登录/登出按钮 -->
      <div class="auth-buttons">
        <!-- 已登录状态显示登出按钮 -->
        <div
          v-if="checkLogin"
          class="auth-button logout-button"
          @click="logout"
        >
          <div class="auth-icon-container">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
              <polyline points="16 17 21 12 16 7"></polyline>
              <line x1="21" y1="12" x2="9" y2="12"></line>
            </svg>
          </div>
          <span class="auth-text">登出</span>
        </div>

        <!-- 未登录状态显示登录按钮 -->
        <div
          v-else
          class="auth-button login-button"
          @click="setActiveItem('登录', '/login')"
        >
          <div class="auth-icon-container">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
              <polyline points="10 17 15 12 10 7"></polyline>
              <line x1="15" y1="12" x2="3" y2="12"></line>
            </svg>
          </div>
          <span class="auth-text">登录</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';
import { Logout } from "@/api/api.js";

const router = useRouter();
const route = useRoute();
const store = useStore();

const activeItem = ref('首页');

// 路径到菜单项名称的映射
const pathToMenuItemMap = {
  '/index': '首页',
  '/agent': '智能体',
  '/chinamap': '我的足迹',
  '/uploadevent': '上传事件',
  '/eventgallery': '事件列表',
  '/make-video': '制作视频',
  '/video-gallery': '视频轨迹',
  '/friends': '好友',
  '/user': '',
  '/login': '登录',
  // 处理详情页面
  '/event': '事件列表' // 事件详情页面，保持事件列表高亮
};

// 根据当前路由路径更新活动菜单项
const updateActiveItemFromRoute = (path) => {
  // 处理路径前缀匹配，例如 /event/123 应该匹配 /event
  let menuItem;

  // 首先尝试完全匹配
  menuItem = pathToMenuItemMap[path];

  // 如果没有完全匹配，尝试前缀匹配
  if (menuItem === undefined) {
    // 查找以 / 分割的第一段路径
    const pathSegment = '/' + path.split('/')[1];
    menuItem = pathToMenuItemMap[pathSegment];
  }

  if (menuItem !== undefined) {
    activeItem.value = menuItem;
  }
};

// 从 Vuex 获取登录状态和用户信息
const checkLogin = computed(() => store.getters.isLoggedIn);
const userAvatar = computed(() => {
  const avatar = store.getters.userAvatar;
  return avatar || '/src/assets/images/avartar.png';
});
const userName = computed(() => store.getters.userNickname || '未登录');

// 初始化组件
onMounted(async () => {
  try {
    // 确保store已经初始化
    if (!store.getters.isInitialized) {
      await store.dispatch('initLoginState');
    }

    // 根据当前路由设置活动菜单项
    updateActiveItemFromRoute(route.path);
  } catch (error) {
    console.error('SideBar初始化出错:', error);
  }
});

// 监听路由变化，更新活动菜单项
watch(() => route.path, (newPath) => {
  updateActiveItemFromRoute(newPath);
});

function setActiveItem(item, link) {
  activeItem.value = item;
  router.push({ path: link });
  // 注意：不需要在这里手动更新activeItem，因为路由变化会触发watch回调
}

async function logout() {
  try {
    // 显示加载状态
    const loadingEl = document.createElement('div');
    loadingEl.className = 'logout-loading';
    loadingEl.innerHTML = '<div class="logout-spinner"></div>';
    document.body.appendChild(loadingEl);

    // 尝试调用登出API
    await Logout();
    console.log("登出请求成功");
  } catch (error) {
    console.error("登出API调用失败:", error.response?.data || error.message);
    // 即使API调用失败，我们仍然会在前端执行登出操作
  } finally {
    // 移除加载状态
    const loadingEl = document.querySelector('.logout-loading');
    if (loadingEl) {
      document.body.removeChild(loadingEl);
    }

    // 执行登出操作
    store.dispatch('logout');

    // 跳转到引导页面
    await router.push('/guide');
  }
}
</script>


<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.sidebar {
  min-height: 100vh;
  height: 100vh;
  background-color: var(--background-white);
  position: fixed;
  width: 250px;
  transition: all var(--transition-speed) ease;
  overflow-y: auto;
  overflow-x: hidden;
  box-shadow: 0 0 20px var(--shadow-color);
  user-select: none;
  display: flex;
  flex-direction: column;
  z-index: 1000;
}

/* Sidebar is always expanded */

/* Sidebar Header */
.sidebar-header {
  padding: 20px 15px;
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-img {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  object-fit: cover;
}

.logo-text {
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--primary-dark);
  font-family: 'ChangErFeiBai', sans-serif;
  white-space: nowrap;
  transition: opacity var(--transition-speed) ease;
}

/* Logo text is always visible */

/* Menu Container */
.menu-container {
  flex: 1;
  padding: 15px 0;
  overflow-y: auto;
  max-height: calc(100vh - 200px); /* 减去头部和底部的高度 */
}

.menu-section {
  margin-bottom: 20px;
}

.menu-title {
  font-size: 0.8rem;
  text-transform: uppercase;
  color: var(--text-secondary);
  opacity: 0.7;
  padding: 0 20px;
  margin-bottom: 10px;
  white-space: nowrap;
  transition: opacity var(--transition-speed) ease;
}

/* Menu title is always visible */

.menu-list {
  list-style: none;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  border-radius: 0 20px 20px 0;
  margin-bottom: 5px;
  position: relative;
}

.menu-item:hover {
  background-color: rgba(0,0,0,0.03);
}

.menu-item.active {
  background-color: var(--accent-color);
}

.menu-item.active .menu-icon img {
  filter: brightness(0) invert(1);
}

.menu-item.active .menu-text {
  color: var(--text-light);
  font-weight: 600;
}

.menu-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  margin-right: 15px;
  transition: all var(--transition-speed) ease;
}

.menu-icon img {
  width: 20px;
  height: 20px;
  transition: all var(--transition-speed) ease;
}

.menu-text {
  font-size: 1rem;
  color: var(--text-primary);
  white-space: nowrap;
  transition: all var(--transition-speed) ease;
}

/* Menu text is always visible */

/* User Section */
.user-section {
  padding: 15px;
  border-top: 1px solid rgba(0,0,0,0.05);
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: auto; /* 将用户部分推到底部 */
  background-color: var(--background-white);
  position: sticky;
  bottom: 0;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px;
  border-radius: 10px;
  cursor: pointer;
  transition: all var(--transition-speed) ease;
}

.user-profile:hover {
  background-color: rgba(0,0,0,0.03);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--accent-light);
}

.user-info {
  display: flex;
  flex-direction: column;
  transition: opacity var(--transition-speed) ease;
}

/* User info is always visible */

.user-name {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
}

.user-status {
  font-size: 0.7rem;
  color: var(--text-secondary);
  white-space: nowrap;
  opacity: 0.7;
}

.auth-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 10px;
  width: 100%;
}

.auth-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  border-radius: 10px;
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  background-color: var(--background-light);
  border: 1px solid rgba(0,0,0,0.05);
  width: 100%;
}

.logout-button {
  color: #e74c3c;
}

.login-button {
  color: white;
  background-color: var(--primary-color);
  font-weight: bold;
}

.auth-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.logout-button:hover {
  background-color: rgba(231, 76, 60, 0.1);
}

.login-button:hover {
  background-color: var(--primary-dark);
}

/* Menu icon always has margin */

.auth-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
}

/* Auth icon container always has margin */

.auth-text {
  font-size: 1rem;
  font-weight: 500;
  white-space: nowrap;
  transition: opacity var(--transition-speed) ease;
}

/* Auth text is always visible */

/* Responsive - Always keep sidebar expanded */
@media (max-width: 768px) {
  .sidebar {
    width: 250px;
  }
}

/* 登出加载动画 */
.logout-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.logout-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(231, 76, 60, 0.2);
  border-radius: 50%;
  border-top-color: #e74c3c;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>
