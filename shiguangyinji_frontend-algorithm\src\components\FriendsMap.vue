<template>
  <div class="map-wrapper">
    <div id="custom-china-map" ref="mapContainer" class="w-full h-full"></div>
    <div class="map-mask"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import 'leaflet.markercluster'
import 'leaflet.markercluster/dist/MarkerCluster.css'
import 'leaflet.markercluster/dist/MarkerCluster.Default.css'
import coordtransform from 'coordtransform'

const props = defineProps({
  events: Array,
  highlightedProvinces: {
    type: Array,
    default: () => []
  }
})

const mapContainer = ref(null)
let map = null
let geoJsonLayers = {}
let currentMarkers = []
let provinceLabels = []
let cityMarkers = []
let cityPopupGroupIndex = {};
// 字体大小常量（用于省份标签）
const FONT_SIZE = '16px'
const threshold = 8 // 缩放阈值，决定何时显示城市标记
const highlightedProvinces = ref([])

// 坐标转换函数：将GeoJSON从GCJ-02（高德坐标系）转换为WGS84（地球坐标系）
const transformGeoJSON = (geoJSON) => {
  // 深拷贝GeoJSON以避免修改原始数据
  const transformed = JSON.parse(JSON.stringify(geoJSON))

  // 遍历所有要素
  transformed.features.forEach(feature => {
    // 处理多边形
    if (feature.geometry.type === 'Polygon') {
      feature.geometry.coordinates.forEach(ring => {
        for (let i = 0; i < ring.length; i++) {
          const [lng, lat] = ring[i]
          // 只在中国大陆范围内进行转换
          if (lng > 73 && lng < 136 && lat > 3 && lat < 54) {
            const converted = coordtransform.gcj02towgs84(lng, lat)
            ring[i] = converted
          }
        }
      })
    }
    // 处理多多边形
    else if (feature.geometry.type === 'MultiPolygon') {
      feature.geometry.coordinates.forEach(polygon => {
        polygon.forEach(ring => {
          for (let i = 0; i < ring.length; i++) {
            const [lng, lat] = ring[i]
            // 只在中国大陆范围内进行转换
            if (lng > 73 && lng < 136 && lat > 3 && lat < 54) {
              const converted = coordtransform.gcj02towgs84(lng, lat)
              ring[i] = converted
            }
          }
        })
      })
    }
  })

  return transformed
}

// 初始化地图
const initMap = () => {
  // 确保DOM已挂载
  if (!mapContainer.value) return

  map = L.map(mapContainer.value, {
    minZoom: 3,
    maxZoom: 8,  // 限制最大缩放级别，避免显示过多细节
    maxBounds: [[3.86, 73.66], [53.55, 135.05]],
    attributionControl: false
  }).setView([34.32, 108.55], 4)

  // 添加高德地图简化版瓦片作为底图
  L.tileLayer('https://webst0{s}.is.autonavi.com/appmaptile?style=7&x={x}&y={y}&z={z}', {
    subdomains: ['1', '2', '3', '4'],
    attribution: '高德地图'
  }).addTo(map)

  // 加载国界
  fetch(new URL('@/assets/map/country.json', import.meta.url))
    .then(res => res.json())
    .then(data => {
      // 转换坐标系
      const transformedData = transformGeoJSON(data)
      geoJsonLayers.country = L.geoJSON(transformedData, {
        style: {
          weight: 1.5,
          color: '#1e3a8a',
          fillOpacity: 0,
          className: 'glow-border',
        }
      }).addTo(map)
    })
    .catch(err => console.error('Error loading country GeoJSON:', err))

    map.on('zoomend', updateMapVisibility)

    map.on('popupopen', (e) => {
      bindCityPopupEvents(e.popup);
    });
}

// 查找某省或市的中心坐标
const getCenterFromLayer = (layerGroup, name) => {
  let center = null
  layerGroup.eachLayer(layer => {
    if (layer.feature && layer.feature.properties.name === name) {
      center = layer.getBounds().getCenter()
    }
  })
  return center
}

// 清除旧标记
const clearCurrentMarkers = () => {
  currentMarkers.forEach(marker => map.removeLayer(marker))
  currentMarkers = []
}

// 渲染标记
const renderByZoom = (zoom) => {
  clearCurrentMarkers()

  const eventMap = new Map()

  props.events.forEach(event => {
    const key = zoom < threshold ? event.province_location : event.city_location
    if (!eventMap.has(key)) eventMap.set(key, [])
    eventMap.get(key).push(event)
  })

  eventMap.forEach((events, locationName) => {
    const center = zoom < threshold
      ? getCenterFromLayer(geoJsonLayers.provinces, locationName)
      : getCenterFromLayer(geoJsonLayers.cities, locationName)
    if (center) {
      const marker = L.marker(center, {
        icon: L.icon({
          iconUrl: new URL('@/assets/images/marker-icon-red.png', import.meta.url).href,
          iconSize: [25, 41],
          iconAnchor: [12, 41],
          popupAnchor: [1, -34]
        })
      })

      marker.events = events;
      marker.currentIndex = 0;

      marker.bindPopup('', { autoClose: false });

      marker.on('popupopen', () => {
        // 设置弹窗内容
        marker.getPopup().setContent(createPopupContent(events, marker.currentIndex));

        // 获取弹窗容器
        const container = marker.getPopup().getElement();

        // 移除所有现有的点击事件监听器，防止重复绑定
        const oldHandler = container._clickHandler;
        if (oldHandler) {
          container.removeEventListener('click', oldHandler);
        }

        // 创建新的事件处理函数
        const newHandler = bindPopupNavigation(marker);
        container._clickHandler = newHandler;

        // 绑定新的点击事件监听器
        container.addEventListener('click', newHandler);
      });

      marker.addTo(map)
      currentMarkers.push(marker)
    }
  })
}

const applyProvinceHighlighting = () => {
  if (!geoJsonLayers.provinces) return

  geoJsonLayers.provinces.eachLayer((layer) => {
    const provinceName = layer.feature.properties.name
    if (highlightedProvinces.value.includes(provinceName)) {
      layer.setStyle({
        fillColor: '#3b82f6',
        fillOpacity: 0.3,      // 降低透明度以便看到底图
        weight: 1,
        color: '#1e3a8a'
      })
    } else {
      layer.setStyle({
        fillOpacity: 0,  // 未访问省份不填充颜色
        weight: 1,
        color: '#cccccc' // 浅灰色边界
      })
    }
  })
}

// 为多个事件生成分页弹窗内容
const createPopupContent = (events, currentIndex) => {
  const friendEventsMap = new Map();

  events.forEach(event => {
    if (!friendEventsMap.has(event.friend_name)) {
      friendEventsMap.set(event.friend_name, { event, count: 1 });
    } else {
      const existing = friendEventsMap.get(event.friend_name);
      existing.count += 1;
    }
  });

  // 只保留每个朋友的第一个事件
  const uniqueFriendEvents = Array.from(friendEventsMap.values(), data => data.event);

  const currentEvent = uniqueFriendEvents[currentIndex];
  const { friend_name, description, image } = currentEvent;

  const sameFriendEventsCount = friendEventsMap.get(friend_name).count;

  return `
    <div class="event-popup-wrapper my-custom-popup">
      <div class="event-popup">
        <div class="event-image">
          <img src="${image}" alt="事件图片" />
        </div>
        <div class="event-header">🧭 ${friend_name || '朋友姓名'}</div>
        <div class="event-divider"></div>
        <div class="event-body">
          📜 ${description}
        </div>
        <!-- 如果该朋友还有其他事件，则显示“更多”按钮 -->
        ${sameFriendEventsCount > 1 ? `<div class="more-events"><button>更多</button></div>` : ''}
        <div class="event-controls">
          <button class="event-prev">◀</button>
          <span class="event-index">${currentIndex + 1} / ${uniqueFriendEvents.length}</span>
          <button class="event-next">▶</button>
        </div>
      </div>
    </div>
  `;
};

const bindPopupNavigation = (marker) => {
  const handlePopupClick = (e) => {
    e.stopPropagation(); // 阻止事件冒泡

    const events = marker.events;
    let currentIndex = marker.currentIndex;

    if (e.target.classList.contains('event-prev')) {
      currentIndex = (currentIndex - 1 + events.length) % events.length;
    } else if (e.target.classList.contains('event-next')) {
      currentIndex = (currentIndex + 1) % events.length;
    }

    marker.currentIndex = currentIndex;
    marker.getPopup().setContent(createPopupContent(events, currentIndex));
    marker.openPopup();
  };

  return handlePopupClick;
};

const updateMapVisibility = () => {
  const zoom = map.getZoom()

  // 更新省份标签
  updateProvinceLabels(zoom)

  // 更新城市区域显示
  updateCityLayerVisibility(zoom)
}

// 更新省份标签
const updateProvinceLabels = (zoom) => {
  // 清除现有标签
  provinceLabels.forEach(label => {
    if (map.hasLayer(label)) {
      map.removeLayer(label)
    }
  })
  provinceLabels = []

  if (zoom >= threshold) return

  if (geoJsonLayers.provinces) {
    geoJsonLayers.provinces.eachLayer(layer => {
      const center = layer.getBounds().getCenter()
      const name = layer.feature.properties.name

      // 创建自定义HTML标签
      const label = L.divIcon({
        html: `<div class="province-label" style="font-size: ${FONT_SIZE}">${name}</div>`,
        className: 'custom-div-icon',
        iconSize: [80, 30],
        iconAnchor: [40, 15]
      })

      const marker = L.marker(center, { icon: label, interactive: false })
      marker.addTo(map)
      provinceLabels.push(marker)
    })
  }
}

// 城市弹窗相关函数
let attractionsData = null;

// 加载景点数据
const loadAttractionsData = async () => {
  if (!attractionsData) {
    try {
      const response = await fetch(new URL('@/assets/map/attractions_result.json', import.meta.url));
      attractionsData = await response.json();
      console.log('[FriendsMap] Attractions data loaded successfully');
    } catch (error) {
      console.error('[FriendsMap] Failed to load attractions data:', error);
      attractionsData = {};
    }
  }
  return attractionsData;
};

// 根据城市名称获取省份
const getProvinceFromCity = (cityName) => {
  if (!attractionsData) return null;

  for (const [province, cities] of Object.entries(attractionsData)) {
    if (cities[cityName]) {
      return province;
    }
  }
  return null;
};

const generateCityPopupContent = async (cityName) => {
  await loadAttractionsData();

  if (!(cityName in cityPopupGroupIndex)) cityPopupGroupIndex[cityName] = 0;
  const groupIndex = cityPopupGroupIndex[cityName];

  // 获取该城市的景点数据
  const province = getProvinceFromCity(cityName);
  let groups = [];

  if (province && attractionsData[province] && attractionsData[province][cityName]) {
    const cityAttractions = attractionsData[province][cityName];
    groups = cityAttractions.map(group =>
      group.map(attractionName => ({
        name: attractionName,
        desc: '', // 按要求设为空字符串
        img: new URL('@/assets/images/test.png', import.meta.url).href
      }))
    );
  }

  // 如果没有找到数据，使用默认数据
  if (groups.length === 0) {
    groups = [
      [
        { name: '景点 A', desc: '', img: new URL('@/assets/images/test.png', import.meta.url).href },
        { name: '景点 B', desc: '', img: new URL('@/assets/images/test.png', import.meta.url).href }
      ],
      [
        { name: '景点 C', desc: '', img: new URL('@/assets/images/test.png', import.meta.url).href },
        { name: '景点 D', desc: '', img: new URL('@/assets/images/test.png', import.meta.url).href }
      ]
    ];
  }

  const currentGroup = groups[groupIndex % groups.length];

  return `
    <div class="event-popup-wrapper my-custom-popup">
      <div class="city-popup">
        <h4 class="popup-title">📍 ${cityName}</h4>
        <div class="spot-container">
          ${currentGroup.map(spot => `
            <div class="spot-card">
              <img src="${spot.img}" alt="${spot.name}" class="spot-img" />
              <div class="spot-info">
                <div class="spot-name">${spot.name}</div>
                <div class="spot-desc">${spot.desc}</div>
              </div>
            </div>
          `).join('')}
        </div>
        <button class="change-btn" data-city="${cityName}">🔁 换一换</button>
      </div>
    </div>
  `;
};

const bindCityPopupEvents = (popup) => {
  const container = popup.getElement();
  if (!container) return;

  container.querySelectorAll('.change-btn').forEach(btn => {
    btn.addEventListener('click', async (ev) => {
      ev.stopPropagation();

      const city = ev.currentTarget.dataset.city;

      // 获取该城市的景点组数量
      await loadAttractionsData();
      const province = getProvinceFromCity(city);
      let maxGroups = 2; // 默认值

      if (province && attractionsData[province] && attractionsData[province][city]) {
        maxGroups = attractionsData[province][city].length;
      }

      cityPopupGroupIndex[city] = (cityPopupGroupIndex[city] + 1) % maxGroups;

      // 更新弹窗内容
      const newContent = await generateCityPopupContent(city);
      popup.setContent(newContent);

      // 等待 DOM 更新后再次绑定事件
      setTimeout(() => {
        popup.update();
        bindCityPopupEvents(popup);
      }, 0);
    });
  });
};

// 更新城市区域显示
const updateCityLayerVisibility = (zoom) => {
  // 清除现有城市标记（保留此代码以防有遗留的标记）
  cityMarkers.forEach(marker => {
    if (map.hasLayer(marker)) {
      map.removeLayer(marker)
    }
  })
  cityMarkers = []

  // 根据缩放级别决定是否显示城市图层
  if (zoom < threshold) {
    if (geoJsonLayers.cities && map.hasLayer(geoJsonLayers.cities)) {
      map.removeLayer(geoJsonLayers.cities);
    }
  } else {
    if (geoJsonLayers.cities && !map.hasLayer(geoJsonLayers.cities)) {
      geoJsonLayers.cities.addTo(map);
    }
  }
}

// 清理地图资源
const cleanupMap = () => {
  if (map) {
    // 清除所有标记
    clearCurrentMarkers();

    // 清除省份标签
    provinceLabels.forEach(label => {
      if (map.hasLayer(label)) {
        map.removeLayer(label);
      }
    });
    provinceLabels = [];

    // 清除城市标记
    cityMarkers.forEach(marker => {
      if (map.hasLayer(marker)) {
        map.removeLayer(marker);
      }
    });
    cityMarkers = [];

    // 清除所有图层
    Object.values(geoJsonLayers).forEach(layer => {
      if (layer && map.hasLayer(layer)) {
        map.removeLayer(layer);
      }
    });
    geoJsonLayers = {};

    // 移除所有事件监听器
    map.off();

    // 销毁地图实例
    map.remove();
    map = null;

    console.log('朋友地图资源已清理');
  }
};

// 地图加载后执行
onMounted(async () => {
  // 使用nextTick确保DOM已更新
  await nextTick()

  initMap()

  const provinceData = await (await fetch(new URL('@/assets/map/province.json', import.meta.url))).json()
  // 转换省份坐标系
  const transformedProvinceData = transformGeoJSON(provinceData)
  geoJsonLayers.provinces = L.geoJSON(transformedProvinceData, {
    style: {
      weight: 1,     // 细一点的边界线，与高德地图风格协调
      color: '#cccccc', // 浅灰色边界
      fillOpacity: 0    // 默认不填充
    },
    onEachFeature: (feature, layer) => layer.bindPopup(feature.properties.name)
  }).addTo(map)

  applyProvinceHighlighting()
  updateProvinceLabels(4)

  const cityData = await (await fetch(new URL('@/assets/map/city.json', import.meta.url))).json()
  // 转换城市坐标系
  const transformedCityData = transformGeoJSON(cityData)
  geoJsonLayers.cities = L.geoJSON(transformedCityData, {
    style: (feature) => {
      // 检查是否有事件发生在这个城市
      const cityHasEvents = props.events && props.events.some(event => event.city_location === feature.properties.name);

      return {
        weight: 1,
        color: cityHasEvents ? '#ff0000' : '#cccccc', // 有事件的城市边界为红色
        fillOpacity: 0,
        fillColor: '#ff0000' // 红色填充，与城市标记颜色一致
      };
    },
    onEachFeature: (feature, layer) => {
      const cityName = feature.properties.name;

      // 绑定鼠标悬停事件
      layer.on({
        mouseover: (e) => {
          const layer = e.target;
          layer.setStyle({
            fillOpacity: 0.3, // 悬停时显示填充色
            weight: 2,
            color: '#666'
          });

          // 将当前图层置于顶层
          layer.bringToFront();
        },
        mouseout: (e) => {
          const layer = e.target;
          // 检查是否有事件发生在这个城市
          const cityHasEvents = props.events && props.events.some(event => event.city_location === cityName);

          layer.setStyle({
            fillOpacity: 0,
            weight: 1,
            color: cityHasEvents ? '#ff0000' : '#cccccc' // 有事件的城市保持红色边界
          });
        },
        click: async (e) => {
          // 点击时显示弹窗
          const layer = e.target;
          const content = await generateCityPopupContent(cityName);
          layer.bindPopup(content).openPopup();
        }
      });
    }
  })

  renderByZoom(map.getZoom())

  map.on('zoomend', () => {
    renderByZoom(map.getZoom())
  })
})

// 在组件卸载前清理资源
onBeforeUnmount(() => {
  cleanupMap();
})

// 监听数据变化时重新渲染
watch(() => props.events, () => {
  if (map) {
    // 重新渲染标记
    renderByZoom(map.getZoom())

    // 更新城市图层样式以反映新的事件数据
    if (geoJsonLayers.cities) {
      geoJsonLayers.cities.eachLayer(layer => {
        const cityName = layer.feature.properties.name;
        const cityHasEvents = props.events && props.events.some(event => event.city_location === cityName);

        layer.setStyle({
          color: cityHasEvents ? '#ff0000' : '#cccccc'
        });
      });
    }
  }
})

watch(
  () => props.highlightedProvinces,
  (newVal) => {
    highlightedProvinces.value = newVal || []
    applyProvinceHighlighting()
  },
  { immediate: true }
)
</script>

<style scoped>
.map-wrapper {
  width: 100%;
  height: 600px;
  background-color: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  position: relative;
  z-index: 1;
}

/* 确保地图容器有明确的高度 */
#custom-china-map {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 0 20px rgba(0, 0, 0, 0.2));
  border-radius: 20px;
  background: white;
  box-shadow: 0 0 30px 15px rgba(0, 0, 0, 0.1);
}

:deep(.glow-border path) {
  filter: drop-shadow(0 0 6px rgba(0, 102, 255, 0.7));
}

:deep(.leaflet-div-icon.custom-div-icon) {
  background: transparent !important;
  border: none !important;
}

:deep(.province-label) {
  color: white;
  text-shadow:
    -1px -1px 0 #000,
     1px -1px 0 #000,
    -1px  1px 0 #000,
     1px  1px 0 #000;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  width: 80px;
  pointer-events: none;
  user-select: none;
  background-color: rgba(0, 0, 0, 0.2);
  padding: 2px 4px;
  border-radius: 4px;
}

:deep(.custom-city-icon) {
  background: transparent;
  border: none;
}

:deep(.city-button-wrapper) {
  display: flex;
  flex-direction: column;
  align-items: center;
  pointer-events: auto; /* 保证点击事件有效 */
}

:deep(.city-button) {
  position: relative;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

:deep(.city-button.red) {
  background-color: rgba(255, 0, 0, 0.2); /* 红色外圈 */
}

:deep(.city-button::before) {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

:deep(.city-button.red::before) {
  background-color: #ff0000; /* 红色中心点 */
  box-shadow: 0 0 4px rgba(255, 0, 0, 0.6);
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.6);
    opacity: 0.2;
  }
  100% {
    transform: scale(1);
    opacity: 0.6;
  }
}

:deep(.city-label-below) {
  margin-top: 2px;
  font-size: 12px;
  color: #333;
  background: rgba(255, 255, 255, 0.8);
  padding: 1px 4px;
  border-radius: 3px;
  white-space: nowrap;
}

:deep(.city-popup) {
  font-family: Arial, sans-serif;
  width: 300px;
  padding: 10px;
  background: #f3d47a;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0,0,0,0.1);
}

:deep(.popup-title) {
  margin: 0 0 10px 0;
  font-size: 18px;
  font-weight: bold;
  color: #2c3e50;
}

:deep(.spot-container) {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

:deep(.spot-card) {
  background: #f8f8f8;
  border-radius: 8px;
  flex: 1;
  padding: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);
  transition: transform 0.2s;
}

:deep(.spot-card:hover) {
  transform: translateY(-2px);
}

:deep(.spot-img) {
  width: 100%;
  height: 70px;
  object-fit: cover;
  border-radius: 6px;
  margin-bottom: 6px;
}

:deep(.spot-info) {
  text-align: center;
}

:deep(.spot-name) {
  font-size: 14px;
  font-weight: bold;
  color: #333;
}

:deep(.spot-desc) {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

:deep(.change-btn) {
  display: block;
  width: 100%;
  padding: 6px;
  background-color: #e74c3c;
  color: #fff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

:deep(.change-btn:hover) {
  background-color: #c0392b;
}

/* 城市区域悬停效果 */
:deep(.leaflet-interactive) {
  transition: fill-opacity 0.2s ease;
  cursor: pointer;
}

:deep(.event-popup) {
  font-family: 'Arial', sans-serif;
  width: 280px;
  background-color: white;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
  animation: popup-fade-in 0.3s ease-out;
}

:deep(.event-header) {
  font-weight: 700;
  font-size: 18px;
  text-align: center;
  margin-bottom: 10px;
  color: #1e3a8a;
}

:deep(.event-divider) {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #10b981);
  margin: 10px 0 14px;
  border-radius: 2px;
}

:deep(.event-body) {
  max-height: 100px;
  overflow-y: auto;
  font-size: 15px;
  line-height: 1.6;
  margin-bottom: 14px;
  padding-right: 6px;
  color: #4b5563;
}

:deep(.event-controls) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

:deep(.event-controls button) {
  background-color: #f0f9ff;
  border: none;
  padding: 6px 12px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #3b82f6;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

:deep(.event-controls button:hover) {
  background-color: #dbeafe;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

:deep(.event-index) {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

:deep(.event-image) {
  width: 100%;
  height: 160px;
  overflow: hidden;
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  position: relative;
}

:deep(.event-image::after) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(0deg, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0) 50%);
  border-radius: 12px;
}

:deep(.event-image img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  border-radius: 12px;
  transition: transform 0.3s ease;
}

:deep(.event-image:hover img) {
  transform: scale(1.05);
}

:deep(.more-events) {
  text-align: center;
  margin: 10px 0;
}

:deep(.more-events button) {
  background-color: #f0f9ff;
  border: none;
  padding: 6px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #3b82f6;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  font-weight: 500;
}

:deep(.more-events button:hover) {
  background-color: #dbeafe;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

@keyframes popup-fade-in {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

:deep(.leaflet-popup-close-button) {
  color: #fff;
  background-color: transparent;
  border-radius: 4px;
  font-weight: bold;
  transition: all 0.3s ease;
  font-size: 18px;
}

:deep(.leaflet-popup-close-button:hover) {
  background-color: #d32f2f;
  color: #fff;
  cursor: pointer;
}

:deep(.leaflet-popup-content-wrapper:has(.my-custom-popup)) {
  background-color: #f5f5dc !important;
  border-radius: 12px;
}
</style>