<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="51">
            <item index="0" class="java.lang.String" itemvalue="scikit-image" />
            <item index="1" class="java.lang.String" itemvalue="shapely" />
            <item index="2" class="java.lang.String" itemvalue="Automat" />
            <item index="3" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="4" class="java.lang.String" itemvalue="xformers" />
            <item index="5" class="java.lang.String" itemvalue="srsly" />
            <item index="6" class="java.lang.String" itemvalue="bitarray" />
            <item index="7" class="java.lang.String" itemvalue="nvidia-nccl-cu12" />
            <item index="8" class="java.lang.String" itemvalue="frozenlist" />
            <item index="9" class="java.lang.String" itemvalue="fsspec" />
            <item index="10" class="java.lang.String" itemvalue="mkl-fft" />
            <item index="11" class="java.lang.String" itemvalue="spacy" />
            <item index="12" class="java.lang.String" itemvalue="PyWavelets" />
            <item index="13" class="java.lang.String" itemvalue="pyzmq" />
            <item index="14" class="java.lang.String" itemvalue="safetensors" />
            <item index="15" class="java.lang.String" itemvalue="lxml" />
            <item index="16" class="java.lang.String" itemvalue="blis" />
            <item index="17" class="java.lang.String" itemvalue="sympy" />
            <item index="18" class="java.lang.String" itemvalue="pyarrow" />
            <item index="19" class="java.lang.String" itemvalue="lazy_loader" />
            <item index="20" class="java.lang.String" itemvalue="scipy" />
            <item index="21" class="java.lang.String" itemvalue="transformers" />
            <item index="22" class="java.lang.String" itemvalue="triton" />
            <item index="23" class="java.lang.String" itemvalue="salesforce-lavis" />
            <item index="24" class="java.lang.String" itemvalue="streamlit" />
            <item index="25" class="java.lang.String" itemvalue="flash-attn" />
            <item index="26" class="java.lang.String" itemvalue="incremental" />
            <item index="27" class="java.lang.String" itemvalue="Hydra" />
            <item index="28" class="java.lang.String" itemvalue="contourpy" />
            <item index="29" class="java.lang.String" itemvalue="imageio" />
            <item index="30" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="31" class="java.lang.String" itemvalue="aiohttp" />
            <item index="32" class="java.lang.String" itemvalue="distlib" />
            <item index="33" class="java.lang.String" itemvalue="thinc" />
            <item index="34" class="java.lang.String" itemvalue="grpcio" />
            <item index="35" class="java.lang.String" itemvalue="Pillow" />
            <item index="36" class="java.lang.String" itemvalue="pandas" />
            <item index="37" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="38" class="java.lang.String" itemvalue="importlib-metadata" />
            <item index="39" class="java.lang.String" itemvalue="pycocotools" />
            <item index="40" class="java.lang.String" itemvalue="catalogue" />
            <item index="41" class="java.lang.String" itemvalue="protobuf" />
            <item index="42" class="java.lang.String" itemvalue="rsa" />
            <item index="43" class="java.lang.String" itemvalue="google-auth-oauthlib" />
            <item index="44" class="java.lang.String" itemvalue="tabulate" />
            <item index="45" class="java.lang.String" itemvalue="mdurl" />
            <item index="46" class="java.lang.String" itemvalue="oauthlib" />
            <item index="47" class="java.lang.String" itemvalue="einops" />
            <item index="48" class="java.lang.String" itemvalue="aiosignal" />
            <item index="49" class="java.lang.String" itemvalue="onnxruntime-gpu" />
            <item index="50" class="java.lang.String" itemvalue="gradio" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>