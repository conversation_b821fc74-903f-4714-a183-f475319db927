import service from '@/utils/request'

/**
 * 获取用户的视频列表
 * @returns {Promise} 视频列表
 */
export function getVideoList() {
  return service({
    url: '/video/',
    method: 'get'
  })
}

/**
 * 获取特定视频的详情
 * @param {number} videoId 视频ID
 * @returns {Promise} 视频详情
 */
export function getVideoDetail(videoId) {
  return service({
    url: '/video/',
    method: 'get',
    params: {
      video_id: videoId
    }
  })
}

/**
 * 创建新视频
 * @param {Object} data 视频数据
 * @param {Array} data.pictures 图片ID列表
 * @param {string} data.title 视频标题
 * @param {string} data.description 视频描述
 * @returns {Promise} 创建结果
 */
export function createVideo(data) {
  return service({
    url: '/video/',
    method: 'post',
    data: data
  })
}

/**
 * 更新视频（覆盖）
 * @param {Object} data 视频数据
 * @param {number} data.video_id 要覆盖的视频ID
 * @param {Array} data.pictures 图片ID列表
 * @param {string} data.title 视频标题
 * @param {string} data.description 视频描述
 * @returns {Promise} 更新结果
 */
export function updateVideo(data) {
  return service({
    url: '/video/',
    method: 'post',
    data: data
  })
}

/**
 * 删除视频
 * @param {number} videoId 视频ID
 * @returns {Promise} 删除结果
 */
export function deleteVideo(videoId) {
  return service({
    url: '/video/',
    method: 'delete',
    data: {
      video_id: videoId
    }
  })
}

/**
 * 查询视频生成状态
 * @param {number} videoId 视频ID
 * @returns {Promise} 状态信息
 */
export function getVideoStatus(videoId) {
  return service({
    url: '/video/status/',
    method: 'get',
    params: {
      video_id: videoId
    }
  })
}

/**
 * 保存视频（确认生成完成）
 * @param {number} videoId 视频ID
 * @returns {Promise} 保存结果
 */
export function saveVideo(videoId) {
  return service({
    url: '/video/save/',
    method: 'post',
    data: {
      video_id: videoId
    }
  })
}

/**
 * 搜索视频
 * @param {Object} params 搜索参数
 * @param {string} params.keyword 关键词
 * @param {string} params.startDate 开始日期
 * @param {string} params.endDate 结束日期
 * @returns {Promise} 搜索结果
 */
export function searchVideos(params) {
  return service({
    url: '/video/',
    method: 'get',
    params: params
  })
}

// 模拟数据（用于离线模式）
const mockVideos = [
  {
    id: 1,
    video_id: 1,
    title: '黄山游玩日记',
    description: '记录了和朋友们一同前往黄山，留下的美好时光。',
    date: '2024-10-15T10:30:00Z',
    upload_at: '2024-10-13T08:00:00Z',
    completed_at: '2024-10-15T10:30:00Z',
    started_at: '2024-10-15T09:00:00Z',
    thumbnail: new URL('@/assets/example/huangshan1.png', import.meta.url).href,
    videoUrl: 'https://sample-videos.com/video123/mp4/720/big_buck_bunny_720p_1mb.mp4',
    status: 'completed',
    progress: 100,
    event_title: [
      { id: 1, title: '乘火车前往', date: '2024-10-13' },
      { id: 2, title: '勇攀高峰', date: '2024-10-14' },
      { id: 3, title: '观看日出', date: '2024-10-15' }
    ],
    events: [
      { id: 1, title: '乘火车前往', date: '2024-10-13' },
      { id: 2, title: '勇攀高峰', date: '2024-10-14' },
      { id: 3, title: '观看日出', date: '2024-10-15' }
    ]
  },
  {
    id: 2,
    video_id: 2,
    title: '上海之行',
    description: '上海的繁华与现代感让人印象深刻，外滩的夜景尤为美丽。',
    date: '2024-08-20T15:45:00Z',
    upload_at: '2024-08-18T07:30:00Z',
    completed_at: '2024-08-20T15:45:00Z',
    started_at: '2024-08-20T14:30:00Z',
    thumbnail: 'https://picsum.photos/id/1015/800/450',
    videoUrl: 'https://sample-videos.com/video123/mp4/720/big_buck_bunny_720p_1mb.mp4',
    status: 'completed',
    progress: 100,
    event_title: [
      { id: 1, title: '外滩夜游', date: '2024-08-18' },
      { id: 2, title: '城隍庙品小吃', date: '2024-08-19' },
      { id: 3, title: '豫园漫步', date: '2024-08-20' }
    ],
    events: [
      { id: 1, title: '外滩夜游', date: '2024-08-18' },
      { id: 2, title: '城隍庙品小吃', date: '2024-08-19' },
      { id: 3, title: '豫园漫步', date: '2024-08-20' }
    ]
  },
  {
    id: 3,
    video_id: 3,
    title: '广州美食之旅',
    description: '广州的美食文化丰富多彩，从早茶到夜宵，处处是美味。',
    date: '2024-06-10T12:20:00Z',
    upload_at: '2024-06-08T06:00:00Z',
    completed_at: '2024-06-10T12:20:00Z',
    started_at: '2024-06-10T11:00:00Z',
    thumbnail: 'https://picsum.photos/id/1080/800/450',
    videoUrl: 'https://sample-videos.com/video123/mp4/720/big_buck_bunny_720p_1mb.mp4',
    status: 'completed',
    progress: 100,
    event_title: [
      { id: 1, title: '早茶体验', date: '2024-06-08' },
      { id: 2, title: '沙面岛漫步', date: '2024-06-09' },
      { id: 3, title: '珠江夜游', date: '2024-06-10' }
    ],
    events: [
      { id: 1, title: '早茶体验', date: '2024-06-08' },
      { id: 2, title: '沙面岛漫步', date: '2024-06-09' },
      { id: 3, title: '珠江夜游', date: '2024-06-10' }
    ]
  },
  {
    id: 4,
    video_id: 4,
    title: '西湖春游记',
    description: '春天的西湖，柳絮飞舞，荷花初绽，美不胜收。',
    date: '2024-04-15T09:15:00Z',
    upload_at: '2024-04-14T05:30:00Z',
    completed_at: '2024-04-15T09:15:00Z',
    started_at: '2024-04-15T08:00:00Z',
    thumbnail: 'https://picsum.photos/id/1018/800/450',
    videoUrl: 'https://sample-videos.com/video123/mp4/720/big_buck_bunny_720p_1mb.mp4',
    status: 'completed',
    progress: 100,
    event_title: [
      { id: 1, title: '断桥残雪', date: '2024-04-14' },
      { id: 2, title: '苏堤春晓', date: '2024-04-15' },
      { id: 3, title: '雷峰塔登顶', date: '2024-04-15' }
    ],
    events: [
      { id: 1, title: '断桥残雪', date: '2024-04-14' },
      { id: 2, title: '苏堤春晓', date: '2024-04-15' },
      { id: 3, title: '雷峰塔登顶', date: '2024-04-15' }
    ]
  }
]

/**
 * 获取模拟视频数据（离线模式）
 * @returns {Promise} 模拟视频列表
 */
export function getMockVideoList() {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        data: {
          videos: mockVideos
        }
      })
    }, 500)
  })
}

/**
 * 检查后端连接状态
 * @returns {Promise<boolean>} 是否连接成功
 */
export async function checkBackendConnection() {
  try {
    const response = await service({
      url: '/video/',
      method: 'get',
      timeout: 3000
    })
    return true
  } catch (error) {
    console.warn('后端连接失败，将使用模拟数据:', error.message)
    return false
  }
}

/**
 * 智能获取视频列表（自动检测后端状态）
 * @returns {Promise} 视频列表
 */
export async function getVideoListSmart() {
  try {
    // 首先尝试连接后端
    const isBackendAvailable = await checkBackendConnection()

    if (isBackendAvailable) {
      console.log('✅ 使用后端数据')
      return await getVideoList()
    } else {
      console.log('⚠️ 后端不可用，使用模拟数据')
      return await getMockVideoList()
    }
  } catch (error) {
    console.warn('获取视频列表失败，使用模拟数据:', error.message)
    return await getMockVideoList()
  }
}

export default {
  getVideoList,
  getVideoDetail,
  createVideo,
  updateVideo,
  deleteVideo,
  getVideoStatus,
  saveVideo,
  searchVideos,
  getMockVideoList,
  checkBackendConnection,
  getVideoListSmart
}
