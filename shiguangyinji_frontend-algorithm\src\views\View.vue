<script setup>
import SideBar from "@/components/SideBar.vue";
</script>

<template>
  <div class="layout">
    <div class="sidebar-container">
      <SideBar />
    </div>
    <div class="content-container">
      <div class="content-wrapper">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<style scoped>
.layout {
  display: flex;
  min-height: 100vh;
  width: 100%;
  position: relative;
}

.sidebar-container {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 250px; /* 保持固定宽度 */
  z-index: 100;
  /* 移除宽度过渡效果 */
  /* transition: width var(--transition-speed) ease; */
}

.content-container {
  flex-grow: 1;
  padding: 20px;
  margin-left: 250px; /* 保持固定的左边距 */
  /* 移除左边距过渡效果 */
  /* transition: margin-left var(--transition-speed) ease; */
  min-height: 100vh;
  background-color: var(--background-light);
  width: calc(100% - 250px);
  overflow-x: hidden;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

@media (max-width: 768px) {
  .sidebar-container {
    width: 250px;
  }

  .content-container {
    margin-left: 250px;
    width: calc(100% - 250px);
  }

  .content-wrapper {
    padding: 0 10px;
  }
}
</style>
