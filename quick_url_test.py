#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速URL测试 - 验证路由配置
"""

import sys
import os

# 添加Django项目路径
sys.path.append('shiguangyinji_backend-algorithm')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shiguangyinji.settings')

import django
django.setup()

from django.urls import reverse, resolve
from django.test import RequestFactory
from video.views import VideoManagement, VideoStatusView, VideoSaveView

def test_url_patterns():
    """测试URL模式配置"""
    print("测试URL模式配置...")
    print("=" * 50)
    
    # 测试URL反向解析
    url_tests = [
        ('video_management', '/api/video/'),
        ('video_create', '/api/video/create/'),
        ('video_status', '/api/video/status/'),
        ('video_save', '/api/video/save/'),
    ]
    
    for name, expected_url in url_tests:
        try:
            url = reverse(name)
            print(f"✅ {name:15} → {url}")
            if url == expected_url:
                print(f"   ✓ URL正确: {expected_url}")
            else:
                print(f"   ⚠ URL不匹配: 期望 {expected_url}, 实际 {url}")
        except Exception as e:
            print(f"❌ {name:15} → 错误: {e}")
    
    print()

def test_url_resolution():
    """测试URL解析"""
    print("测试URL解析...")
    print("=" * 50)
    
    # 测试URL解析
    resolution_tests = [
        '/api/video/',
        '/api/video/create/',
        '/api/video/status/',
        '/api/video/save/',
    ]
    
    for url in resolution_tests:
        try:
            match = resolve(url)
            view_name = match.view_class.__name__ if hasattr(match, 'view_class') else str(match.func)
            print(f"✅ {url:20} → {view_name}")
        except Exception as e:
            print(f"❌ {url:20} → 错误: {e}")
    
    print()

def test_view_methods():
    """测试视图方法"""
    print("测试视图方法...")
    print("=" * 50)
    
    factory = RequestFactory()
    
    # 测试VideoManagement视图
    print("VideoManagement视图:")
    view = VideoManagement()
    methods = ['get', 'post', 'delete']
    for method in methods:
        if hasattr(view, method):
            print(f"  ✅ {method.upper()} 方法存在")
        else:
            print(f"  ❌ {method.upper()} 方法不存在")
    
    # 测试VideoStatusView视图
    print("\nVideoStatusView视图:")
    view = VideoStatusView()
    if hasattr(view, 'get'):
        print("  ✅ GET 方法存在")
    else:
        print("  ❌ GET 方法不存在")
    
    # 测试VideoSaveView视图
    print("\nVideoSaveView视图:")
    view = VideoSaveView()
    if hasattr(view, 'post'):
        print("  ✅ POST 方法存在")
    else:
        print("  ❌ POST 方法不存在")
    
    print()

def show_fix_summary():
    """显示修复总结"""
    print("修复总结:")
    print("=" * 50)
    print("问题: 前端调用 /api/video/create/ 返回 404 错误")
    print()
    print("原因分析:")
    print("  - 旧的URL配置只有 path('create/', ...)")
    print("  - 新的URL配置改为 path('', ...)")
    print("  - 前端仍在使用旧路径 /api/video/create/")
    print()
    print("解决方案:")
    print("  ✅ 保留新的主要端点: path('', VideoManagement.as_view())")
    print("  ✅ 添加兼容端点: path('create/', VideoManagement.as_view())")
    print("  ✅ 保持向后兼容性，前端无需立即修改")
    print()
    print("当前可用的URL:")
    print("  POST   /api/video/         - 推荐使用")
    print("  POST   /api/video/create/  - 兼容旧版")
    print("  GET    /api/video/status/  - 状态查询")
    print("  POST   /api/video/save/    - 保存确认")
    print()
    print("前端迁移建议:")
    print("  1. 当前可继续使用 /api/video/create/")
    print("  2. 逐步迁移到异步API")
    print("  3. 最终统一使用 /api/video/")

if __name__ == "__main__":
    print("Django URL配置测试")
    print("=" * 60)
    
    try:
        # 测试URL模式
        test_url_patterns()
        
        # 测试URL解析
        test_url_resolution()
        
        # 测试视图方法
        test_view_methods()
        
        # 显示修复总结
        show_fix_summary()
        
        print("\n" + "=" * 60)
        print("✅ URL配置测试完成!")
        print("现在前端应该可以正常调用 /api/video/create/ 了")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
