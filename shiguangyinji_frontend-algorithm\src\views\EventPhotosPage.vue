<template>
  <div class="event-photos-page">
    <!-- 页面头部 -->
    <header class="page-header">
      <button class="back-btn" @click="goBack">
        <span class="back-icon">←</span>
        <span>返回</span>
      </button>
      <div class="header-info">
        <h1 class="event-title">{{ event.title }}</h1>
        <p class="event-meta">{{ formatDate(event.date) }} · {{ event.location }}</p>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 图片展示区域 -->
      <div class="image-section">
        <div class="image-container" v-if="currentImage">
          <!-- 图片导航按钮 -->
          <button
            class="nav-btn prev-btn"
            @click="prevImage"
            :disabled="currentImageIndex === 0"
            v-if="images.length > 1"
          >
            <span class="nav-icon">‹</span>
          </button>

          <!-- 主图片 -->
          <div class="image-wrapper" @touchstart="handleTouchStart" @touchend="handleTouchEnd">
            <!-- 图片加载指示器 -->
            <div v-if="imageLoading" class="image-loading">
              <div class="loading-spinner"></div>
              <p>加载中...</p>
            </div>

            <img
              :src="currentImage.url"
              :alt="currentImage.description"
              class="main-image"
              @load="onImageLoad"
              @error="onImageError"
              :style="{ opacity: imageLoading ? 0 : 1 }"
            />

            <!-- 人物标注点 -->
            <div
              v-for="person in currentImage.annotations"
              :key="person.id"
              class="annotation-point"
              :style="{
                left: person.x + '%',
                top: person.y + '%'
              }"
              @click="selectPerson(person)"
              :class="{ 'active': selectedPerson && selectedPerson.id === person.id }"
            >
              <div class="point-marker"></div>
              <div class="person-tooltip" v-if="selectedPerson && selectedPerson.id === person.id">
                {{ person.name || '未知' }}
              </div>
            </div>
          </div>

          <!-- 图片导航按钮 -->
          <button
            class="nav-btn next-btn"
            @click="nextImage"
            :disabled="currentImageIndex === images.length - 1"
            v-if="images.length > 1"
          >
            <span class="nav-icon">›</span>
          </button>
        </div>

        <!-- 图片指示器 -->
        <div class="image-indicators" v-if="images.length > 1">
          <span
            v-for="(_, index) in images"
            :key="index"
            class="indicator"
            :class="{ 'active': index === currentImageIndex }"
            @click="goToImage(index)"
          ></span>
        </div>

        <!-- 图片信息 -->
        <div class="image-info">
          <div class="image-description">
            <h3>图片描述</h3>
            <p>{{ currentImage?.description || '暂无描述' }}</p>
          </div>
          <div class="image-location" v-if="currentImage?.location">
            <h3>拍摄地点</h3>
            <p>
              <span class="location-icon">📍</span>
              {{ currentImage.location }}
            </p>
          </div>
        </div>
      </div>

      <!-- 人物识别卡片区域 -->
      <div class="people-section">
        <div class="section-header">
          <h2>识别到的人物</h2>
          <span class="people-count">{{ currentImage?.annotations?.length || 0 }} 人</span>
        </div>

        <div class="people-cards" v-if="currentImage?.annotations?.length > 0">
          <div
            v-for="person in currentImage.annotations"
            :key="person.id"
            class="person-card"
            :class="{ 'selected': selectedPerson && selectedPerson.id === person.id }"
            @click="selectPerson(person)"
          >
            <div class="person-avatar">
              <img :src="person.avatar" :alt="person.name || '未知'" />
              <div
                v-if="editingPerson && editingPerson.id === person.id"
                class="avatar-upload-overlay"
                @click="triggerAvatarUpload(person)"
              >
                <span class="upload-icon">📷</span>
                <span class="upload-text">上传头像</span>
              </div>
            </div>
            <div class="person-info">
              <div class="person-name">
                <input
                  v-if="editingPerson && editingPerson.id === person.id"
                  v-model="editingName"
                  @blur="saveName(person)"
                  @keyup.enter="saveName(person)"
                  class="name-input"
                  placeholder="输入姓名"
                />
                <span v-else @click="editName(person)" class="name-text">
                  {{ person.name || '未知' }}
                </span>
              </div>
              <div class="person-relationship">
                <input
                  v-if="editingPerson && editingPerson.id === person.id"
                  v-model="editingRelationship"
                  @blur="saveRelationship(person)"
                  @keyup.enter="saveRelationship(person)"
                  class="relationship-input"
                  placeholder="请输入关系"
                  maxlength="20"
                />
                <span v-else @click="editRelationship(person)" class="relationship-text">
                  {{ person.relationship || '未知关系' }}
                </span>
              </div>
            </div>
            <div class="person-actions">
              <button
                class="edit-btn"
                @click.stop="editPerson(person)"
                v-if="!editingPerson || editingPerson.id !== person.id"
              >
                编辑
              </button>
              <button
                class="save-btn"
                @click.stop="savePerson(person)"
                v-if="editingPerson && editingPerson.id === person.id"
              >
                保存
              </button>
            </div>
          </div>
        </div>

        <div class="no-people" v-else>
          <div class="no-people-icon">👤</div>
          <p>当前图片中未识别到人物</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getEventPhotos, convertFaceDataToAnnotations } from '@/api/eventPhotosApi.js'
import { createCharacter, updateCharacter } from '@/api/characterApi.js'

const route = useRoute();
const router = useRouter();

// 事件数据
const event = ref({
  id: null,
  title: '',
  date: '',
  location: '',
  description: ''
});

// 图片数据
const images = ref([]);
const currentImageIndex = ref(0);
const selectedPerson = ref(null);
const editingPerson = ref(null);
const editingName = ref('');
const editingRelationship = ref('');
const uploadedAvatar = ref(null);

// 图片加载状态
const imageLoading = ref(false);

// 触摸相关
const touchStartX = ref(0);

// 计算属性
const currentImage = computed(() => {
  return images.value[currentImageIndex.value] || null;
});

// 方法
const goBack = () => {
  router.go(-1);
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  return `${year}年${month}月${day}日`;
};

const prevImage = () => {
  if (currentImageIndex.value > 0) {
    imageLoading.value = true;
    currentImageIndex.value--;
    selectedPerson.value = null;
  }
};

const nextImage = () => {
  if (currentImageIndex.value < images.value.length - 1) {
    imageLoading.value = true;
    currentImageIndex.value++;
    selectedPerson.value = null;
  }
};

const goToImage = (index) => {
  imageLoading.value = true;
  currentImageIndex.value = index;
  selectedPerson.value = null;
};

const selectPerson = (person) => {
  selectedPerson.value = selectedPerson.value?.id === person.id ? null : person;
};

const editPerson = (person) => {
  editingPerson.value = person;
  editingName.value = person.name || '';
  editingRelationship.value = person.relationship || '';
  uploadedAvatar.value = null; // 重置上传的头像
};

const editName = (person) => {
  editPerson(person);
};

const editRelationship = (person) => {
  editPerson(person);
};

const saveName = (person) => {
  person.name = editingName.value;
  editingPerson.value = null;
};

const saveRelationship = (person) => {
  person.relationship = editingRelationship.value;
};

const savePerson = async (person) => {
  try {
    // 验证必填字段
    if (!editingName.value || !editingRelationship.value) {
      alert('姓名和关系为必填项');
      return;
    }

    // 如果是新建人物，必须上传头像
    if (!person.character_id && !uploadedAvatar.value) {
      alert('请上传头像');
      return;
    }

    const characterData = {
      name: editingName.value,
      relationship: editingRelationship.value,
      gender: 'male' // 可以添加性别选择
    };

    // 只在有新头像时才包含avatar字段
    if (uploadedAvatar.value) {
      characterData.avatar = uploadedAvatar.value;
    }

    let result;
    if (person.character_id) {
      // 更新已有character
      result = await updateCharacter(person.character_id, characterData);
    } else {
      // 创建新character
      result = await createCharacter(characterData);
      person.character_id = result.character_id;
    }

    // 更新本地数据
    person.name = editingName.value;
    person.relationship = editingRelationship.value;

    // 重置编辑状态
    editingPerson.value = null;
    uploadedAvatar.value = null;

    console.log('保存人物信息成功:', result);
    alert('保存成功！');

  } catch (error) {
    console.error('保存人物信息失败:', error);
    alert('保存失败：' + (error.response?.data?.message || error.message));
  }
};

// 触发头像上传
const triggerAvatarUpload = (person) => {
  // 创建一个临时的文件输入元素
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'image/*';
  input.onchange = (event) => handleAvatarUpload(event, person);
  input.click();
};

// 处理头像上传
const handleAvatarUpload = (event, person) => {
  const file = event.target.files[0];
  if (file) {
    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      alert('请选择图片文件');
      return;
    }

    // 验证文件大小（限制为5MB）
    if (file.size > 5 * 1024 * 1024) {
      alert('图片大小不能超过5MB');
      return;
    }

    uploadedAvatar.value = file;

    // 预览头像
    const reader = new FileReader();
    reader.onload = (e) => {
      person.avatar = e.target.result;
    };
    reader.readAsDataURL(file);

    console.log('头像已选择:', file.name);
  }
};

const onImageLoad = () => {
  // 图片加载完成后隐藏加载指示器
  imageLoading.value = false;
};

const onImageError = () => {
  // 图片加载失败时也隐藏加载指示器
  imageLoading.value = false;
  console.error('图片加载失败');
};

// 触摸事件处理
const handleTouchStart = (e) => {
  touchStartX.value = e.touches[0].clientX;
};

const handleTouchEnd = (e) => {
  const touchEndX = e.changedTouches[0].clientX;
  const diff = touchStartX.value - touchEndX;

  if (Math.abs(diff) > 50) { // 滑动距离超过50px
    if (diff > 0) {
      nextImage(); // 向左滑动，下一张
    } else {
      prevImage(); // 向右滑动，上一张
    }
  }
};

// 键盘事件处理
const handleKeydown = (e) => {
  if (e.key === 'ArrowLeft') {
    prevImage();
  } else if (e.key === 'ArrowRight') {
    nextImage();
  }
};

// 加载事件数据
const loadEventData = async () => {
  const eventId = route.params.id;

  try {
    // 从后端获取事件数据
    const response = await getEventPhotos(eventId);
    const data = response.data;

    // 设置事件基本信息
    event.value = {
      id: data.issue_id,
      title: data.title,
      date: data.date,
      location: data.location,
      description: data.description
    };

    // 处理图片数据
    const processedImages = [];

    for (const picture of data.pictures) {
      try {
        // 构建完整的图片URL
        const imageUrl = picture.url.startsWith('http')
          ? picture.url
          : `http://127.0.0.1:8000${picture.url}`;

        // 转换face_data为annotations
        const annotations = await convertFaceDataToAnnotations(picture.face_data, imageUrl);

        const imageData = {
          id: picture.picture_id,
          url: imageUrl,
          description: picture.description || '暂无描述',
          location: picture.location || data.location,
          annotations: annotations
        };

        processedImages.push(imageData);
      } catch (error) {
        console.error('处理图片数据失败:', error);
        // 即使处理失败也添加基本图片信息
        processedImages.push({
          id: picture.picture_id,
          url: picture.url.startsWith('http') ? picture.url : `http://127.0.0.1:8000${picture.url}`,
          description: picture.description || '暂无描述',
          location: picture.location || data.location,
          annotations: []
        });
      }
    }

    images.value = processedImages;

  } catch (error) {
    console.error('加载事件数据失败:', error);

    // 如果后端请求失败，使用模拟数据
    event.value = {
      id: eventId,
      title: '勇攀高峰',
      date: '2022-09-02',
      location: '安徽省-黄山市',
      description: '和朋友们一起攀登黄山'
    };

    // 模拟图片数据
    images.value = [
      {
        id: 1,
        url: new URL('@/assets/example/huangshan0.png', import.meta.url).href,
        description: '爬山途中',
        location: '安徽省黄山市黄山',
        annotations: [
          {
            id: 1,
            x: 55,
            y: 35,
            name: '天天开心',
            relationship: '自己',
            avatar: new URL('@/assets/example/user.png', import.meta.url).href
          },
          {
            id: 2,
            x: 36,
            y: 38,
            name: '',
            relationship: '',
            avatar: new URL('@/assets/example/huangshan-1.png', import.meta.url).href
          },
          {
            id: 3,
            x: 29,
            y: 35,
            name: '',
            relationship: '',
            avatar: new URL('@/assets/example/huangshan-2.png', import.meta.url).href
          }
        ]
      },
      {
        id: 2,
        url: new URL('@/assets/example/huangshan1.png', import.meta.url).href,
        description: '登顶自拍',
        location: '安徽省黄山市黄山',
        annotations: [
          {
            id: 1,
            x: 57,
            y: 42,
            name: '天天开心',
            relationship: '自己',
            avatar: new URL('@/assets/example/user.png', import.meta.url).href
          },
        ]
      }
    ];
  }
};

onMounted(async () => {
  // 设置初始加载状态
  imageLoading.value = true;

  await loadEventData();

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown);
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});
</script>

<style scoped>
.event-photos-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4ff 0%, #e6f7ff 50%, #f0fdfa 100%);
  background-image:
    radial-gradient(circle at 10% 20%, rgba(120, 119, 198, 0.05) 0%, transparent 20%),
    radial-gradient(circle at 90% 80%, rgba(242, 202, 241, 0.08) 0%, transparent 20%);
}

.page-header {
  display: flex;
  align-items: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #4b5563;
  font-size: 16px;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.2s;
}

.back-btn:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #1f2937;
}

.back-icon {
  font-size: 20px;
}

.header-info {
  margin-left: 20px;
}

.event-title {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.event-meta {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 20px;
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.image-section {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.image-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.image-wrapper {
  position: relative;
  width: 100%;
  /* 移除固定高度，让高度根据图片内容自动调整 */
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  background: #f8f9fa; /* 添加背景色，避免空白区域 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-image {
  width: 100%;
  height: auto; /* 高度自动适配，保持宽高比 */
  display: block;
  transition: opacity 0.3s ease; /* 添加透明度过渡效果 */
}

/* 图片加载指示器 */
.image-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #6b7280;
  z-index: 10;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.image-loading p {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 24px;
  color: #4b5563;
  transition: all 0.2s;
  z-index: 10;
}

.nav-btn:hover:not(:disabled) {
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-50%) scale(1.1);
}

.nav-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.prev-btn {
  left: -24px;
}

.next-btn {
  right: -24px;
}

.annotation-point {
  position: absolute;
  cursor: pointer;
  z-index: 5;
}

.point-marker {
  width: 12px;
  height: 12px;
  background: #3b82f6;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.2s;
}

.annotation-point:hover .point-marker,
.annotation-point.active .point-marker {
  transform: scale(1.3);
  background: #1d4ed8;
}

.person-tooltip {
  position: absolute;
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}

.image-indicators {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 20px;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #d1d5db;
  cursor: pointer;
  transition: all 0.2s;
}

.indicator.active {
  background: #3b82f6;
  transform: scale(1.2);
}

.image-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.image-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.image-info p {
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.location-icon {
  margin-right: 4px;
}

.people-section {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  height: fit-content;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.people-count {
  background: #f3f4f6;
  color: #6b7280;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.people-cards {
  max-height: 500px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.person-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.person-card:hover,
.person-card.selected {
  border-color: #3b82f6;
  background: #f8faff;
}

.person-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  position: relative;
}

.person-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s;
  border-radius: 50%;
}

.person-avatar:hover .avatar-upload-overlay {
  opacity: 1;
}

.upload-icon {
  font-size: 16px;
  color: white;
  margin-bottom: 2px;
}

.upload-text {
  font-size: 8px;
  color: white;
  text-align: center;
  line-height: 1;
}

.person-info {
  flex: 1;
  min-width: 0;
}

.person-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.person-relationship {
  font-size: 14px;
  color: #6b7280;
}

.name-input,
.relationship-input {
  width: 100%;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s;
}

.name-input:focus,
.relationship-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.name-text,
.relationship-text {
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 4px;
  transition: background 0.2s;
}

.name-text:hover,
.relationship-text:hover {
  background: rgba(0, 0, 0, 0.05);
}

.person-actions {
  flex-shrink: 0;
}

.edit-btn,
.save-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s;
}

.edit-btn:hover,
.save-btn:hover {
  background: #2563eb;
}

.no-people {
  text-align: center;
  padding: 40px 20px;
  color: #9ca3af;
}

.no-people-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 16px;
  }

  .image-info {
    grid-template-columns: 1fr;
  }

  .nav-btn {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .prev-btn {
    left: -20px;
  }

  .next-btn {
    right: -20px;
  }
}

@media (max-width: 480px) {
  .nav-btn {
    width: 36px;
    height: 36px;
    font-size: 18px;
  }

  .prev-btn {
    left: -18px;
  }

  .next-btn {
    right: -18px;
  }
}
</style>
