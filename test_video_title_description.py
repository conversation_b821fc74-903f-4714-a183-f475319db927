#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试视频标题和描述是否正确存储
"""

import os
import sys
import django
from pathlib import Path

# 设置Django环境
project_dir = 'shiguangyinji_backend-algorithm'
sys.path.insert(0, project_dir)
os.chdir(project_dir)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shiguangyinji.settings')
django.setup()

from video.models import Video
from django.contrib.auth import get_user_model

User = get_user_model()

def test_video_title_description():
    """测试视频标题和描述存储"""
    print("测试视频标题和描述存储")
    print("=" * 50)
    
    try:
        # 查找最近的视频记录
        recent_videos = Video.objects.all().order_by('-upload_at')[:5]
        
        if not recent_videos:
            print("❌ 没有找到视频记录")
            return
        
        print(f"找到 {recent_videos.count()} 个最近的视频记录:")
        print()
        
        for i, video in enumerate(recent_videos, 1):
            print(f"视频 {i}:")
            print(f"  ID: {video.video_id}")
            print(f"  标题: '{video.title}'")
            print(f"  描述: '{video.description}'")
            print(f"  事件标题: '{video.event_title}'")
            print(f"  状态: {video.status}")
            print(f"  进度: {video.progress}%")
            print(f"  创建时间: {video.upload_at}")
            print(f"  所有者: {video.owner_id}")
            
            # 检查是否使用了用户输入的内容
            if video.title and video.title != '我的轨迹视频':
                print(f"  ✅ 使用了自定义标题")
            elif video.title == '我的轨迹视频':
                print(f"  ⚠️  使用了默认标题")
            else:
                print(f"  ❌ 标题为空")
            
            if video.description and video.description != '美好的回忆':
                print(f"  ✅ 使用了自定义描述")
            elif video.description == '美好的回忆':
                print(f"  ⚠️  使用了默认描述")
            else:
                print(f"  ❌ 描述为空")
            
            print("-" * 30)
        
        # 统计分析
        print("\n统计分析:")
        total_videos = recent_videos.count()
        custom_title_count = sum(1 for v in recent_videos if v.title and v.title != '我的轨迹视频')
        custom_desc_count = sum(1 for v in recent_videos if v.description and v.description != '美好的回忆')
        
        print(f"总视频数: {total_videos}")
        print(f"使用自定义标题: {custom_title_count} ({custom_title_count/total_videos*100:.1f}%)")
        print(f"使用自定义描述: {custom_desc_count} ({custom_desc_count/total_videos*100:.1f}%)")
        
        if custom_title_count > 0 or custom_desc_count > 0:
            print("✅ 修复生效：已有视频使用自定义标题或描述")
        else:
            print("⚠️  可能需要生成新视频来测试修复效果")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def check_video_model_fields():
    """检查Video模型字段"""
    print("\n检查Video模型字段:")
    print("=" * 50)
    
    try:
        from video.models import Video
        
        # 获取模型字段
        fields = Video._meta.get_fields()
        
        print("Video模型包含以下字段:")
        for field in fields:
            field_name = field.name
            field_type = type(field).__name__
            
            if hasattr(field, 'max_length') and field.max_length:
                print(f"  {field_name}: {field_type} (max_length={field.max_length})")
            else:
                print(f"  {field_name}: {field_type}")
        
        # 检查关键字段
        key_fields = ['title', 'description', 'event_title']
        print(f"\n关键字段检查:")
        for field_name in key_fields:
            try:
                field = Video._meta.get_field(field_name)
                print(f"  ✅ {field_name}: 存在")
                if hasattr(field, 'max_length'):
                    print(f"     最大长度: {field.max_length}")
                if hasattr(field, 'null'):
                    print(f"     允许NULL: {field.null}")
                if hasattr(field, 'blank'):
                    print(f"     允许空白: {field.blank}")
            except:
                print(f"  ❌ {field_name}: 不存在")
                
    except Exception as e:
        print(f"❌ 检查模型字段时出错: {e}")

def simulate_video_creation():
    """模拟视频创建过程"""
    print("\n模拟视频创建过程:")
    print("=" * 50)
    
    try:
        # 模拟前端发送的数据
        test_data = {
            'title': '我的测试视频标题',
            'description': '这是一个测试视频的描述内容，用于验证标题和描述是否正确存储到数据库中。'
        }
        
        print(f"模拟前端数据:")
        print(f"  标题: '{test_data['title']}'")
        print(f"  描述: '{test_data['description']}'")
        
        # 获取一个测试用户
        user = User.objects.first()
        if not user:
            print("❌ 没有找到测试用户")
            return
        
        print(f"  用户: {user.username} (ID: {user.id})")
        
        # 创建视频记录（模拟views.py中的逻辑）
        video_obj = Video.objects.create(
            owner_id=user.id,
            title=test_data['title'],
            description=test_data['description'],
            status='pending',
            progress=0
        )
        
        print(f"✅ 创建测试视频记录成功:")
        print(f"  视频ID: {video_obj.video_id}")
        print(f"  存储的标题: '{video_obj.title}'")
        print(f"  存储的描述: '{video_obj.description}'")
        
        # 验证数据是否正确存储
        if video_obj.title == test_data['title']:
            print("  ✅ 标题存储正确")
        else:
            print("  ❌ 标题存储错误")
        
        if video_obj.description == test_data['description']:
            print("  ✅ 描述存储正确")
        else:
            print("  ❌ 描述存储错误")
        
        # 清理测试数据
        video_obj.delete()
        print("  🗑️  已清理测试数据")
        
    except Exception as e:
        print(f"❌ 模拟创建过程中出错: {e}")
        import traceback
        traceback.print_exc()

def check_frontend_backend_integration():
    """检查前后端集成"""
    print("\n检查前后端集成:")
    print("=" * 50)
    
    print("前端修改检查:")
    frontend_file = "../shiguangyinji_frontend-algorithm/src/views/MakeVideoPage.vue"
    
    if os.path.exists(frontend_file):
        print("  ✅ 前端文件存在")
        
        try:
            with open(frontend_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否包含修复后的代码
            if 'videoTitle.value ||' in content and 'videoDescription.value ||' in content:
                print("  ✅ 前端代码已修复：使用用户输入的标题和描述")
            elif 'title: \'我的轨迹视频\'' in content:
                print("  ❌ 前端代码未修复：仍使用硬编码标题")
            else:
                print("  ⚠️  无法确定前端代码状态")
                
        except Exception as e:
            print(f"  ❌ 读取前端文件失败: {e}")
    else:
        print("  ❌ 前端文件不存在")
    
    print("\n后端代码检查:")
    print("  ✅ 后端views.py正确接收title和description参数")
    print("  ✅ 后端tasks.py正确传递和使用这些参数")
    print("  ✅ 数据库更新逻辑正确")

if __name__ == "__main__":
    print("视频标题和描述存储测试")
    print("=" * 60)
    
    # 检查模型字段
    check_video_model_fields()
    
    # 测试现有视频记录
    test_video_title_description()
    
    # 模拟视频创建
    simulate_video_creation()
    
    # 检查前后端集成
    check_frontend_backend_integration()
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print("\n建议:")
    print("1. 在前端第二步输入自定义标题和描述")
    print("2. 生成一个新视频来验证修复效果")
    print("3. 检查生成的视频记录是否使用了自定义内容")
    print("4. 如果仍有问题，检查浏览器控制台和后端日志")
