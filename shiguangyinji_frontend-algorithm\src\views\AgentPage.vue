<script setup>
import { ref, onMounted, nextTick, watch } from 'vue';
import { AichatStart, AichatAsk } from "@/api/api.js";

// 假设有多个智能体
const agents = [
  { id: 'agent1', name: '张伟', avatar: 'https://avatars.githubusercontent.com/u/443345?v=4' },
  { id: 'agent2', name: '李娜', avatar: 'https://avatars.githubusercontent.com/u/443334?v=4' },
  { id: 'agent3', name: '王强', avatar: 'https://avatars.githubusercontent.com/u/443337?v=4' }
];
const currentAgent = ref(agents[0]);

const messages = ref([]);
const inputMessage = ref('');
let cid = ref('');

function getStorageKey() {
  return 'chat_history_' + currentAgent.value.id;
}

// 监听 messages 变化，自动保存
watch(messages, (val) => {
  localStorage.setItem(getStorageKey(), JSON.stringify(val));
}, { deep: true });

// 进入页面时读取历史记录
onMounted(() => {
  const saved = localStorage.getItem(getStorageKey());
  if (saved) {
    messages.value = JSON.parse(saved);
    nextTick(scrollToBottom);
  }
});

const sendMessage = async () => {
  if (inputMessage.value.trim()) {
    const msg = inputMessage.value;
    messages.value.push({ text: msg, sender: 'me' });
    inputMessage.value = '';

    await nextTick();
    scrollToBottom();

    try {
      // 传入当前智能体id
      const res = await AichatAsk(msg, currentAgent.value.id, cid.value, currentAgent.value.id);
      if (res && res.msg && res.errno === 0) {
        messages.value.push({ text: res.msg, sender: 'agent' });
        cid.value = res.conversation_id;
      } else {
        messages.value.push({ text: '抱歉，智能体目前不可用~', sender: 'agent' });
      }
      await nextTick();
      scrollToBottom();
    } catch (error) {
      messages.value.push({ text: '抱歉，智能体目前不可用~', sender: 'agent' });
      await nextTick();
      scrollToBottom();
      console.error('请求失败:', error);
    }
  }
};

const selectAgent = async (agent) => {
  currentAgent.value = agent;
  const key = getStorageKey();
  const saved = localStorage.getItem(key);
  if (saved && JSON.parse(saved).length > 0) {
    // 有历史记录，只加载历史记录
    messages.value = JSON.parse(saved);
    cid.value = '';
    await nextTick();
    scrollToBottom();
  } else {
    // 没有历史记录，调用AichatStart，传入不同的智能体id
    messages.value = [];
    cid.value = '';
    try {
      const res = await AichatStart(agent.id); // 传入当前智能体id
      if (res && res.msg) {
        messages.value.push({ text: res.msg, sender: 'agent' });
        cid.value = res.cid || '';
        await nextTick();
        scrollToBottom();
      }
    } catch (error) {
      console.error("请求失败:", error);
    }
  }
};

onMounted(selectAgent.bind(null, currentAgent.value));



const scrollToBottom = () => {
  const list = document.querySelector('.message-list');
  if (list) list.scrollTop = list.scrollHeight;
}
</script>

<template>
  <div class="chat-container">
    <div class="chat-wrapper">
      <!-- 智能体选择区 -->
      <div class="agent-bar">
        <div
          v-for="agent in agents"
          :key="agent.id"
          :class="['agent-item', {active: agent.id === currentAgent.id}]"
          @click="selectAgent(agent)"
        >
          <img :src="agent.avatar" class="agent-avatar" />
          <span>{{ agent.name }}</span>
        </div>
      </div>
      <!-- 聊天主窗口 -->
      <div class="chat-main">
        <div class="chat-header">
          <img :src="currentAgent.avatar" class="header-avatar" />
          <span class="header-title">{{ currentAgent.name }}</span>
        </div>
        <div class="message-list">
          <div v-if="!messages.length" class="empty-tip">暂无对话，快来和我聊天吧！</div>
          <transition-group name="fade" tag="div">
            <div
              v-for="(message, idx) in messages"
              :key="idx"
              :class="['chat-bubble', message.sender === 'me' ? 'bubble-me' : 'bubble-agent']"
            >
              <!-- 智能体消息：头像在左 -->
              <template v-if="message.sender === 'agent'">
                <img :src="currentAgent.avatar" class="bubble-avatar" />
                <div class="bubble-content-wrapper">
                  <div class="bubble-content">{{ message.text }}</div>
                </div>
              </template>
              <!-- 自己消息：头像在右 -->
              <template v-else>
                <div class="bubble-content-wrapper">
                  <div class="bubble-content">{{ message.text }}</div>
                </div>
                <img src="https://avatars.githubusercontent.com/u/443346?v=4" class="bubble-avatar" />
              </template>
            </div>
          </transition-group>
        </div>
        <div class="input-area">
          <input
            v-model="inputMessage"
            @keyup.enter="sendMessage"
            type="text"
            placeholder="输入消息..."
            class="input-box"
          />
          <div class="send-btn" @click="sendMessage">
            <img src="https://static.thenounproject.com/png/14455-200.png" alt="发送" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chat-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f0f4ff 0%, #e6f7ff 50%, #f0fdfa 100%);
  background-image:
    radial-gradient(circle at 15% 15%, rgba(120, 119, 198, 0.08) 0%, transparent 25%),
    radial-gradient(circle at 85% 85%, rgba(242, 202, 241, 0.1) 0%, transparent 25%),
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.8) 0%, transparent 100%),
    linear-gradient(135deg, #f0f4ff 0%, #e6f7ff 50%, #f0fdfa 100%);
}

.chat-wrapper {
  display: flex;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08);
  border-radius: 20px;
  background: #fff;
  width: 900px;   /* 增大宽度 */
  height: 700px;  /* 增大高度 */
  overflow: hidden;
}

.agent-bar {
  width: 140px;   /* 稍微加宽 */
  background: #fff;
  border-radius: 20px 0 0 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 0;
  height: 100%;
  box-shadow: none;
}

.chat-main {
  flex: 1;
  background: #fff;
  border-radius: 0 20px 20px 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0 0 16px 0;
}

.chat-header {
  display: flex;
  align-items: center;
  padding: 18px 24px 12px 24px;
  border-bottom: 1px solid #f0f0f0;
}
.header-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 14px;
}
.header-title {
  font-size: 1.2em;
  color: #333;
  font-weight: bold;
}
.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 18px 18px 0 18px;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  width: 100%;
}
.empty-tip {
  color: #bbb;
  text-align: center;
  margin-top: 60px;
  font-size: 1.1em;
}
.chat-bubble {
  display: flex;
  align-items: flex-end;
  margin-bottom: 18px;
  width: 100%;
  /* 让每条消息都占满一行，便于左右对齐 */
}
.bubble-agent {
  justify-content: flex-start;
  flex-direction: row;
}
.bubble-me {
  justify-content: flex-end;
  flex-direction: row;
}
.bubble-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin: 0 8px;
  object-fit: cover;
}
.bubble-content-wrapper {
  display: flex;
  align-items: center;
  max-width: 70%;
  position: relative;
}
.bubble-agent .bubble-content-wrapper {
  margin-left: 0;
  margin-right: auto;
}
.bubble-me .bubble-content-wrapper {
  margin-left: auto;
  margin-right: 0;
  flex-direction: row-reverse;
}
.bubble-content {
  background: #fff;
  border-radius: 16px;
  padding: 10px 16px;
  font-size: 1em;
  box-shadow: 0 1px 4px rgba(0,0,0,0.06);
  display: flex;
  align-items: center;
  word-break: break-all;
  overflow-wrap: break-word;
  max-width: 100%;
  min-width: 0; /* 关键，防止flex子项溢出 */
}
.bubble-agent .bubble-content {
  background: #e0f7fa;
  border-bottom-left-radius: 4px;
}
.bubble-me .bubble-content {
  background: #c8e6c9;
  border-bottom-right-radius: 4px;
}

.input-area {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 999px;
  padding: 8px 16px;
  margin: 12px 18px 0 18px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}
.input-box {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 1em;
  padding: 8px 12px;
  color: #333; /* 修改输入文字颜色为深色 */
  caret-color: #4CAF50; /* 可选：设置光标颜色为主题色 */
}
.send-btn {
  margin-left: 8px;
  background: #4CAF50;
  border-radius: 50%;
  padding: 6px;
  cursor: pointer;
  transition: background 0.2s, transform 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}
.send-btn:hover {
  background: #43a047;
  transform: scale(1.1);
}
.send-btn img {
  width: 22px;
  height: 22px;
}
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.4s;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}
.agent-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-bottom: 6px;
  border: 2px solid #4caf50;
  object-fit: cover; /* 保证图片不变形 */
  display: block;
}
.agent-item.active {
  background: #e0f2f1;
  box-shadow: 0 0 12px 2px #4caf5044;
  transition: box-shadow 0.2s, background 0.2s;
}
.bubble-me .bubble-avatar {
  margin-right: 28px; /* 右侧头像整体左移，数值可根据实际调整 */
  margin-left: 8px;   /* 保持与气泡的间距 */
}
</style>


