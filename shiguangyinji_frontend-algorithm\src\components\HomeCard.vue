<template>
  <div class="home-card">
    <div class="card-header">
      <img :src="headerImage || '@/assets/images/test.png'" alt="header" class="card-header-image" />
      <div class="card-overlay"></div>
    </div>
    <div class="card-content">
      <h3 class="card-title">{{ title }}</h3>
      <p v-if="content" class="card-excerpt">{{ truncatedContent }}</p>
      <div class="card-footer">
        <div class="user-info">
          <img :src="userAvatar" alt="user avatar" class="user-avatar" />
          <span class="user-name">{{ userName }}</span>
        </div>
        <div class="card-actions">
          <span class="action-icon">
            <img src="@/assets/icon/heart.svg" alt="Like" class="icon" />
          </span>
          <span class="action-icon">
            <img src="@/assets/icon/message-square.svg" alt="Comment" class="icon" />
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HomeCard',
  props: {
    title: {
      type: String,
      required: true
    },
    userAvatar: {
      type: String,
      required: true
    },
    userName: {
      type: String,
      required: true
    },
    content: {
      type: String,
      default: ''
    },
    headerImage: {
      type: String,
      default: ''
    }
  },
  computed: {
    truncatedContent() {
      return this.content && this.content.length > 80
        ? this.content.substring(0, 80) + '...'
        : this.content;
    }
  }
}
</script>

<style scoped>
.home-card {
  width: 280px;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 4px 12px var(--shadow-color);
  background-color: var(--background-card);
  transition: all var(--transition-speed) ease;
  display: flex;
  flex-direction: column;
  user-select: none;
  margin-bottom: 20px;
  position: relative;
}

.home-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.card-header {
  height: 140px;
  position: relative;
  overflow: hidden;
}

.card-header-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.home-card:hover .card-header-image {
  transform: scale(1.05);
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.4));
}

.card-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: var(--text-primary);
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-excerpt {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0 0 16px 0;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid rgba(0,0,0,0.05);
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
  object-fit: cover;
  border: 2px solid var(--accent-light);
}

.user-name {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.card-actions {
  display: flex;
  gap: 12px;
}

.action-icon {
  cursor: pointer;
  opacity: 0.7;
  transition: opacity var(--transition-speed) ease;
}

.action-icon:hover {
  opacity: 1;
}

.icon {
  width: 18px;
  height: 18px;
}

</style>
