# Generated by Django 4.2.20 on 2025-04-09 16:06

import character_db.models
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Character",
            fields=[
                (
                    "character_id",
                    models.AutoField(db_column="id", primary_key=True, serialize=False),
                ),
                (
                    "avatar",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to=character_db.models.get_upload_path,
                    ),
                ),
                ("name", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "gender",
                    models.CharField(
                        choices=[("male", "男"), ("female", "女")],
                        default="male",
                        max_length=6,
                    ),
                ),
                (
                    "relationship",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("birthday", models.DateField(blank=True, null=True)),
                (
                    "belongs_to",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "character",
                "managed": True,
            },
        ),
    ]
