#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复卡住的视频状态
"""

import os
import sys
import django
from pathlib import Path

# 设置Django环境
project_dir = 'shiguangyinji_backend-algorithm'
sys.path.insert(0, project_dir)
os.chdir(project_dir)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shiguangyinji.settings')
django.setup()

from video.models import Video
from django.utils import timezone

def fix_stuck_videos():
    """修复卡住的视频状态"""
    print("检查卡住的视频状态...")
    print("=" * 50)
    
    try:
        # 查找状态为processing但实际已生成的视频
        stuck_videos = Video.objects.filter(status='processing')
        
        print(f"找到 {stuck_videos.count()} 个处理中的视频")
        
        for video in stuck_videos:
            print(f"\n检查视频 {video.video_id}:")
            print(f"  当前状态: {video.status}")
            print(f"  当前进度: {video.progress}%")
            print(f"  视频URL: {video.video_url}")
            
            # 检查视频文件是否存在
            if video.video_url:
                video_path = os.path.join('media', video.video_url)
                if os.path.exists(video_path):
                    file_size = os.path.getsize(video_path)
                    print(f"  ✅ 视频文件存在: {video_path} ({file_size} bytes)")
                    
                    # 更新状态为completed
                    video.status = 'completed'
                    video.progress = 100
                    video.completed_at = timezone.now()
                    video.error_message = None
                    video.save()
                    
                    print(f"  ✅ 已修复状态为completed")
                    
                else:
                    print(f"  ❌ 视频文件不存在: {video_path}")
                    
                    # 检查是否有错误信息
                    if video.progress >= 70:  # 如果进度已经很高，可能是数据库更新失败
                        print(f"  ⚠️  进度已达到{video.progress}%，可能是数据库连接问题")
                        print(f"  建议手动检查media/video/目录下是否有相关文件")
                        
                        # 列出可能的视频文件
                        video_dir = Path('media/video')
                        if video_dir.exists():
                            possible_files = list(video_dir.glob(f"*{video.video_id}*"))
                            if possible_files:
                                print(f"  可能的视频文件:")
                                for file in possible_files:
                                    print(f"    - {file}")
                                    
                                # 如果找到视频文件，更新URL
                                video_files = [f for f in possible_files if f.suffix == '.mp4']
                                if video_files:
                                    latest_video = max(video_files, key=lambda x: x.stat().st_mtime)
                                    relative_path = str(latest_video.relative_to('media'))
                                    
                                    video.video_url = relative_path
                                    video.status = 'completed'
                                    video.progress = 100
                                    video.completed_at = timezone.now()
                                    video.error_message = None
                                    video.save()
                                    
                                    print(f"  ✅ 已更新视频URL为: {relative_path}")
                                    print(f"  ✅ 已修复状态为completed")
            else:
                print(f"  ❌ 视频URL为空")
                
                # 尝试查找可能的视频文件
                video_dir = Path('media/video')
                if video_dir.exists():
                    possible_files = list(video_dir.glob(f"*{video.video_id}*"))
                    video_files = [f for f in possible_files if f.suffix == '.mp4']
                    
                    if video_files:
                        latest_video = max(video_files, key=lambda x: x.stat().st_mtime)
                        relative_path = str(latest_video.relative_to('media'))
                        
                        video.video_url = relative_path
                        video.status = 'completed'
                        video.progress = 100
                        video.completed_at = timezone.now()
                        video.error_message = None
                        video.save()
                        
                        print(f"  ✅ 找到视频文件: {relative_path}")
                        print(f"  ✅ 已修复状态为completed")
                    else:
                        print(f"  ❌ 未找到对应的视频文件")
        
        print("\n" + "=" * 50)
        print("修复完成!")
        
        # 显示修复后的状态
        completed_videos = Video.objects.filter(status='completed').count()
        processing_videos = Video.objects.filter(status='processing').count()
        failed_videos = Video.objects.filter(status='failed').count()
        
        print(f"当前视频状态统计:")
        print(f"  已完成: {completed_videos}")
        print(f"  处理中: {processing_videos}")
        print(f"  失败: {failed_videos}")
        
    except Exception as e:
        print(f"修复过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def list_video_files():
    """列出所有视频文件"""
    print("\n列出media/video/目录下的所有文件:")
    print("=" * 50)
    
    video_dir = Path('media/video')
    if video_dir.exists():
        files = list(video_dir.iterdir())
        files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        for file in files:
            if file.is_file():
                size = file.stat().st_size
                mtime = file.stat().st_mtime
                from datetime import datetime
                mtime_str = datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
                print(f"  {file.name} ({size} bytes, {mtime_str})")
    else:
        print("  media/video/ 目录不存在")

def check_database_connection():
    """检查数据库连接"""
    print("\n检查数据库连接:")
    print("=" * 50)
    
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            print("✅ 数据库连接正常")
            
            # 检查video表
            cursor.execute("SELECT COUNT(*) FROM video")
            count = cursor.fetchone()[0]
            print(f"✅ video表中有 {count} 条记录")
            
            # 检查processing状态的视频
            cursor.execute("SELECT COUNT(*) FROM video WHERE status = 'processing'")
            processing_count = cursor.fetchone()[0]
            print(f"⚠️  有 {processing_count} 个视频状态为processing")
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")

if __name__ == "__main__":
    print("视频状态修复工具")
    print("=" * 60)
    
    # 检查数据库连接
    check_database_connection()
    
    # 列出视频文件
    list_video_files()
    
    # 修复卡住的视频状态
    fix_stuck_videos()
    
    print("\n" + "=" * 60)
    print("修复工具执行完成!")
    print("\n建议:")
    print("1. 检查修复结果是否正确")
    print("2. 重启Django服务器")
    print("3. 在前端刷新页面查看状态")
    print("4. 如果问题仍然存在，检查MySQL服务器配置")
