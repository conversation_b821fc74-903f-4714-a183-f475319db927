# 保存时光片段总流程

## 流程概述

用户通过"记录时光片段"页面上传图片、填写信息，系统保存数据并进行AI处理，最终生成完整的时光片段记录。

## 详细流程

### 1. 前端用户操作 (UploadEventPage.vue)

#### 1.1 页面初始化
- 加载省市数据 (`province_city_mapping.json`)
- 初始化响应式数据：
  - `imagePreviews` - 图片预览URL数组
  - `imageFiles` - 实际图片文件数组
  - `videoPreviews` - 视频预览URL数组
  - `title`, `description`, `datetime`, `province`, `city` - 表单数据

#### 1.2 用户输入数据
1. **媒体上传**
   - 选择图片文件 → `handleImageUpload()`
   - 选择视频文件 → `handleVideoUpload()`
   - 生成预览URL (`URL.createObjectURL()`)
   - 存储实际文件对象

2. **基本信息填写**
   - 标题 (必填)
   - 描述 (可选，支持一键扩写)
   - 时间 (必填，日期选择器)
   - 地点 (必填，省市二级联动)

3. **预览管理**
   - 实时显示上传的图片/视频预览
   - 支持删除已上传的媒体文件

#### 1.3 表单验证
在 `saveMemory()` 函数中进行前端验证：
```javascript
- 标题不能为空
- 时间必须选择
- 省份和城市必须选择
- 至少上传一张图片
```

#### 1.4 数据提交
构建 `FormData` 对象：
```javascript
formData.append('location', province + ' ' + city)
formData.append('date', datetime)
formData.append('title', title)
formData.append('description', description)
// 添加所有图片文件
for (let file of imageFiles) {
    formData.append('pictures', file)
}
```

### 2. 前端API调用

#### 2.1 HTTP请求
```javascript
const res = await service({
    method: 'post',
    url: 'issue/create/',
    headers: { 'Content-Type': 'multipart/form-data' },
    data: formData
})
```

#### 2.2 请求处理
- 通过 `utils/request.js` 的axios实例发送
- 自动添加JWT token认证头
- 支持文件上传的multipart格式

### 3. 后端接收处理 (IssueCreate View)

#### 3.1 权限验证
- 检查用户是否已登录 (`IsAuthenticated`)
- 验证JWT token有效性

#### 3.2 数据提取
从请求中提取数据：
```python
author = request.user
location = request.data.get('location')
date = request.data.get('date')
title = request.data.get('title')
pictures = request.FILES.getlist('pictures')
description = request.data.get('description')
```

#### 3.3 创建Issue记录
```python
issue = Issue.objects.create(
    author=author,
    title=title,
    location=location,
    date=date,
    description=description
)
```

#### 3.4 保存图片文件
```python
for pic in pictures:
    if pic.name.endswith(settings.IMAGE_TAIL):
        Picture.objects.create(issue=issue, url=pic, location=location)
```

### 4. AI处理流程

#### 4.1 获取用户角色信息
```python
me = Character.objects.filter(belongs_to=author.id, relationship='自己').first()
protagonist = {
    'name': me.name,
    'avatar': avatar_path
}
```

#### 4.2 获取用户朋友信息
```python
my_friends = Character.objects.filter(belongs_to=author).all()
user_characters = [构建朋友数据列表]
```

#### 4.3 构建事件列表
```python
events = Picture.objects.filter(issue=issue).all()
event_list = [{'id': pic.picture_id, 'url': pic_path}]
```

#### 4.4 调用AI处理
```python
infos = load_event(protagonist, user_characters, description, date, event_list, issue.issue_id)
```

#### 4.5 更新图片信息
对每张图片进行AI分析后更新：
```python
for info in infos:
    update_pic = Picture.objects.get(picture_id=info['id'])
    update_pic.description = info['description']
    update_pic.location = info['location']
    update_pic.relations = info['relations']
    # 保存检测到的人脸图片
    # 保存关系图片
    update_pic.save()
```

### 5. 数据存储结构

#### 5.1 Issue表
```sql
- issue_id (主键)
- author (外键 → User)
- title (标题)
- location (地点)
- date (日期)
- description (描述)
```

#### 5.2 Picture表
```sql
- picture_id (主键)
- issue (外键 → Issue)
- url (原始图片路径)
- upload_at (上传时间)
- description (AI生成的描述)
- relations (人物关系)
- location (图片地点)
- detected_image (人脸检测图片)
- relationship_image (关系图片)
```

### 6. 文件存储路径

#### 6.1 原始图片
```
media/issue/{user_id}/{issue_id}/{timestamp}_{filename}
```

#### 6.2 检测图片
```
media/detected/{user_id}/{issue_id}/detected_{timestamp}_{filename}
```

#### 6.3 关系图片
```
media/relationship/{user_id}/{issue_id}/relationship_{timestamp}_{filename}
```

### 7. 响应处理

#### 7.1 成功响应
```json
{
    "issue_id": 123
}
```
状态码：201 Created

#### 7.2 错误响应
```json
{
    "error": "错误信息"
}
```
可能的错误：
- 401: 用户未认证
- 400: 参数错误
- 500: 服务器内部错误

### 8. 前端响应处理

#### 8.1 成功处理
```javascript
alert('🎉 已保存你的时光片段！')
location.reload() // 刷新页面
```

#### 8.2 错误处理
```javascript
alert('上传失败，请重试')
console.error(e)
```

## 关键技术点

### 1. 文件上传
- 使用 `FormData` 支持多文件上传
- 前端预览功能 (`URL.createObjectURL`)
- 后端文件验证 (图片格式检查)

### 2. 数据验证
- 前端表单验证 (必填项检查)
- 后端权限验证 (JWT认证)
- 文件类型验证 (IMAGE_TAIL设置)

### 3. AI集成
- 人脸识别和检测
- 图片描述生成
- 人物关系分析
- 结果图片生成

### 4. 错误处理
- 前端用户友好提示
- 后端详细错误日志
- 网络异常处理

## 优化建议

### 1. 性能优化
- [ ] 图片压缩上传
- [ ] 异步AI处理
- [ ] 进度条显示
- [ ] 分片上传大文件

### 2. 用户体验
- [ ] 拖拽上传
- [ ] 批量操作
- [ ] 自动保存草稿
- [ ] 上传进度显示

### 3. 错误处理
- [ ] 重试机制
- [ ] 离线缓存
- [ ] 详细错误分类
- [ ] 用户指导提示

## 总结

整个"保存时光片段"流程涉及前端表单处理、文件上传、后端数据存储、AI处理等多个环节，形成了一个完整的用户内容创建和智能处理系统。流程设计考虑了用户体验、数据安全、AI增强等多个方面，为用户提供了便捷的时光记录功能。
