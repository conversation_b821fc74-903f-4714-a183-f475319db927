<script setup>
import {ref, onMounted, onUpdated, nextTick, onBeforeUnmount} from 'vue';
import HomeCard from "@/components/HomeCard.vue";
import CardDetail from "@/components/CardDetail.vue";
import vueImage from '@/assets/vue.svg';
import testImage from "@/assets/test.jpg";
import avatarImage from '@/assets/avatar.png';
import router from "@/router/index.js";
import {GetRandomDocuments, GetUserById, SearchDocuments} from "@/api/api.js";

const searchInput = ref('');
const currentView = ref('推荐');

const originalCards = [
  {
    docId: 1,
    title: '西藏之旅：追寻心中的净土',
    userAvatar: avatarImage,
    userName: '旅行者小明',
    content: '这次西藏之行让我深深震撼，从拉萨的布达拉宫到纳木错的湖光山色，每一处风景都是大自然的杰作。在海拔4000多米的高原上，我感受到了前所未有的宁静与纯净。',
    headerImage: new URL('@/assets/covers/tibet.jpg', import.meta.url).href
  },
  {
    docId: 2,
    title: '毕业季：青春不散场的合影',
    userAvatar: avatarImage,
    userName: '青春记录者小红',
    content: '四年大学时光即将结束，和朋友们拍下了最后一张合影。那些一起熬夜、一起哭笑的日子，都定格在这张照片里。愿友谊长存，青春不老。',
    headerImage: new URL('@/assets/covers/graduation.jpg', import.meta.url).href
  },
  {
    docId: 3,
    title: '家庭聚餐：温暖的时光印记',
    userAvatar: avatarImage,
    userName: '家庭记录员小刚',
    content: '每年春节的家庭聚餐都是最温馨的时刻，三代人围坐在一起，分享着一年来的点点滴滴。奶奶慈祥的笑容，爸妈忙碌的身影，都是我心中最珍贵的回忆。',
    headerImage: new URL('@/assets/covers/family.jpg', import.meta.url).href
  },
  {
    docId: 4,
    title: '海边日落：与爱人的浪漫时光',
    userAvatar: avatarImage,
    userName: '浪漫主义者小李',
    content: '在三亚的海边，我和她手牵手看着夕阳西下，海浪轻抚着沙滩，微风吹过发梢。那一刻，时间仿佛静止了，只有我们两个人和这片金色的海洋。',
    headerImage: new URL('@/assets/covers/beach.jpg', import.meta.url).href
  },
  {
    docId: 5,
    title: '登山日记：征服人生第一座高峰',
    userAvatar: avatarImage,
    userName: '户外爱好者小王',
    content: '经过6小时的艰难攀登，终于站在了泰山之巅。虽然双腿酸痛，但看到日出那一刻的壮丽景色，所有的疲惫都烟消云散。这次登山让我明白，坚持就是胜利。',
    headerImage: new URL('@/assets/covers/mountain.jpg', import.meta.url).href
  }
];

const cards = ref([...originalCards]);

const topics= ref();

const switchView = (view) => {
  currentView.value = view;
};

const clearInput = () => {
  searchInput.value = '';
  cards.value = [...originalCards]; // 恢复原本的本地数据
};

const setCardHeights = () => {
  nextTick(() => {
    const cardElements = document.querySelectorAll('.waterfall-item');
    const container = document.querySelector('.waterfall');
    const columnCount = Math.floor(container.offsetWidth / 250);
    const columns = Array(columnCount).fill(0);

    cardElements.forEach(card => {
      const minColumnHeight = Math.min(...columns);
      const index = columns.indexOf(minColumnHeight);

      card.style.top = `${minColumnHeight}px`;
      card.style.left = `${index * 300}px`;

      columns[index] += card.offsetHeight + 20;
    });
  });
};

async function fetchDocs() {
  try {
    const response = await GetRandomDocuments(10);
    console.log("获取的文档:", response);

    if (response && response.success && Array.isArray(response.data)) {
      // 并发请求所有用户信息
      const docs = response.data;
      const userRequests = docs.map(doc => GetUserById(doc.owner_id));
      const userResponses = await Promise.all(userRequests);

      // 组合文档和用户数据
      cards.value = docs.map((doc, index) => {
        const user = userResponses[index];
        return {
          docId: doc.id, // 这里必须有
          title: doc.title,
          userAvatar: user?.avatar_url || avatarImage,
          userName: user?.nickname || '未知用户',
          content: doc.content,
          headerImage: doc.cover_url || testImage
        };
      });
    } else {
      console.error("返回数据格式不符合预期:", response);
    }
  } catch (error) {
    console.error("文档获取出错:", error);
  }
}

// 页面加载后调整瀑布流布局
onMounted(async () => {
  try {
    // 注释掉原有的API调用
    // await fetchDocs();
    setCardHeights();
  } catch (error) {
    console.error("加载推荐文档失败:", error);
  }
  setCardHeights();

  window.onload = () => {
    setCardHeights();  // 确保在页面加载完成后执行瀑布流布局计算
  };

  // 监听窗口大小变化时，重新调整瀑布流布局
  let resizeTimeout;
  window.addEventListener('resize', () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(setCardHeights, 200);
  });
});

// 监听视图切换，重新调整瀑布流布局
onUpdated(() => {
  if (currentView.value === '推荐') {
    setCardHeights();
  }
});

// 在组件销毁时，移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', setCardHeights);
});

const showDetail = ref(false); // 控制弹窗显示
const selectedCard = ref(null); // 当前选中的卡片

const openCardDetail = (card) => {
  selectedCard.value = card;
  showDetail.value = true;
};

const closeCardDetail = () => {
  showDetail.value = false;
  selectedCard.value = null;
};

const gotoArticle = (docId) => {
  router.push({
    name: '文章页面',
    query: {
      id: docId
    }
  });
};

// async function handleSearch(keyword) {
//   try {
//     const result = await SearchDocuments(keyword);
//     console.log("搜索结果：", result);


//     if (result && result.success && Array.isArray(result.data)) {
//       // 并发请求所有用户信息
//       const docs = result.data;
//       const userRequests = docs.map(doc => GetUserById(doc.owner_id));
//       const userResponses = await Promise.all(userRequests);

//       // 组合文档和用户数据
//       cards.value = docs.map((doc, index) => {
//         const user = userResponses[index];
//         return {
//           docId: doc.id,
//           title: doc.title,
//           userAvatar: user?.avatar_url || avatarImage, // 默认头像
//           userName: user?.nickname || '未知用户',
//           content: doc.content,
//         };
//       });
//     } else {
//       console.error("返回数据格式不符合预期:", result);
//     }
//   } catch (error) {
//     console.error("搜索失败：", error);
//   }
// }

async function handleSearch(keyword) {
  if (!keyword) {
    cards.value = [...originalCards];
    return;
  }
  const lower = keyword.toLowerCase();
  cards.value = originalCards.filter(card =>
    (card.title && card.title.toLowerCase().includes(lower)) ||
    (card.content && card.content.toLowerCase().includes(lower)) ||
    (card.userName && card.userName.toLowerCase().includes(lower))
  );
}

const followCards = ref([
  {
    id: 1,
    userAvatar: avatarImage,
    userName: '旅行者小明',
    action: '分享了新的时光片段',
    time: '2小时前',
    title: '西藏之旅：追寻心中的净土',
    content: '刚刚从西藏回来，这次旅行让我收获满满。在布达拉宫前虔诚祈祷，在纳木错边静静冥想，每一个瞬间都是心灵的洗礼。',
    docId: 1
  },
  {
    id: 2,
    userAvatar: avatarImage,
    userName: '青春记录者小红',
    action: '上传了珍贵合影',
    time: '3小时前',
    title: '毕业季：青春不散场的合影',
    content: '和最好的朋友们在校园里拍下了最后一张合影，四年的美好时光即将结束，但友谊永远不会褪色。',
    docId: 2
  },
  {
    id: 3,
    userAvatar: avatarImage,
    userName: '家庭记录员小刚',
    action: '记录了温馨时刻',
    time: '5小时前',
    title: '家庭聚餐：温暖的时光印记',
    content: '今天全家人聚在一起吃饭，看着爷爷奶奶慈祥的笑容，感受到了家的温暖。这些平凡却珍贵的时光，值得永远珍藏。',
    docId: 3
  },
  {
    id: 4,
    userAvatar: avatarImage,
    userName: '浪漫主义者小李',
    action: '分享了浪漫瞬间',
    time: '1天前',
    title: '海边日落：与爱人的浪漫时光',
    content: '和她一起在海边看日落，金色的阳光洒在海面上，那一刻的美好让人永生难忘。爱情最美的样子，就是陪伴。',
    docId: 4
  },
  {
    id: 5,
    userAvatar: avatarImage,
    userName: '户外爱好者小王',
    action: '完成了登山挑战',
    time: '2天前',
    title: '登山日记：征服人生第一座高峰',
    content: '终于登上了泰山顶峰！虽然过程很辛苦，但站在山顶看日出的那一刻，所有的努力都值得了。人生就像登山，坚持到底就是胜利。',
    docId: 5
  }
]);
</script>

<template>
  <div class="main-page">
    <div class="hero-section">
      <h1 class="hero-title">时光印记</h1>
      <p class="hero-subtitle">记录美好瞬间，分享珍贵回忆</p>

      <div class="search-container">
        <div class="input-box">
          <input
              type="text"
              class="search-input"
              placeholder="搜索时光片段、旅行记录..."
              v-model="searchInput"
              @keyup.enter="handleSearch(searchInput)"
          />
          <div class="input-button">
            <div class="close-icon" v-if="searchInput" @click="clearInput">
              <img src="@/assets/icon/close.svg" alt="关闭" />
            </div>
            <div class="search-icon">
              <img src="@/assets/icon/search.svg" alt="搜索" @click="handleSearch(searchInput)"/>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="content">
      <div class="top-navi">
        <span @click="switchView('推荐')" :class="{ active: currentView === '推荐' }">
          推荐
          <img
            src="@/assets/icon/refresh-cw.svg"
            alt="刷新"
            class="refresh-icon"
            @click.stop="fetchDocs"
          />
        </span>
        <span @click="switchView('关注')" :class="{ active: currentView === '关注' }">关注</span>
        <span
            v-for="(t, index) in topics"
            :key="index"
            @click="switchView(t)"
            :class="{ active: currentView === t }"
        >
          {{ t }}
        </span>
      </div>

      <div class="navi-content">
        <div v-if="currentView === '推荐'" class="card-grid">
          <HomeCard
              v-for="(card, index) in cards"
              :key="index"
              :title="card.title"
              :userAvatar="card.userAvatar"
              :userName="card.userName"
              :content="card.content"
              :headerImage="card.headerImage || '@/assets/images/test.png'"
              class="grid-item"
              @click="gotoArticle(card.docId)"
          />
        </div>

        <div v-if="currentView === '关注'" class="follow-section">
          <h2>好友时光动态</h2>
          <div class="follow-cards">
            <div v-for="card in followCards" :key="card.id" class="follow-card" @click="gotoArticle(card.docId)">
              <div class="follow-header">
                <img :src="card.userAvatar" alt="用户头像" class="follow-avatar" />
                <div class="follow-info">
                  <span class="follow-name">{{ card.userName }}</span>
                  <span class="follow-action">{{ card.action }}</span>
                  <span class="follow-time">{{ card.time }}</span>
                </div>
              </div>
              <div class="follow-content">
                <h3 class="follow-title">{{ card.title }}</h3>
                <p class="follow-text">{{ card.content }}</p>
              </div>
            </div>
          </div>
        </div>

        <CardDetail
            v-if="showDetail"
            :cardData="selectedCard"
            :isVisible="showDetail"
            @close="closeCardDetail"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
* {
  user-select: none;
}

.main-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  min-height: 100vh;
  background-color: #f8faff;
  background-image: url('@/assets/backgrounds/dots.svg');
  position: relative;
}

.main-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(240, 244, 255, 0.7) 0%, rgba(230, 247, 255, 0.7) 50%, rgba(240, 253, 250, 0.7) 100%);
  z-index: -1;
}

/* Hero Section */
.hero-section {
  text-align: center;
  padding: 40px 0;
  margin-bottom: 20px;
}

.hero-title {
  font-size: 2.5rem;
  color: var(--primary-dark);
  margin-bottom: 10px;
  font-family: 'ChangErFeiBai', sans-serif;
}

.hero-subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: 30px;
}

.search-container {
  max-width: 600px;
  margin: 0 auto;
}

/* Search Box */
.input-box {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  background-color: var(--background-white);
  box-shadow: 0 4px 12px var(--shadow-color);
  border-radius: 25px;
  overflow: hidden;
  transition: all var(--transition-speed) ease;
}

.input-box:focus-within {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.search-input {
  padding: 0 20px;
  width: 100%;
  height: 100%;
  font-size: 16px;
  color: var(--text-primary);
  caret-color: var(--primary-color);
  background: transparent;
  border: none;
  outline: none;
}

.search-input::placeholder {
  color: var(--text-secondary);
  opacity: 0.6;
}

.input-button {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.close-icon img,
.search-icon img {
  width: 20px;
  height: 20px;
  cursor: pointer;
  transition: transform var(--transition-speed) ease;
  margin-left: 10px;
  opacity: 0.7;
}

.close-icon img:hover,
.search-icon img:hover {
  transform: scale(1.1);
  opacity: 1;
}

/* Content Section */
.content {
  margin-top: 20px;
}

/* Navigation Tabs */
.top-navi {
  margin-bottom: 30px;
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  align-items: center;
  border-bottom: 1px solid rgba(0,0,0,0.05);
  padding-bottom: 15px;
}

.top-navi span {
  font-size: 1.1rem;
  padding: 8px 16px;
  border-radius: 20px;
  transition: all var(--transition-speed) ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.top-navi span:hover {
  background-color: rgba(0,0,0,0.03);
}

.top-navi span.active {
  font-weight: 600;
  color: var(--primary-dark);
  background-color: var(--primary-light);
  color: var(--text-light);
}

.refresh-icon {
  width: 16px;
  height: 16px;
  opacity: 0.7;
  transition: all 0.3s ease;
  cursor: pointer;
}

.refresh-icon:hover {
  opacity: 1;
  transform: rotate(180deg);
}

/* Card Grid */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 25px;
  padding: 10px 0;
}

.grid-item {
  transition: all var(--transition-speed) ease;
}

/* Follow Section */
.follow-section {
  background-color: var(--background-white);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 12px var(--shadow-color);
  padding: 25px;
  margin-top: 20px;
}

.follow-section h2 {
  margin-bottom: 20px;
  color: var(--primary-dark);
  font-size: 1.5rem;
}

.follow-cards {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.follow-card {
  padding: 20px;
  background-color: var(--background-light);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  border-left: 4px solid var(--accent-color);
}

.follow-card:hover {
  background-color: var(--background-white);
  transform: translateY(-3px);
  box-shadow: 0 6px 15px var(--shadow-color);
}

.follow-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.follow-avatar {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  border: 2px solid var(--accent-light);
  object-fit: cover;
}

.follow-info {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.follow-name {
  font-weight: 600;
  color: var(--text-primary);
}

.follow-action {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.follow-time {
  color: var(--text-secondary);
  font-size: 0.8rem;
  opacity: 0.7;
}

.follow-content {
  margin-top: 15px;
}

.follow-title {
  font-size: 1.2rem;
  color: var(--text-primary);
  margin-bottom: 10px;
  font-weight: 600;
}

.follow-text {
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Responsive */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }

  .top-navi span {
    font-size: 1rem;
    padding: 6px 12px;
  }
}
</style>
