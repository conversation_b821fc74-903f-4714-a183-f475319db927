#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复Video模型迁移问题
"""

import os
import sys
import django
from django.db import connection

# 设置Django环境
os.chdir('shiguangyinji_backend-algorithm')
sys.path.insert(0, '.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shiguangyinji.settings')
django.setup()

def check_table_structure():
    """检查video表的当前结构"""
    print("检查video表结构...")
    print("=" * 50)
    
    try:
        with connection.cursor() as cursor:
            # 检查表是否存在
            cursor.execute("SHOW TABLES LIKE 'video'")
            if not cursor.fetchone():
                print("❌ video表不存在")
                return False
            
            # 获取表结构
            cursor.execute("DESCRIBE video")
            columns = cursor.fetchall()
            
            print("当前video表字段:")
            existing_fields = []
            for column in columns:
                field_name = column[0]
                field_type = column[1]
                existing_fields.append(field_name)
                print(f"  {field_name}: {field_type}")
            
            # 检查新字段是否存在
            required_fields = [
                'title', 'description', 'event_title', 
                'status', 'progress', 'error_message',
                'started_at', 'completed_at'
            ]
            
            missing_fields = []
            for field in required_fields:
                if field in existing_fields:
                    print(f"✅ {field} 字段存在")
                else:
                    print(f"❌ {field} 字段缺失")
                    missing_fields.append(field)
            
            return missing_fields
            
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")
        return None

def check_migration_status():
    """检查迁移状态"""
    print("\n检查迁移状态...")
    print("=" * 50)
    
    try:
        with connection.cursor() as cursor:
            # 检查django_migrations表中的记录
            cursor.execute("""
                SELECT id, app, name, applied 
                FROM django_migrations 
                WHERE app = 'video' 
                ORDER BY id
            """)
            migrations = cursor.fetchall()
            
            print("已记录的video迁移:")
            for migration in migrations:
                print(f"  {migration[1]}.{migration[2]} - {migration[3]}")
            
            # 检查0003迁移是否已记录
            cursor.execute("""
                SELECT COUNT(*) 
                FROM django_migrations 
                WHERE app = 'video' AND name = '0003_video_add_fields'
            """)
            count = cursor.fetchone()[0]
            
            if count > 0:
                print("✅ 0003_video_add_fields 迁移已记录")
                return True
            else:
                print("❌ 0003_video_add_fields 迁移未记录")
                return False
                
    except Exception as e:
        print(f"❌ 检查迁移状态失败: {e}")
        return None

def manually_add_fields():
    """手动添加缺失的字段"""
    print("\n手动添加缺失字段...")
    print("=" * 50)
    
    # 字段定义
    field_definitions = [
        "ADD COLUMN title VARCHAR(255) NULL",
        "ADD COLUMN description TEXT NULL", 
        "ADD COLUMN event_title VARCHAR(255) NULL",
        "ADD COLUMN status VARCHAR(20) NOT NULL DEFAULT 'pending'",
        "ADD COLUMN progress INT NOT NULL DEFAULT 0",
        "ADD COLUMN error_message TEXT NULL",
        "ADD COLUMN started_at DATETIME NULL",
        "ADD COLUMN completed_at DATETIME NULL"
    ]
    
    try:
        with connection.cursor() as cursor:
            for field_def in field_definitions:
                try:
                    sql = f"ALTER TABLE video {field_def}"
                    print(f"执行: {sql}")
                    cursor.execute(sql)
                    print("✅ 成功")
                except Exception as e:
                    if "Duplicate column name" in str(e):
                        print("⚠️  字段已存在，跳过")
                    else:
                        print(f"❌ 失败: {e}")
        
        print("\n✅ 字段添加完成")
        return True
        
    except Exception as e:
        print(f"❌ 手动添加字段失败: {e}")
        return False

def mark_migration_as_applied():
    """标记迁移为已应用"""
    print("\n标记迁移为已应用...")
    print("=" * 50)
    
    try:
        with connection.cursor() as cursor:
            # 检查迁移是否已存在
            cursor.execute("""
                SELECT COUNT(*) 
                FROM django_migrations 
                WHERE app = 'video' AND name = '0003_video_add_fields'
            """)
            
            if cursor.fetchone()[0] == 0:
                # 插入迁移记录
                cursor.execute("""
                    INSERT INTO django_migrations (app, name, applied) 
                    VALUES ('video', '0003_video_add_fields', NOW())
                """)
                print("✅ 迁移记录已添加")
            else:
                print("⚠️  迁移记录已存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 标记迁移失败: {e}")
        return False

def reset_migration():
    """重置迁移（删除记录后重新应用）"""
    print("\n重置迁移...")
    print("=" * 50)
    
    try:
        with connection.cursor() as cursor:
            # 删除迁移记录
            cursor.execute("""
                DELETE FROM django_migrations 
                WHERE app = 'video' AND name = '0003_video_add_fields'
            """)
            print("✅ 迁移记录已删除")
        
        # 重新运行迁移
        print("重新运行迁移...")
        os.system("python manage.py migrate video")
        
        return True
        
    except Exception as e:
        print(f"❌ 重置迁移失败: {e}")
        return False

def main():
    """主函数"""
    print("Video模型迁移修复工具")
    print("=" * 60)
    
    # 1. 检查表结构
    missing_fields = check_table_structure()
    if missing_fields is None:
        return
    
    # 2. 检查迁移状态
    migration_recorded = check_migration_status()
    
    # 3. 根据情况选择修复方案
    if missing_fields and migration_recorded:
        print("\n🔧 情况：字段缺失但迁移已记录")
        print("解决方案：手动添加字段")
        manually_add_fields()
        
    elif missing_fields and not migration_recorded:
        print("\n🔧 情况：字段缺失且迁移未记录")
        print("解决方案：手动添加字段并标记迁移")
        if manually_add_fields():
            mark_migration_as_applied()
            
    elif not missing_fields and migration_recorded:
        print("\n✅ 情况：字段存在且迁移已记录")
        print("一切正常，无需修复")
        
    elif not missing_fields and not migration_recorded:
        print("\n🔧 情况：字段存在但迁移未记录")
        print("解决方案：标记迁移为已应用")
        mark_migration_as_applied()
    
    # 4. 最终验证
    print("\n" + "=" * 60)
    print("最终验证...")
    final_missing = check_table_structure()
    
    if not final_missing:
        print("✅ 所有字段都已正确添加到数据库！")
        print("\n现在可以正常使用异步视频生成功能了")
    else:
        print(f"❌ 仍有字段缺失: {final_missing}")
        print("建议手动检查数据库配置")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
