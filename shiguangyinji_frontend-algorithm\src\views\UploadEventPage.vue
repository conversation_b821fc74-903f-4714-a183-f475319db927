<template>
  <div class="page-container">
    <!-- 页面顶部 -->
    <header class="page-header">
      <div class="header-content">
        <h1 class="page-title">✨ 记录时光片段</h1>
        <p class="page-subtitle">"捕捉珍贵瞬间，留存美好回忆"</p>
      </div>
      <div class="header-decoration"></div>
    </header>

    <div class="content-wrapper">
      <!-- 基本信息卡片 -->
      <section class="card upload-card">
        <div class="card-header">
          <div class="card-icon left-icon">📝</div>
          <h2 class="card-title blue-title">创建新片段</h2>
          <div class="card-icon right-icon"></div>
        </div>

        <!-- 图片 & 视频 上传 区 -->
        <div class="media-grid">
          <!-- 图片上传 -->
          <div class="media-item">
            <label class="label">
              <span class="label-icon">🖼️</span>
              <span class="label-text">添加照片</span>
            </label>
            <div class="upload-zone">
              <input type="file" accept="image/*" multiple @change="handleImageUpload" class="file-input" id="image-upload" />
              <label for="image-upload" class="upload-btn">
                <span class="upload-icon">+</span>
                <span>选择图片</span>
              </label>
            </div>
            <div class="preview-row" v-if="imagePreviews.length > 0">
              <img v-for="(img, index) in imagePreviews" :key="index" :src="img" class="image-thumb" />
            </div>
          </div>

          <!-- 视频上传 -->
          <div class="media-item">
            <label class="label">
              <span class="label-icon">🎬</span>
              <span class="label-text">添加视频</span>
            </label>
            <div class="upload-zone">
              <input type="file" accept="video/*" multiple @change="handleVideoUpload" class="file-input" id="video-upload" />
              <label for="video-upload" class="upload-btn">
                <span class="upload-icon">+</span>
                <span>选择视频</span>
              </label>
            </div>
            <div class="preview-row" v-if="videoPreviews.length > 0">
              <video v-for="(vid, index) in videoPreviews" :key="index" :src="vid" class="video-thumb" controls />
            </div>
          </div>
        </div>

        <!-- 文本输入区 -->
        <div class="text-inputs">
          <div class="text-group">
            <label class="label">
              <span class="label-icon">✏️</span>
              <span class="label-text">标题</span>
            </label>
            <input type="text" v-model="title" class="text-field" placeholder="给这个时光片段起个名字..." />
          </div>

          <div class="text-group description-group">
            <label class="label">
              <span class="label-icon">📝</span>
              <span class="label-text">描述</span>
            </label>
            <textarea v-model="description" rows="3" class="text-area" placeholder="记录下当时的心情和感受..."></textarea>
            <button @click="expandDescription" class="expand-btn" :disabled="isExpanding || !description.trim()">
              <span class="btn-icon" :class="{ 'spinning': isExpanding }">✨</span>
              <span>{{ isExpanding ? '正在努力扩写中...' : '一键扩写' }}</span>
            </button>
          </div>

          <div class="datetime-location">
            <div class="text-group">
              <label class="label">
                <span class="label-icon">🕒</span>
                <span class="label-text">时间</span>
              </label>
              <input type="date" v-model="datetime" class="text-field datetime-field" />
            </div>

            <div class="text-group">
              <label class="label">
                <span class="label-icon">📍</span>
                <span class="label-text">地点</span>
              </label>
              <div class="select-row">
                <select v-model="province" @change="loadCities" class="select-field">
                  <option disabled value="">选择省份</option>
                  <option v-for="prov in provinces" :key="prov" :value="prov">{{ prov }}</option>
                </select>
                <select v-model="city" class="select-field">
                  <option disabled value="">选择城市</option>
                  <option v-for="c in cities" :key="c" :value="c">{{ c }}</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 预览管理卡片 -->
      <section class="card preview-card" v-if="imagePreviews.length > 0 || videoPreviews.length > 0">
        <div class="card-header">
          <div class="card-icon left-icon">👁️</div>
          <h2 class="card-title green-title">预览与管理</h2>
          <div class="card-icon right-icon"></div>
        </div>

        <div class="preview-grid">
          <!-- 图片预览 -->
          <div v-for="(img, index) in imagePreviews" :key="`img-${index}`" class="preview-item">
            <img :src="img" class="preview-media" />
            <div class="preview-overlay">
              <button @click="removeImage(index)" class="delete-btn" title="删除">
                <span class="delete-icon">×</span>
              </button>
            </div>
          </div>

          <!-- 视频预览 -->
          <div v-for="(vid, index) in videoPreviews" :key="`vid-${index}`" class="preview-item video-item">
            <video :src="vid" class="preview-media" muted></video>
            <div class="preview-overlay">
              <button @click="removeVideo(index)" class="delete-btn" title="删除">
                <span class="delete-icon">×</span>
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- 保存按钮 -->
      <div class="save-wrapper">
        <button @click="saveMemory" class="save-btn" :disabled="isUploading">
          <span class="save-icon" v-if="!isUploading">💾</span>
          <span class="loading-spinner" v-if="isUploading"></span>
          <span class="save-text">{{ isUploading ? '正在保存中...' : '保存时光片段' }}</span>
        </button>
        <p class="save-note">"每一个片段，都是生命中的璀璨星光"</p>
      </div>
    </div>

    <!-- 加载遮罩层 -->
    <div v-if="isUploading" class="loading-overlay">
      <div class="loading-container">
        <div class="loading-animation">
          <div class="loading-circle"></div>
          <div class="loading-circle"></div>
          <div class="loading-circle"></div>
        </div>
        <h3 class="loading-title">正在保存您的时光片段</h3>
        <p class="loading-subtitle">请稍候，我们正在为您处理...</p>
        <div class="loading-progress">
          <div class="progress-bar"></div>
        </div>
      </div>
    </div>

    <!-- 成功弹窗 -->
    <div v-if="showSuccessModal" class="success-modal-overlay" @click="closeSuccessModal">
      <div class="success-modal" @click.stop>
        <div class="success-icon-container">
          <div class="success-icon">🎉</div>
          <div class="success-sparkles">
            <span class="sparkle">✨</span>
            <span class="sparkle">✨</span>
            <span class="sparkle">✨</span>
            <span class="sparkle">✨</span>
          </div>
        </div>
        <h2 class="success-title">上传成功！</h2>
        <p class="success-message">您的时光片段已成功保存</p>
        <p class="success-subtitle">每一个美好瞬间都值得被珍藏</p>
        <button @click="closeSuccessModal" class="success-btn">
          <span class="btn-icon">👍</span>
          <span>好的</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import service from '@/utils/request.js'
import provinceCityMapping from '@/assets/map/province_city_mapping.json'

const imagePreviews = ref([])
const imageFiles = ref([]) // 新增：存储真正的图片文件
const videoPreviews = ref([])
const title = ref('')
const description = ref('')
const datetime = ref('')
const province = ref('')
const city = ref('')

const provinces = Object.keys(provinceCityMapping)
const cities = ref([])
const isExpanding = ref(false) // 扩写状态
const isUploading = ref(false) // 上传状态
const showSuccessModal = ref(false) // 成功弹窗状态

const handleImageUpload = (e) => {
  const files = Array.from(e.target.files)
  for (let file of files) {
    const url = URL.createObjectURL(file)
    imagePreviews.value.push(url)
    imageFiles.value.push(file) // 记录文件
  }
  // 清空 input，否则连续上传同一张图片不会触发 change
  e.target.value = ''
}

const handleVideoUpload = (e) => {
  const files = e.target.files
  for (let file of files) {
    const url = URL.createObjectURL(file)
    videoPreviews.value.push(url)
  }
}

const removeImage = (index) => {
  imagePreviews.value.splice(index, 1)
  imageFiles.value.splice(index, 1) // 同步删除文件
}

const removeVideo = (index) => {
  videoPreviews.value.splice(index, 1)
}

const loadCities = () => {
  cities.value = provinceCityMapping[province.value] || []
  city.value = ''
}

const expandDescription = async () => {
  if (!description.value.trim()) {
    alert('请先输入一些描述内容')
    return
  }

  isExpanding.value = true

  try {
    const response = await service({
      method: 'post',
      url: 'issue/expand-text/',
      headers: { 'Content-Type': 'application/json' },
      data: {
        description: description.value
      }
    })

    if (response.data.status === 'success') {
      description.value = response.data.expanded_text
    } else {
      alert('扩写失败：' + (response.data.error || '未知错误'))
    }
  } catch (error) {
    console.error('扩写请求失败:', error)
    alert('扩写失败，请检查网络连接或稍后重试')
  } finally {
    isExpanding.value = false
  }
}

// 上传保存方法
const saveMemory = async () => {
  if (!title.value.trim()) {
    alert('请填写标题');
    return;
  }
  if (!datetime.value) {
    alert('请选择时间');
    return;
  }
  if (!province.value || !city.value) {
    alert('请选择省份和城市');
    return;
  }

  if (imageFiles.value.length === 0) {
    alert('请至少上传一张图片');
    return;
  }

  // 开始上传，显示加载动画
  isUploading.value = true;

  const formData = new FormData();
  formData.append('location', province.value + ' ' + city.value);
  formData.append('date', datetime.value);
  formData.append('title', title.value);
  formData.append('description', description.value);

  // 用 imageFiles 里的文件
  for (let file of imageFiles.value) {
    formData.append('pictures', file);
  }

  try {
    await service({
      method: 'post',
      url: 'issue/create/',
      headers: { 'Content-Type': 'multipart/form-data' },
      data: formData
    });

    // 上传成功，隐藏加载动画，显示成功弹窗
    isUploading.value = false;
    showSuccessModal.value = true;
  } catch (e) {
    // 上传失败，隐藏加载动画，显示错误提示
    isUploading.value = false;
    alert('上传失败，请重试');
    console.error(e);
  }
}

// 关闭成功弹窗
const closeSuccessModal = () => {
  showSuccessModal.value = false;
  // 延迟刷新页面，让用户看到弹窗关闭动画
  setTimeout(() => {
    location.reload();
  }, 300);
}
</script>

<style scoped>
/* 全局样式和动画 */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* 页面容器 */
.page-container {
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #f0f4ff 0%, #e6f7ff 50%, #f0fdfa 100%);
  background-image:
    radial-gradient(circle at 10% 20%, rgba(120, 119, 198, 0.05) 0%, transparent 20%),
    radial-gradient(circle at 90% 80%, rgba(242, 202, 241, 0.08) 0%, transparent 20%),
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.9) 0%, transparent 100%),
    linear-gradient(135deg, #f0f4ff 0%, #e6f7ff 50%, #f0fdfa 100%);
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow-x: hidden;
  font-family: 'Noto Sans SC', sans-serif;
  position: relative;
}

/* 页面头部 */
.page-header {
  position: relative;
  text-align: center;
  margin-bottom: 40px;
  padding: 60px 20px 30px;
  background: linear-gradient(120deg, #a5b4fc 0%, #93c5fd 50%, #6ee7b7 100%);
  border-radius: 0 0 30% 30% / 40px;
  width: 100%;
  box-shadow:
    0 10px 30px rgba(30, 58, 138, 0.08),
    0 5px 15px rgba(147, 197, 253, 0.15);
  overflow: hidden;
}

.header-content {
  position: relative;
  z-index: 2;
  animation: fadeIn 0.8s ease-out;
}

.header-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.3) 0%, transparent 20%),
    radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 20%);
  z-index: 1;
}

.page-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: #1e3a8a;
  margin-bottom: 16px;
  letter-spacing: 2px;
  text-shadow: 0 2px 10px rgba(255, 255, 255, 0.5);
}

.page-subtitle {
  font-size: 1.25rem;
  color: #1e40af;
  opacity: 0.9;
  font-weight: 500;
  letter-spacing: 1px;
}

/* 内容区域 */
.content-wrapper {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 40px;
  padding: 0 20px 60px;
  animation: fadeIn 0.6s ease-out 0.2s both;
}

/* 卡片样式 */
.card {
  background: rgba(255, 255, 255, 0.95);
  width: 100%;
  border-radius: 24px;
  padding: 32px;
  box-shadow:
    0 10px 30px rgba(30, 58, 138, 0.06),
    0 5px 15px rgba(147, 197, 253, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.05);
  position: relative;
  margin-bottom: 0;
  transition: transform 0.3s, box-shadow 0.3s;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow:
    0 15px 35px rgba(30, 58, 138, 0.08),
    0 8px 20px rgba(147, 197, 253, 0.12),
    0 1px 3px rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 28px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  position: relative;
}

.card-icon {
  font-size: 1.8rem;
  opacity: 0.8;
}

.left-icon {
  position: absolute;
  left: 0;
}

.right-icon {
  position: absolute;
  right: 0;
  opacity: 0;
}

.upload-card {
  position: relative;
  border-top: none;
  border-radius: 24px;
  background: linear-gradient(to bottom, #ffffff, #f8faff);
}

.upload-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  border-radius: 24px 24px 0 0;
}

.preview-card {
  position: relative;
  border-top: none;
  border-radius: 24px;
  background: linear-gradient(to bottom, #ffffff, #f0fdf9);
}

.preview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #10b981, #34d399);
  border-radius: 24px 24px 0 0;
}

.card-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  letter-spacing: 1px;
}

.blue-title { color: #2563eb; }
.green-title { color: #059669; }

/* 媒体上传区域 */
.media-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;
}

@media(min-width: 768px) {
  .media-grid { grid-template-columns: repeat(2, 1fr); }
}

.media-item {
  background: rgba(248, 250, 252, 0.8);
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 2px 10px rgba(30, 58, 138, 0.04);
  transition: transform 0.3s, box-shadow 0.3s;
}

.media-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(30, 58, 138, 0.08);
}

.label {
  display: flex;
  align-items: center;
  font-weight: 600;
  margin-bottom: 16px;
  color: #1e40af;
  letter-spacing: 0.5px;
}

.label-icon {
  margin-right: 8px;
  font-size: 1.2rem;
}

.upload-zone {
  border: 2px dashed #93c5fd;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  background: rgba(239, 246, 255, 0.6);
  transition: all 0.3s;
  cursor: pointer;
}

.upload-zone:hover {
  border-color: #60a5fa;
  background: rgba(239, 246, 255, 0.9);
}

.file-input {
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  position: absolute;
  z-index: -1;
}

.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #3b82f6;
  font-weight: 500;
  transition: color 0.3s;
}

.upload-btn:hover {
  color: #2563eb;
}

.upload-icon {
  font-size: 2rem;
  margin-bottom: 8px;
  display: block;
}

.preview-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 20px;
}

.image-thumb {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 3px 10px rgba(30, 58, 138, 0.1);
  transition: transform 0.3s;
}

.image-thumb:hover {
  transform: scale(1.05);
}

.video-thumb {
  width: 140px;
  height: 90px;
  border-radius: 12px;
  box-shadow: 0 3px 10px rgba(30, 58, 138, 0.1);
  transition: transform 0.3s;
}

.video-thumb:hover {
  transform: scale(1.05);
}

/* 文本输入区域 */
.text-inputs {
  margin-top: 36px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.text-group {
  display: flex;
  flex-direction: column;
}

.text-field, .text-area, .select-field {
  padding: 16px;
  border: 2px solid #dbeafe;
  border-radius: 12px;
  font-size: 1rem;
  background: #f8fafc;
  transition: all 0.3s;
  box-shadow: 0 2px 5px rgba(30, 58, 138, 0.03);
}

.text-field:focus, .text-area:focus, .select-field:focus {
  border-color: #60a5fa;
  outline: none;
  background: #fff;
  box-shadow: 0 0 0 4px rgba(96, 165, 250, 0.15);
}

.text-field::placeholder, .text-area::placeholder {
  color: #94a3b8;
}

.text-area {
  resize: vertical;
  min-height: 100px;
}

.description-group {
  position: relative;
}

.expand-btn {
  position: absolute;
  top: 0;
  right: 0;
  background: #eff6ff;
  border: none;
  border-radius: 8px;
  padding: 8px 12px;
  color: #3b82f6;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  transition: all 0.3s;
  box-shadow: 0 2px 5px rgba(30, 58, 138, 0.05);
}

.expand-btn:hover:not(:disabled) {
  background: #dbeafe;
  color: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(30, 58, 138, 0.1);
}

.expand-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  background: #f1f5f9;
  color: #94a3b8;
}

.btn-icon {
  margin-right: 6px;
}

/* 加载动画 */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.datetime-location {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

@media(min-width: 768px) {
  .datetime-location {
    flex-direction: row;
    gap: 32px;
  }

  .datetime-location > .text-group {
    flex: 1;
  }
}

.datetime-field {
  color: #1e40af;
}

.select-row {
  display: flex;
  gap: 16px;
}

.select-field {
  flex: 1;
  min-width: 120px;
  color: #1e40af;
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%233b82f6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  padding-right: 40px;
}

/* 预览区域 */
.preview-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
}

@media(min-width: 640px) {
  .preview-grid { grid-template-columns: repeat(3, 1fr); }
}

@media(min-width: 1024px) {
  .preview-grid { grid-template-columns: repeat(4, 1fr); }
}

.preview-item {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(30, 58, 138, 0.08);
  background: #f8fafc;
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
}

.preview-item:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 8px 25px rgba(30, 58, 138, 0.12);
}

.preview-media {
  width: 100%;
  height: 160px;
  object-fit: cover;
  display: block;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: opacity 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-item:hover .preview-overlay {
  opacity: 1;
}

.delete-btn {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.delete-btn:hover {
  background: #fee2e2;
  transform: scale(1.1);
}

.delete-icon {
  font-size: 1.5rem;
  color: #ef4444;
  line-height: 1;
}

/* 保存按钮区域 */
.save-wrapper {
  text-align: center;
  margin-top: 40px;
}

.save-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #10b981 100%);
  color: white;
  font-size: 1.2rem;
  padding: 16px 48px;
  border: none;
  border-radius: 999px;
  cursor: pointer;
  box-shadow:
    0 10px 25px rgba(37, 99, 235, 0.25),
    0 5px 10px rgba(16, 185, 129, 0.1);
  font-weight: 700;
  letter-spacing: 1px;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.save-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 200%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmer 2s infinite;
  background-size: 200% 100%;
}

.save-btn:hover {
  transform: translateY(-5px) scale(1.03);
  box-shadow:
    0 15px 30px rgba(37, 99, 235, 0.3),
    0 8px 15px rgba(16, 185, 129, 0.15);
}

.save-btn:active {
  transform: translateY(0) scale(0.98);
}

.save-icon {
  margin-right: 10px;
  font-size: 1.3rem;
}

.save-note {
  font-size: 1.05rem;
  color: #6b7280;
  margin-top: 20px;
  letter-spacing: 1px;
  font-style: italic;
}

/* 保存按钮禁用状态 */
.save-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none !important;
}

/* 加载旋转器 */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

/* 加载遮罩层 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

.loading-container {
  background: white;
  border-radius: 24px;
  padding: 48px;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 400px;
  width: 90%;
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.loading-animation {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 24px;
  gap: 8px;
}

.loading-circle {
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #3b82f6, #10b981);
  border-radius: 50%;
  animation: bounce 1.4s ease-in-out infinite both;
}

.loading-circle:nth-child(1) { animation-delay: -0.32s; }
.loading-circle:nth-child(2) { animation-delay: -0.16s; }
.loading-circle:nth-child(3) { animation-delay: 0s; }

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.loading-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e40af;
  margin-bottom: 8px;
  letter-spacing: 1px;
}

.loading-subtitle {
  font-size: 1rem;
  color: #6b7280;
  margin-bottom: 24px;
}

.loading-progress {
  width: 100%;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #10b981);
  border-radius: 3px;
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0% {
    width: 0%;
    transform: translateX(-100%);
  }
  50% {
    width: 100%;
    transform: translateX(0%);
  }
  100% {
    width: 100%;
    transform: translateX(100%);
  }
}

/* 成功弹窗样式 */
.success-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease-out;
}

.success-modal {
  background: white;
  border-radius: 24px;
  padding: 48px;
  text-align: center;
  box-shadow: 0 25px 70px rgba(0, 0, 0, 0.3);
  max-width: 450px;
  width: 90%;
  position: relative;
  animation: successSlideUp 0.5s ease-out;
  overflow: hidden;
}

@keyframes successSlideUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.success-modal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #10b981, #34d399, #6ee7b7);
  animation: shimmer 2s infinite;
}

.success-icon-container {
  position: relative;
  margin-bottom: 24px;
  display: inline-block;
}

.success-icon {
  font-size: 4rem;
  animation: successBounce 0.8s ease-out 0.2s both;
}

@keyframes successBounce {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.success-sparkles {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.sparkle {
  position: absolute;
  font-size: 1.5rem;
  animation: sparkleFloat 2s ease-in-out infinite;
}

.sparkle:nth-child(1) {
  top: -30px;
  left: -30px;
  animation-delay: 0.5s;
}

.sparkle:nth-child(2) {
  top: -30px;
  right: -30px;
  animation-delay: 0.7s;
}

.sparkle:nth-child(3) {
  bottom: -30px;
  left: -30px;
  animation-delay: 0.9s;
}

.sparkle:nth-child(4) {
  bottom: -30px;
  right: -30px;
  animation-delay: 1.1s;
}

@keyframes sparkleFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 1;
  }
}

.success-title {
  font-size: 2rem;
  font-weight: 700;
  color: #059669;
  margin-bottom: 12px;
  letter-spacing: 1px;
  animation: fadeIn 0.6s ease-out 0.4s both;
}

.success-message {
  font-size: 1.2rem;
  color: #374151;
  margin-bottom: 8px;
  font-weight: 500;
  animation: fadeIn 0.6s ease-out 0.6s both;
}

.success-subtitle {
  font-size: 1rem;
  color: #6b7280;
  margin-bottom: 32px;
  font-style: italic;
  animation: fadeIn 0.6s ease-out 0.8s both;
}

.success-btn {
  background: linear-gradient(135deg, #10b981, #34d399);
  color: white;
  font-size: 1.1rem;
  padding: 14px 32px;
  border: none;
  border-radius: 999px;
  cursor: pointer;
  font-weight: 600;
  letter-spacing: 0.5px;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
  animation: fadeIn 0.6s ease-out 1s both;
}

.success-btn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 12px 25px rgba(16, 185, 129, 0.4);
}

.success-btn:active {
  transform: translateY(0) scale(0.98);
}

.success-btn .btn-icon {
  margin-right: 8px;
  font-size: 1.2rem;
}
</style>
