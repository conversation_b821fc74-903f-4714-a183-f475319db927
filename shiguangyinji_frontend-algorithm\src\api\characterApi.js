import service from '@/utils/request';

/**
 * 获取所有好友/人物列表
 * @returns {Promise<Object>} 包含好友列表的响应对象
 */
export async function getCharacters() {
    try {
        const token = localStorage.getItem('access_token');
        if (!token) {
            throw new Error('请先登录');
        }

        const response = await service({
            method: 'get',
            url: 'character/characters/',
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });

        return response.data;
    } catch (error) {
        console.error('获取好友列表失败:', error);
        throw error;
    }
}

/**
 * 创建新的人物角色
 * @param {Object} characterData 人物数据
 * @param {string} characterData.name 姓名
 * @param {string} characterData.relationship 关系
 * @param {string} characterData.gender 性别
 * @param {string} characterData.birthday 生日
 * @param {File} characterData.avatar 头像文件
 * @returns {Promise<Object>} 创建结果
 */
export async function createCharacter(characterData) {
    try {
        const token = localStorage.getItem('access_token');
        if (!token) {
            throw new Error('请先登录');
        }

        const formData = new FormData();
        formData.append('name', characterData.name);
        formData.append('relationship', characterData.relationship);
        formData.append('gender', characterData.gender || 'male');
        if (characterData.birthday) {
            formData.append('birthday', characterData.birthday);
        }
        if (characterData.avatar) {
            formData.append('avatar', characterData.avatar);
        }

        const response = await service({
            method: 'post',
            url: 'character/characters/',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'multipart/form-data',
            },
            data: formData,
        });

        return response.data;
    } catch (error) {
        console.error('创建人物失败:', error);
        throw error;
    }
}

/**
 * 更新人物角色信息
 * @param {number} characterId 人物ID
 * @param {Object} characterData 人物数据
 * @param {string} characterData.name 姓名
 * @param {string} characterData.relationship 关系
 * @param {string} characterData.gender 性别
 * @param {string} characterData.birthday 生日
 * @param {File} characterData.avatar 头像文件（可选）
 * @returns {Promise<Object>} 更新结果
 */
export async function updateCharacter(characterId, characterData) {
    try {
        const token = localStorage.getItem('access_token');
        if (!token) {
            throw new Error('请先登录');
        }

        const formData = new FormData();
        formData.append('name', characterData.name);
        formData.append('relationship', characterData.relationship);
        formData.append('gender', characterData.gender || 'male');
        if (characterData.birthday) {
            formData.append('birthday', characterData.birthday);
        }
        if (characterData.avatar) {
            formData.append('avatar', characterData.avatar);
        }

        const response = await service({
            method: 'put',
            url: `character/characters/${characterId}/`,
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'multipart/form-data',
            },
            data: formData,
        });

        return response.data;
    } catch (error) {
        console.error('更新人物失败:', error);
        throw error;
    }
}

/**
 * 获取单个人物详情
 * @param {number} characterId 人物ID
 * @returns {Promise<Object>} 人物详情
 */
export async function getCharacterDetail(characterId) {
    try {
        const token = localStorage.getItem('access_token');
        if (!token) {
            throw new Error('请先登录');
        }

        const response = await service({
            method: 'get',
            url: `character/characters/${characterId}/`,
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });

        return response.data;
    } catch (error) {
        console.error('获取人物详情失败:', error);
        throw error;
    }
}





/**
 * 删除好友/人物
 * @param {number} characterId 好友/人物ID
 * @returns {Promise<Object>} 删除结果
 */
export async function deleteCharacter(characterId) {
    try {
        const token = localStorage.getItem('access_token');
        if (!token) {
            throw new Error('请先登录');
        }

        const response = await service({
            method: 'delete',
            url: `character/characters/${characterId}/`,
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });

        return response.data;
    } catch (error) {
        console.error('删除好友失败:', error);
        throw error;
    }
}

/**
 * 获取好友的事件列表（模拟，实际需要后端支持）
 * @param {number} characterId 好友/人物ID
 * @returns {Promise<Array>} 事件列表
 */
export async function getCharacterEvents(characterId) {
    // 这里是模拟数据，实际应该调用后端API
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                status: 'success',
                data: [
                    {
                        id: 1,
                        title: '旅行记录',
                        date: '2023-10-15',
                        location: '北京',
                        description: '和朋友一起游览了故宫博物院，感受了深厚的历史文化底蕴。',
                        images: ['https://example.com/image1.jpg']
                    },
                    {
                        id: 2,
                        title: '聚餐',
                        date: '2023-09-20',
                        location: '上海',
                        description: '在外滩附近的餐厅共进晚餐，分享了各自的近况和未来计划。',
                        images: ['https://example.com/image2.jpg']
                    }
                ]
            });
        }, 300);
    });
}

/**
 * 获取好友的视频列表（模拟，实际需要后端支持）
 * @param {number} characterId 好友/人物ID
 * @returns {Promise<Array>} 视频列表
 */
export async function getCharacterVideos(characterId) {
    // 这里是模拟数据，实际应该调用后端API
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                status: 'success',
                data: [
                    {
                        id: 1,
                        title: '毕业旅行',
                        date: '2023-07-10',
                        duration: '3:45',
                        thumbnail: 'https://example.com/thumbnail1.jpg',
                        url: 'https://example.com/video1.mp4'
                    },
                    {
                        id: 2,
                        title: '生日派对',
                        date: '2023-05-22',
                        duration: '2:30',
                        thumbnail: 'https://example.com/thumbnail2.jpg',
                        url: 'https://example.com/video2.mp4'
                    }
                ]
            });
        }, 300);
    });
}
