<template>
  <div class="friends-page">
    <div class="page-header">
      <h1 class="page-title">我的好友</h1>
      <div class="header-actions">
        <button class="add-friend-btn" @click="showAddFriendModal = true">
          <span class="icon">+</span>
          <span>添加好友</span>
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在加载好友数据...</p>
    </div>

    <!-- 错误提示 -->
    <div v-else-if="error" class="error-container">
      <p>{{ error }}</p>
      <button @click="fetchFriends" class="retry-btn">重试</button>
    </div>

    <!-- 空状态 -->
    <div v-else-if="friends.length === 0" class="empty-container">
      <div class="empty-icon">👥</div>
      <p>您还没有添加任何好友</p>
      <button @click="showAddFriendModal = true" class="add-btn">添加第一个好友</button>
    </div>

    <!-- 好友列表 -->
    <div v-else class="friends-container">
      <!-- 关系图谱视图 -->
      <div class="relationship-graph">
        <h2>好友关系图谱</h2>
        <div class="graph-container" ref="graphContainer">
          <!-- 图谱将通过JS渲染 -->
        </div>
      </div>

      <!-- 好友列表视图 -->
      <div class="friends-list">
        <h2>好友列表</h2>
        <div class="friends-grid">
          <div
            v-for="friend in friends.filter(f => f.relationship !== '自己')"
            :key="friend.character_id"
            class="friend-card"
            @click="selectFriend(friend)"
            :class="{ 'active': selectedFriend && selectedFriend.character_id === friend.character_id }"
          >
            <div class="friend-avatar">
              <img :src="friend.avatar || defaultAvatar" :alt="friend.name">
            </div>
            <div class="friend-info">
              <h3 class="friend-name">{{ friend.name }}</h3>
              <p class="friend-relationship">{{ friend.relationship }}</p>
            </div>
            <div class="friend-actions">
              <button class="action-btn edit-btn" @click.stop="editFriend(friend)">
                <span>编辑</span>
              </button>
              <button class="action-btn delete-btn" @click.stop="confirmDeleteFriend(friend)">
                <span>删除</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 好友详情侧边栏 -->
    <div class="friend-detail-sidebar" :class="{ 'active': selectedFriend }" v-if="selectedFriend">
      <div class="sidebar-header">
        <h2>好友详情</h2>
        <button class="close-btn" @click="selectedFriend = null">×</button>
      </div>
      <div class="sidebar-content">
        <div class="detail-avatar">
          <img :src="selectedFriend.avatar || defaultAvatar" :alt="selectedFriend.name">
        </div>
        <div class="detail-info">
          <div class="info-item">
            <span class="label">姓名:</span>
            <span class="value">{{ selectedFriend.name }}</span>
          </div>
          <div class="info-item">
            <span class="label">关系:</span>
            <span class="value">{{ selectedFriend.relationship }}</span>
          </div>
          <div class="info-item">
            <span class="label">性别:</span>
            <span class="value">{{ selectedFriend.gender === 'male' ? '男' : '女' }}</span>
          </div>
          <div class="info-item" v-if="selectedFriend.birthday">
            <span class="label">生日:</span>
            <span class="value">{{ selectedFriend.birthday }}</span>
          </div>
        </div>
        <div class="detail-actions">
          <button class="detail-btn" @click="viewFriendEvents(selectedFriend)">查看时光片段</button>
          <button class="detail-btn" @click="viewFriendVideos(selectedFriend)">查看视频轨迹</button>
        </div>
      </div>
    </div>

    <!-- 添加/编辑好友模态框 -->
    <div class="modal" v-if="showAddFriendModal || editingFriend">
      <div class="modal-content">
        <div class="modal-header">
          <h2>{{ editingFriend ? '编辑好友' : '添加好友' }}</h2>
          <button class="close-btn" @click="closeModal">×</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="submitFriendForm">
            <div class="form-group">
              <label for="name">姓名</label>
              <input type="text" id="name" v-model="friendForm.name" required>
            </div>
            <div class="form-group">
              <label for="relationship">关系</label>
              <input type="text" id="relationship" v-model="friendForm.relationship" required>
            </div>
            <div class="form-group">
              <label>性别</label>
              <div class="radio-group">
                <label>
                  <input type="radio" v-model="friendForm.gender" value="male">
                  男
                </label>
                <label>
                  <input type="radio" v-model="friendForm.gender" value="female">
                  女
                </label>
              </div>
            </div>
            <div class="form-group">
              <label for="birthday">生日</label>
              <input type="date" id="birthday" v-model="friendForm.birthday">
            </div>
            <div class="form-group">
              <label for="avatar">头像</label>
              <input type="file" id="avatar" @change="handleAvatarChange" accept="image/*">
              <div class="avatar-preview" v-if="avatarPreview">
                <img :src="avatarPreview" alt="头像预览">
              </div>
            </div>
            <div class="form-actions">
              <button type="button" class="cancel-btn" @click="closeModal">取消</button>
              <button type="submit" class="submit-btn">{{ editingFriend ? '保存' : '添加' }}</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal" v-if="showDeleteModal">
      <div class="modal-content delete-modal">
        <div class="modal-header">
          <h2>确认删除</h2>
          <button class="close-btn" @click="showDeleteModal = false">×</button>
        </div>
        <div class="modal-body">
          <p>确定要删除好友 "{{ friendToDelete?.name }}" 吗？此操作不可撤销。</p>
          <div class="form-actions">
            <button type="button" class="cancel-btn" @click="showDeleteModal = false">取消</button>
            <button type="button" class="delete-confirm-btn" @click="deleteFriend">删除</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { getCharacters, createCharacter, updateCharacter, deleteCharacter } from '@/api/characterApi';

const router = useRouter();
const defaultAvatar = new URL('@/assets/images/test.png', import.meta.url).href;

// 状态变量
const loading = ref(true);
const error = ref(null);
const friends = ref([]);
const selectedFriend = ref(null);
const showAddFriendModal = ref(false);
const editingFriend = ref(null);
const showDeleteModal = ref(false);
const friendToDelete = ref(null);
const graphContainer = ref(null);

// 图谱状态变量
const graphState = ref({
  nodes: [],
  isDragging: false,
  dragNode: null,
  dragOffset: { x: 0, y: 0 },
  svg: null,
  width: 500,
  height: 500
});

// 表单数据
const friendForm = ref({
  name: '',
  relationship: '',
  gender: 'male',
  birthday: '',
  avatar: null
});
const avatarPreview = ref(null);

// 模拟好友数据
const mockFriends = [
  {
    character_id: 1,
    name: '张三',
    relationship: '大学同学',
    gender: 'male',
    birthday: '1995-05-15',
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg'
  },
  {
    character_id: 2,
    name: '李四',
    relationship: '高中好友',
    gender: 'male',
    birthday: '1994-08-22',
    avatar: 'https://randomuser.me/api/portraits/men/2.jpg'
  },
  {
    character_id: 3,
    name: '王五',
    relationship: '同事',
    gender: 'male',
    birthday: '1992-11-10',
    avatar: 'https://randomuser.me/api/portraits/men/3.jpg'
  },
  {
    character_id: 4,
    name: '赵六',
    relationship: '大学室友',
    gender: 'male',
    birthday: '1995-03-28',
    avatar: 'https://randomuser.me/api/portraits/men/4.jpg'
  },
  {
    character_id: 5,
    name: '小红',
    relationship: '表妹',
    gender: 'female',
    birthday: '1998-07-19',
    avatar: 'https://randomuser.me/api/portraits/women/1.jpg'
  },
  {
    character_id: 6,
    name: '小芳',
    relationship: '同事',
    gender: 'female',
    birthday: '1996-12-05',
    avatar: 'https://randomuser.me/api/portraits/women/2.jpg'
  }
];

// 获取好友列表
const fetchFriends = async () => {
  loading.value = true;
  error.value = null;
  try {
    // 尝试从API获取数据
    const response = await getCharacters();
    if (response.status === 'success') {
      friends.value = response.data;
    } else {
      // 如果API返回失败，使用模拟数据
      console.log('API返回失败，使用模拟数据');
      friends.value = mockFriends;
    }
  } catch (err) {
    // 如果API调用出错，使用模拟数据
    console.error('获取好友列表出错:', err);
    console.log('使用模拟数据');
    friends.value = mockFriends;
  } finally {
    loading.value = false;
    await nextTick();
    if (friends.value.length > 0) {
      initRelationshipGraph();
    }
  }
};

// 选择好友
const selectFriend = (friend) => {
  selectedFriend.value = friend;
};

// 编辑好友
const editFriend = (friend) => {
  editingFriend.value = friend;
  friendForm.value = {
    name: friend.name,
    relationship: friend.relationship,
    gender: friend.gender,
    birthday: friend.birthday || '',
    avatar: null
  };
  avatarPreview.value = friend.avatar;
};

// 确认删除好友
const confirmDeleteFriend = (friend) => {
  friendToDelete.value = friend;
  showDeleteModal.value = true;
};

// 删除好友
const deleteFriend = async () => {
  try {
    try {
      // 尝试调用API删除好友
      const response = await deleteCharacter(friendToDelete.value.character_id);
      if (response.status === 'success') {
        // API调用成功，从列表中移除
        friends.value = friends.value.filter(f => f.character_id !== friendToDelete.value.character_id);
      }
    } catch (error) {
      console.error('API调用失败，使用本地数据删除', error);
      // API调用失败，直接从本地列表中移除
      friends.value = friends.value.filter(f => f.character_id !== friendToDelete.value.character_id);
    }

    // 如果当前选中的是被删除的好友，清除选中状态
    if (selectedFriend.value && selectedFriend.value.character_id === friendToDelete.value.character_id) {
      selectedFriend.value = null;
    }

    // 重新初始化关系图谱
    if (friends.value.length > 0) {
      initRelationshipGraph();
    }
  } catch (err) {
    console.error('删除好友出错:', err);
    alert('删除好友时发生错误，请稍后重试');
  } finally {
    showDeleteModal.value = false;
    friendToDelete.value = null;
  }
};

// 处理头像变更
const handleAvatarChange = (event) => {
  const file = event.target.files[0];
  if (file) {
    friendForm.value.avatar = file;
    avatarPreview.value = URL.createObjectURL(file);
  }
};

// 提交好友表单
const submitFriendForm = async () => {
  try {
    if (editingFriend.value) {
      try {
        // 尝试更新好友
        const response = await updateCharacter(editingFriend.value.character_id, friendForm.value);
        if (response.status === 'success') {
          // 更新列表中的数据
          fetchFriends();
        }
      } catch (error) {
        console.error('API调用失败，使用模拟数据更新', error);
        // 使用模拟数据更新
        const index = friends.value.findIndex(f => f.character_id === editingFriend.value.character_id);
        if (index !== -1) {
          friends.value[index] = {
            ...friends.value[index],
            name: friendForm.value.name,
            relationship: friendForm.value.relationship,
            gender: friendForm.value.gender,
            birthday: friendForm.value.birthday
          };

          // 如果有新头像预览，更新头像
          if (avatarPreview.value && avatarPreview.value !== friends.value[index].avatar) {
            friends.value[index].avatar = avatarPreview.value;
          }
        }
      }
    } else {
      try {
        // 尝试创建好友
        const response = await createCharacter(friendForm.value);
        if (response.status === 'success') {
          // 重新获取好友列表
          fetchFriends();
        }
      } catch (error) {
        console.error('API调用失败，使用模拟数据创建', error);
        // 使用模拟数据创建
        const newFriend = {
          character_id: Math.max(0, ...friends.value.map(f => f.character_id)) + 1,
          name: friendForm.value.name,
          relationship: friendForm.value.relationship,
          gender: friendForm.value.gender,
          birthday: friendForm.value.birthday,
          avatar: avatarPreview.value || defaultAvatar
        };
        friends.value.push(newFriend);
      }
    }
    closeModal();
  } catch (err) {
    console.error('提交好友表单出错:', err);
    alert('操作失败，请稍后重试');
  }
};

// 关闭模态框
const closeModal = () => {
  showAddFriendModal.value = false;
  editingFriend.value = null;
  friendForm.value = {
    name: '',
    relationship: '',
    gender: 'male',
    birthday: '',
    avatar: null
  };
  avatarPreview.value = null;
};

// 查看好友的时光片段
const viewFriendEvents = (friend) => {
  // 这里可以跳转到好友的时光片段页面
  alert(`查看 ${friend.name} 的时光片段功能待实现`);
};

// 查看好友的视频轨迹
const viewFriendVideos = (friend) => {
  // 这里可以跳转到好友的视频轨迹页面
  alert(`查看 ${friend.name} 的视频轨迹功能待实现`);
};

// 创建箭头标记
const createArrowMarker = (svg) => {
  const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
  const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');

  marker.setAttribute('id', 'arrowhead');
  marker.setAttribute('markerWidth', '6');
  marker.setAttribute('markerHeight', '4');
  marker.setAttribute('refX', '5');
  marker.setAttribute('refY', '2');
  marker.setAttribute('orient', 'auto');
  marker.setAttribute('markerUnits', 'strokeWidth');

  const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
  polygon.setAttribute('points', '0 0, 6 2, 0 4');
  polygon.setAttribute('fill', '#4f46e5');
  polygon.setAttribute('stroke', '#4f46e5');
  polygon.setAttribute('stroke-width', '0.5');

  marker.appendChild(polygon);
  defs.appendChild(marker);
  svg.appendChild(defs);
};

// 计算两点间的距离
const distance = (p1, p2) => {
  return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
};

// 计算箭头终点（考虑节点半径）
const getArrowEndPoint = (start, end, radius) => {
  const dx = end.x - start.x;
  const dy = end.y - start.y;
  const dist = Math.sqrt(dx * dx + dy * dy);

  if (dist === 0) return end;

  const ratio = (dist - radius) / dist;
  return {
    x: start.x + dx * ratio,
    y: start.y + dy * ratio
  };
};

// 更新连线和箭头
const updateConnections = () => {
  if (!graphState.value.svg) return;

  const svg = graphState.value.svg;
  const nodes = graphState.value.nodes;

  // 移除所有现有的连线
  const existingLines = svg.querySelectorAll('.connection-line, .relationship-text, .relationship-bg');
  existingLines.forEach(el => el.remove());

  // 找到中心节点（自己）
  const centerNode = nodes.find(n => n.isCenter);
  if (!centerNode) return;

  // 绘制从中心到其他节点的箭头
  nodes.forEach(node => {
    if (node.isCenter) return;

    const startPoint = { x: centerNode.x, y: centerNode.y };
    const endPoint = getArrowEndPoint(startPoint, { x: node.x, y: node.y }, node.radius);

    // 创建箭头线
    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
    line.setAttribute('class', 'connection-line');
    line.setAttribute('x1', startPoint.x);
    line.setAttribute('y1', startPoint.y);
    line.setAttribute('x2', endPoint.x);
    line.setAttribute('y2', endPoint.y);
    line.setAttribute('stroke', '#4f46e5');
    line.setAttribute('stroke-width', '3');
    line.setAttribute('marker-end', 'url(#arrowhead)');
    line.setAttribute('opacity', '0.8');

    svg.appendChild(line);

    // 添加关系文字
    const midX = (startPoint.x + node.x) / 2;
    const midY = (startPoint.y + node.y) / 2;

    // 添加文字背景
    const textBg = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    textBg.setAttribute('class', 'relationship-bg');
    textBg.setAttribute('x', midX - 30);
    textBg.setAttribute('y', midY - 12);
    textBg.setAttribute('width', '60');
    textBg.setAttribute('height', '20');
    textBg.setAttribute('fill', 'white');
    textBg.setAttribute('stroke', '#4f46e5');
    textBg.setAttribute('stroke-width', '1');
    textBg.setAttribute('rx', '10');
    textBg.setAttribute('opacity', '0.9');

    const relText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    relText.setAttribute('class', 'relationship-text');
    relText.setAttribute('x', midX);
    relText.setAttribute('y', midY + 3);
    relText.setAttribute('text-anchor', 'middle');
    relText.setAttribute('font-size', '11');
    relText.setAttribute('font-weight', '600');
    relText.setAttribute('fill', '#4f46e5');
    relText.textContent = node.relationship;

    svg.appendChild(textBg);
    svg.appendChild(relText);
  });
};

// 创建节点元素
const createNodeElement = (svg, node) => {
  const svgNS = 'http://www.w3.org/2000/svg';

  // 创建节点组
  const nodeGroup = document.createElementNS(svgNS, 'g');
  nodeGroup.setAttribute('class', 'node-group');
  nodeGroup.setAttribute('data-node-id', node.id);
  nodeGroup.style.cursor = 'grab';

  // 创建外圈装饰
  const outerCircle = document.createElementNS(svgNS, 'circle');
  outerCircle.setAttribute('cx', node.x);
  outerCircle.setAttribute('cy', node.y);
  outerCircle.setAttribute('r', node.radius + 5);
  outerCircle.setAttribute('fill', 'none');
  outerCircle.setAttribute('stroke', node.isCenter ? '#6366f1' : (node.gender === 'male' ? '#3b82f6' : '#ec4899'));
  outerCircle.setAttribute('stroke-width', '2');
  outerCircle.setAttribute('opacity', '0.3');
  outerCircle.setAttribute('class', 'outer-circle');

  // 创建主圆圈
  const circle = document.createElementNS(svgNS, 'circle');
  circle.setAttribute('cx', node.x);
  circle.setAttribute('cy', node.y);
  circle.setAttribute('r', node.radius);
  circle.setAttribute('fill', node.isCenter ? '#6366f1' : (node.gender === 'male' ? '#dbeafe' : '#fce7f3'));
  circle.setAttribute('stroke', node.isCenter ? '#4f46e5' : (node.gender === 'male' ? '#3b82f6' : '#ec4899'));
  circle.setAttribute('stroke-width', '3');
  circle.setAttribute('class', 'main-circle');

  // 创建头像
  const avatar = document.createElementNS(svgNS, 'image');
  avatar.setAttributeNS('http://www.w3.org/1999/xlink', 'href', node.avatar);
  avatar.setAttribute('x', node.x - node.radius + 8);
  avatar.setAttribute('y', node.y - node.radius + 8);
  avatar.setAttribute('width', (node.radius - 8) * 2);
  avatar.setAttribute('height', (node.radius - 8) * 2);
  avatar.setAttribute('clip-path', `circle(${node.radius - 8}px at ${node.radius - 8}px ${node.radius - 8}px)`);
  avatar.setAttribute('class', 'avatar-image');

  // 创建名字文本
  const nameText = document.createElementNS(svgNS, 'text');
  nameText.setAttribute('x', node.x);
  nameText.setAttribute('y', node.y + node.radius + 20);
  nameText.setAttribute('text-anchor', 'middle');
  nameText.setAttribute('font-size', node.isCenter ? '16' : '14');
  nameText.setAttribute('font-weight', '600');
  nameText.setAttribute('fill', node.isCenter ? '#4f46e5' : '#374151');
  nameText.setAttribute('class', 'name-text');
  nameText.textContent = node.name;

  // 添加悬停效果
  nodeGroup.addEventListener('mouseenter', () => {
    outerCircle.setAttribute('opacity', '0.6');
    circle.style.transform = 'scale(1.1)';
    circle.style.transformOrigin = `${node.x}px ${node.y}px`;
    circle.style.transition = 'transform 0.2s ease';
  });

  nodeGroup.addEventListener('mouseleave', () => {
    if (!graphState.value.isDragging) {
      outerCircle.setAttribute('opacity', '0.3');
      circle.style.transform = 'scale(1)';
    }
  });

  // 组装节点
  nodeGroup.appendChild(outerCircle);
  nodeGroup.appendChild(circle);
  nodeGroup.appendChild(avatar);
  nodeGroup.appendChild(nameText);

  svg.appendChild(nodeGroup);
};

// 添加拖拽事件监听
const addDragListeners = (svg) => {
  let isDragging = false;
  let dragNode = null;
  let dragOffset = { x: 0, y: 0 };

  const handleMouseDown = (e) => {
    const nodeGroup = e.target.closest('.node-group');
    if (!nodeGroup) return;

    const nodeId = nodeGroup.getAttribute('data-node-id');
    const node = graphState.value.nodes.find(n => n.id == nodeId);

    if (!node || node.isCenter) return; // 中心节点不能拖动

    isDragging = true;
    dragNode = node;
    graphState.value.isDragging = true;
    graphState.value.dragNode = node;

    const rect = svg.getBoundingClientRect();
    dragOffset.x = e.clientX - rect.left - node.x;
    dragOffset.y = e.clientY - rect.top - node.y;

    nodeGroup.style.cursor = 'grabbing';
    e.preventDefault();
  };

  const handleMouseMove = (e) => {
    if (!isDragging || !dragNode) return;

    const rect = svg.getBoundingClientRect();
    const newX = e.clientX - rect.left - dragOffset.x;
    const newY = e.clientY - rect.top - dragOffset.y;

    // 限制在SVG边界内
    const margin = dragNode.radius + 10;
    const constrainedX = Math.max(margin, Math.min(graphState.value.width - margin, newX));
    const constrainedY = Math.max(margin, Math.min(graphState.value.height - margin, newY));

    // 更新节点位置
    dragNode.x = constrainedX;
    dragNode.y = constrainedY;

    // 更新节点元素位置
    const nodeGroup = svg.querySelector(`[data-node-id="${dragNode.id}"]`);
    if (nodeGroup) {
      const elements = nodeGroup.querySelectorAll('.outer-circle, .main-circle');
      elements.forEach(el => {
        el.setAttribute('cx', constrainedX);
        el.setAttribute('cy', constrainedY);
      });

      const avatar = nodeGroup.querySelector('.avatar-image');
      if (avatar) {
        avatar.setAttribute('x', constrainedX - dragNode.radius + 8);
        avatar.setAttribute('y', constrainedY - dragNode.radius + 8);
      }

      const nameText = nodeGroup.querySelector('.name-text');
      if (nameText) {
        nameText.setAttribute('x', constrainedX);
        nameText.setAttribute('y', constrainedY + dragNode.radius + 20);
      }
    }

    // 更新连线
    updateConnections();
  };

  const handleMouseUp = () => {
    if (isDragging && dragNode) {
      const nodeGroup = svg.querySelector(`[data-node-id="${dragNode.id}"]`);
      if (nodeGroup) {
        nodeGroup.style.cursor = 'grab';
        const circle = nodeGroup.querySelector('.main-circle');
        const outerCircle = nodeGroup.querySelector('.outer-circle');
        if (circle) circle.style.transform = 'scale(1)';
        if (outerCircle) outerCircle.setAttribute('opacity', '0.3');
      }
    }

    isDragging = false;
    dragNode = null;
    graphState.value.isDragging = false;
    graphState.value.dragNode = null;
  };

  svg.addEventListener('mousedown', handleMouseDown);
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};

// 初始化关系图谱
const initRelationshipGraph = () => {
  if (graphContainer.value) {
    graphContainer.value.innerHTML = '';

    const width = graphState.value.width;
    const height = graphState.value.height;
    const centerX = width / 2;
    const centerY = height / 2;

    const svgNS = 'http://www.w3.org/2000/svg';
    const svg = document.createElementNS(svgNS, 'svg');
    svg.setAttribute('width', width);
    svg.setAttribute('height', height);

    // 保存SVG引用
    graphState.value.svg = svg;

    // 创建箭头标记
    createArrowMarker(svg);

    // 找到自己
    const myself = friends.value.find(f => f.relationship === '自己');
    // 其他好友
    const others = friends.value.filter(f => f.relationship !== '自己');

    // 初始化节点数据
    graphState.value.nodes = [];

    // 创建中心节点（自己）
    const centerNode = {
      id: myself ? myself.character_id : 'self',
      name: myself ? myself.name : '我',
      x: centerX,
      y: centerY,
      radius: 40,
      isCenter: true,
      avatar: myself ? myself.avatar : defaultAvatar,
      gender: myself ? myself.gender : 'male',
      relationship: '自己'
    };
    graphState.value.nodes.push(centerNode);

    // 创建其他好友节点
    const n = others.length;
    if (n > 0) {
      const radius = 150;
      others.forEach((friend, i) => {
        const angle = (2 * Math.PI / n) * i - Math.PI / 2;
        const fx = centerX + radius * Math.cos(angle);
        const fy = centerY + radius * Math.sin(angle);

        const friendNode = {
          id: friend.character_id,
          name: friend.name,
          x: fx,
          y: fy,
          radius: 35,
          isCenter: false,
          avatar: friend.avatar || defaultAvatar,
          gender: friend.gender,
          relationship: friend.relationship
        };
        graphState.value.nodes.push(friendNode);
      });
    }

    // 绘制节点
    graphState.value.nodes.forEach(node => {
      createNodeElement(svg, node);
    });

    // 绘制连线
    updateConnections();

    // 添加拖拽事件监听
    addDragListeners(svg);

    graphContainer.value.appendChild(svg);
  }
};

// 组件挂载时获取好友列表
onMounted(() => {
  fetchFriends();
});
</script>

<style scoped>
.friends-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4ff 0%, #e6f7ff 50%, #f0fdfa 100%);
  background-image:
    radial-gradient(circle at 10% 20%, rgba(120, 119, 198, 0.05) 0%, transparent 20%),
    radial-gradient(circle at 90% 80%, rgba(242, 202, 241, 0.08) 0%, transparent 20%),
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.9) 0%, transparent 100%),
    linear-gradient(135deg, #f0f4ff 0%, #e6f7ff 50%, #f0fdfa 100%);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 2rem;
  color: var(--primary-dark);
  margin: 0;
}

.add-friend-btn {
  display: flex;
  align-items: center;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.add-friend-btn:hover {
  background-color: var(--accent-dark);
  transform: translateY(-2px);
}

.add-friend-btn .icon {
  margin-right: 8px;
  font-size: 1.2rem;
}

.loading-container, .error-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--accent-color);
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.retry-btn, .add-btn {
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  cursor: pointer;
  font-weight: 600;
  margin-top: 20px;
  transition: all 0.3s ease;
}

.retry-btn:hover, .add-btn:hover {
  background-color: var(--accent-dark);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  color: var(--text-secondary);
}

.friends-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.relationship-graph, .friends-list {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 20px;
}

.relationship-graph h2, .friends-list h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: var(--primary-dark);
  font-size: 1.5rem;
}

.graph-container {
  height: 500px;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  position: relative;
  overflow: hidden;
}

.graph-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 30%, rgba(79, 70, 229, 0.05) 0%, transparent 30%),
    radial-gradient(circle at 80% 70%, rgba(59, 130, 246, 0.05) 0%, transparent 30%);
  pointer-events: none;
}

/* 关系图谱SVG样式 */
.relationship-svg {
  position: relative;
  z-index: 1;
}

/* 节点组样式 */
.node-group {
  transition: all 0.3s ease;
}

.node-group:hover .outer-circle {
  stroke-width: 3;
  opacity: 0.8 !important;
}

.node-group:hover .main-circle {
  filter: drop-shadow(0 4px 12px rgba(79, 70, 229, 0.3));
}

.node-group:hover .name-text {
  font-weight: 700;
}

/* 连线动画 */
@keyframes drawLine {
  from {
    stroke-dashoffset: 100%;
  }
  to {
    stroke-dashoffset: 0;
  }
}

.connection-line {
  animation: drawLine 1s ease-out forwards;
  stroke-dasharray: 100%;
  stroke-dashoffset: 100%;
}

/* 关系文字动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.relationship-text, .relationship-bg {
  animation: fadeIn 0.5s ease-out 0.5s forwards;
  opacity: 0;
}

/* 拖拽时的样式 */
.node-group.dragging {
  cursor: grabbing !important;
}

.node-group.dragging .main-circle {
  transform: scale(1.1);
  filter: drop-shadow(0 6px 20px rgba(79, 70, 229, 0.4));
}

.node-group.dragging .outer-circle {
  opacity: 0.8 !important;
  stroke-width: 4;
}

.friends-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  max-height: 500px;
  overflow-y: auto;
  padding-right: 10px;
}

.friend-card {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 15px;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

.friend-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.friend-card.active {
  border-color: var(--accent-color);
}

.friend-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto 15px;
  border: 3px solid var(--accent-light);
}

.friend-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.friend-info {
  text-align: center;
  margin-bottom: 15px;
}

.friend-name {
  margin: 0 0 5px;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.friend-relationship {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.friend-actions {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
}

.action-btn {
  flex: 1;
  padding: 8px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.edit-btn {
  background-color: var(--primary-light);
  color: var(--primary-dark);
  margin-right: 8px;
}

.delete-btn {
  background-color: #ffebee;
  color: #e53935;
}

.edit-btn:hover {
  background-color: var(--primary-color);
  color: white;
}

.delete-btn:hover {
  background-color: #e53935;
  color: white;
}

.friend-detail-sidebar {
  position: fixed;
  top: 0;
  right: -400px;
  width: 380px;
  height: 100vh;
  background-color: white;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: right 0.3s ease;
  padding: 20px;
  overflow-y: auto;
}

.friend-detail-sidebar.active {
  right: 0;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.sidebar-header h2 {
  margin: 0;
  color: var(--primary-dark);
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary);
}

.detail-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto 30px;
  border: 4px solid var(--accent-light);
}

.detail-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.detail-info {
  margin-bottom: 30px;
}

.info-item {
  margin-bottom: 15px;
  display: flex;
}

.info-item .label {
  font-weight: 600;
  color: var(--text-secondary);
  width: 80px;
}

.info-item .value {
  color: var(--text-primary);
  flex: 1;
}

.detail-actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.detail-btn {
  padding: 12px;
  border: none;
  border-radius: 8px;
  background-color: var(--primary-color);
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.detail-btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-content {
  background-color: white;
  border-radius: 12px;
  width: 500px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.delete-modal {
  width: 400px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h2 {
  margin: 0;
  color: var(--primary-dark);
}

.modal-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--text-primary);
}

.form-group input[type="text"],
.form-group input[type="date"] {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
}

.radio-group {
  display: flex;
  gap: 20px;
}

.radio-group label {
  display: flex;
  align-items: center;
  font-weight: normal;
}

.radio-group input {
  margin-right: 8px;
}

.avatar-preview {
  margin-top: 10px;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--accent-light);
}

.avatar-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 30px;
}

.cancel-btn {
  padding: 10px 20px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background-color: white;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  background-color: var(--accent-color);
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.delete-confirm-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  background-color: #e53935;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  background-color: #f5f5f5;
}

.submit-btn:hover {
  background-color: var(--accent-dark);
}

.delete-confirm-btn:hover {
  background-color: #c62828;
}

@media (max-width: 768px) {
  .friends-container {
    grid-template-columns: 1fr;
  }

  .friend-detail-sidebar {
    width: 100%;
    right: -100%;
  }
}
</style>
