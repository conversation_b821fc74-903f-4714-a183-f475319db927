<template>
  <div class="page-container">
    <div class="back-button" @click="goBack">
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M19 12H5M12 19l-7-7 7-7"/>
      </svg>
      <span>返回</span>
    </div>
    <div class="content-container">
      <div class="text-section">
        <div class="header">
          <img :src="eventData.avatar || defaultAvatar" alt="用户头像" class="avatar"/>
          <span class="user-name">{{ eventData.username || '匿名用户' }}</span>
        </div>
        <h2 class="article-title">{{ eventData.title }}</h2>
        <div class="article-content">
          <p class="description">{{ eventData.description }}</p>

          <!-- 图片展示 -->
          <div v-if="eventData.images && eventData.images.length" class="media-section">
            <h3 class="media-title">📷 相关图片</h3>
            <div class="images-grid">
              <div v-for="(img, idx) in eventData.images" :key="idx" class="image-item">
                <img :src="img" :alt="`图片 ${idx + 1}`" />
              </div>
            </div>
          </div>

          <!-- 视频展示 -->
          <div v-if="eventData.videos && eventData.videos.length" class="media-section">
            <h3 class="media-title">🎬 相关视频</h3>
            <div class="videos-container">
              <video v-for="(vid, idx) in eventData.videos" :key="idx" :src="vid" controls class="video-item">
                您的浏览器不支持视频播放。
              </video>
            </div>
          </div>

          <div class="meta">
            <div class="meta-item">
              <span class="meta-icon">🕒</span>
              <span>{{ formatDateTime(eventData.datetime) }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-icon">📍</span>
              <span>{{ eventData.province }} {{ eventData.city }}</span>
            </div>
          </div>
        </div>

        <!-- 点赞和收藏按钮 -->
        <div class="action-buttons">
          <button @click="like" class="like-button" :class="{ 'liked': isLiked }">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
            </svg>
            <span>{{ isLiked ? '已点赞' : '点赞' }} {{ likes }}</span>
          </button>
          <button @click="collect" class="collect-button" :class="{ 'collected': isCollected }">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"/>
            </svg>
            <span>{{ isCollected ? '已收藏' : '收藏' }}</span>
          </button>
        </div>

        <!-- 评论区 -->
        <div class="comments-section">
          <h3 class="comments-title">评论 ({{ comments.length }})</h3>
          <ul>
            <li v-for="(comment, index) in comments" :key="index" class="comment-item">
              <div class="comment-content">
                <div class="comment-header">
                  <img :src="comment.avatar || defaultAvatar" alt="用户头像" class="comment-avatar" />
                  <span class="comment-user-name">{{ comment.username || '匿名用户' }}</span>
                </div>
                <p class="comment-text">{{ comment.content }}</p>
              </div>
            </li>
          </ul>
        </div>

        <!-- 评论输入 -->
        <div class="comment-input-container">
          <input
            type="text"
            v-model="newComment"
            placeholder="分享你的感受和回忆..."
            @keyup.enter="addComment"
            class="comment-input"
          />
          <button @click="addComment" class="comment-submit-button">发表评论</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import defaultAvatar from '@/assets/avatar.png'
import service from '@/utils/request.js'

const route = useRoute()
const router = useRouter()
const eventData = ref({})

// 交互功能状态
const likes = ref(0)
const isLiked = ref(false)
const isCollected = ref(false)
const comments = ref([])
const newComment = ref('')

// 示例数据
const mockEventData = {
  1: {
    id: 1,
    title: '出发，前往黄山！',
    description: `今天和朋友一起坐火车前往黄山，一路上风景美极了。

早上6点就起床准备行李，带上了充足的水和干粮，还有相机记录这次美好的旅程。火车缓缓驶出站台，窗外的城市景色逐渐被田野和山峦所取代。

一路上我们有说有笑，分享着对黄山的期待和想象。列车员热情地为我们介绍沿途的风景名胜，让这次旅程更加有趣。透过车窗看到远山如黛，近水如镜，心情格外舒畅。

经过几个小时的车程，终于到达了黄山脚下。下车的那一刻，清新的山间空气扑面而来，让人精神为之一振。这次火车之旅不仅是到达目的地的过程，更是一次美好的体验。

期待明天的登山之旅，相信会有更多精彩的回忆等着我们。`,
    username: '天天开心',
    avatar: new URL('@/assets/example/user.png', import.meta.url).href,
    datetime: '2022-09-01 18:30:00',
    province: '安徽省',
    city: '黄山市',
    images: [
      new URL('@/assets/example/huoche0.png', import.meta.url).href
    ],
    videos: [],
    likes: 45,
    isLiked: false,
    isCollected: false,
    comments: [
      {
        username: '旅行爱好者小张',
        avatar: new URL('@/assets/example/user.png', import.meta.url).href,
        content: '火车旅行真的很有意思，沿途的风景一定很美！'
      },
      {
        username: '摄影师小李',
        avatar: new URL('@/assets/example/user.png', import.meta.url).href,
        content: '期待看到更多黄山的美景照片！'
      }
    ]
  },
  2: {
    id: 2,
    title: '勇攀高峰',
    description: `爬黄山对我们老年人来说还是不容易，所幸坚持下来了！

昨天坐火车到达黄山脚下，今天一早就开始了登山之旅。虽然年纪大了，但是内心的激情依然澎湃。和老伴一起，我们决定挑战自己，征服这座名山。

刚开始的时候还觉得挺轻松，山路两旁绿树成荫，空气格外清新。但是爬到半山腰的时候开始感到吃力，腿脚有些不听使唤，需要经常停下来休息。

途中遇到了很多年轻的登山者，他们热心地为我们加油鼓劲，还主动帮我们背包。这让我们深深感动，也更加坚定了登顶的决心。

经过几个小时的努力，我们终于登上了山顶。站在峰顶俯瞰群山，那种成就感无法言喻。虽然身体疲惫，但心情格外舒畅。这次登山让我们证明了，年龄不是阻碍，只要有决心，什么都能做到！`,
    username: '天天开心',
    avatar: new URL('@/assets/example/user.png', import.meta.url).href,
    datetime: '2022-09-02 16:30:00',
    province: '安徽省',
    city: '黄山市',
    images: [
      new URL('@/assets/example/huangshan0.png', import.meta.url).href,
      new URL('@/assets/example/huangshan1.png', import.meta.url).href
    ],
    videos: [],
    likes: 89,
    isLiked: false,
    isCollected: false,
    comments: [
      {
        username: '登山达人',
        avatar: new URL('@/assets/example/user.png', import.meta.url).href,
        content: '黄山真的很美！老年人能坚持爬完真的很了不起！'
      },
      {
        username: '户外爱好者',
        avatar: new URL('@/assets/example/user.png', import.meta.url).href,
        content: '年龄不是问题，有决心就能做到！为您点赞！'
      }
    ]
  },
  3: {
    id: 3,
    title: '参加孙儿的毕业典礼',
    description: `第一次去孙儿小明的大学，没想到就是毕业典礼。

今天是个特别的日子，我们全家人都来到了北京，参加孙儿小明的大学毕业典礼。这是我第一次踏进大学校园，心情既激动又自豪。

校园里到处都是穿着学士服的年轻人，他们脸上洋溢着青春的笑容和对未来的憧憬。看到小明穿着学士服站在我们面前，我不禁想起了他小时候的模样，时间过得真快啊。

毕业典礼很隆重，校长的致辞让人深受感动。当小明走上台接受学位证书的那一刻，我们全家人都激动得热泪盈眶。这四年来他的努力和坚持，终于得到了回报。

典礼结束后，我们在校园里拍了很多照片，记录下这个重要的时刻。小明带我们参观了他的宿舍和经常学习的图书馆，分享了他大学四年的点点滴滴。

作为爷爷奶奶，能够见证孙儿人生中这个重要的里程碑，我们感到无比的幸福和骄傲。希望他在未来的道路上能够继续努力，实现自己的梦想。`,
    username: '天天开心',
    avatar: new URL('@/assets/example/user.png', import.meta.url).href,
    datetime: '2023-06-05 20:00:00',
    province: '北京市',
    city: '海淀区',
    images: [
      new URL('@/assets/example/graduate0.png', import.meta.url).href
    ],
    videos: [],
    likes: 126,
    isLiked: false,
    isCollected: false,
    comments: [
      {
        username: '教育工作者',
        avatar: new URL('@/assets/example/user.png', import.meta.url).href,
        content: '家人的支持是最大的动力！祝贺小明毕业！'
      },
      {
        username: '同龄人',
        avatar: new URL('@/assets/example/user.png', import.meta.url).href,
        content: '能见证孙儿的重要时刻，真是太幸福了！'
      }
    ]
  },
}

onMounted(async () => {
  const id = route.params.id
  if (!id) {
    eventData.value = {
      title: '未找到事件',
      description: '抱歉，无法找到您要查看的事件详情。',
      username: '系统',
      avatar: '',
      datetime: '',
      province: '',
      city: '',
      images: [],
      videos: []
    }
    return
  }

  // 正确的异步请求写法
  const formData = new FormData()
  formData.append('issue_id', id)
  let d = null
  let d2 = null
  try {
    const res = await service({
      url: '/issue/issue/0/',
      method: 'post',
      data: formData
    })
    d = res.data
  } catch (error) {
    console.error('获取事件详情失败:', error)
    eventData.value = {
      title: '加载失败',
      description: '抱歉，无法加载事件详情，请稍后再试。',
      username: '系统',
      avatar: '',
      datetime: '',
      province: '',
      city: '',
      images: [],
      videos: []
    }
    return
  }
  try {
    const res2 = await service({
      url: '/video/create/',
      method: 'get',
    })
    d2 = res2.data
  } catch (error) {
    console.error('获取视频数据失败:', error)
    d2 = {}
  }
  eventData.value = {
    title: d.title,
    datetime: d.date,
    description: d.description,
    avatar: '',
    province: d.location ? d.location.split(' ')[0] : '',
    city: d.location ? d.location.split(' ')[1] || '' : '',
    images: Array.isArray(d.pictures)
      ? d.pictures.map(pic => `http://127.0.0.1:8000${pic.url}`)
      : [],
    username: d.author,
    video: d2 && d2.video_urlss ? d2.video_urlss : []
  }
  console.log('Event Data:', eventData.value)
})

// 点赞功能
const like = () => {
  if (isLiked.value) {
    likes.value--
    isLiked.value = false
  } else {
    likes.value++
    isLiked.value = true
  }

  // 这里可以添加API调用来保存点赞状态
  console.log('点赞状态:', isLiked.value, '点赞数:', likes.value)
}

// 收藏功能
const collect = () => {
  isCollected.value = !isCollected.value

  // 这里可以添加API调用来保存收藏状态
  console.log('收藏状态:', isCollected.value)
}

// 添加评论
const addComment = () => {
  if (newComment.value.trim()) {
    const comment = {
      username: '当前用户', // 这里应该从用户状态获取
      avatar: defaultAvatar,
      content: newComment.value.trim()
    }

    comments.value.push(comment)
    newComment.value = ''

    // 这里可以添加API调用来保存评论
    console.log('新增评论:', comment)
  }
}

const goBack = () => {
  router.back()
}

// 格式化日期时间
const formatDateTime = (dateTimeString) => {
  if (!dateTimeString) return ''

  const date = new Date(dateTimeString)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')

  return `${year}年${month}月${day}日 ${hours}:${minutes}`
}
</script>

<style scoped>
.page-container {
  padding: 20px;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4ff 0%, #e6f7ff 50%, #f0fdfa 100%);
  background-image:
    radial-gradient(circle at 10% 20%, rgba(120, 119, 198, 0.05) 0%, transparent 20%),
    radial-gradient(circle at 90% 80%, rgba(242, 202, 241, 0.08) 0%, transparent 20%);
}
.content-container {
  display: flex;
  flex-direction: row;
  gap: 20px;
  align-items: flex-start;
  justify-content: space-between;
}
.text-section {
  flex: 1;
  padding: 40px;
  background-color: #fff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  min-height: 60vh;
  height: auto;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}
.header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  user-select: none;
}
.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}
.user-name {
  font-weight: bold;
}
.article-title {
  font-size: 1.5em;
  margin: 15px 0;
}
.article-content {
  font-size: 1em;
  margin: 15px 0;
  line-height: 1.6;
  max-width: 100%;
}

.description {
  font-size: 1.1em;
  line-height: 1.8;
  color: #374151;
  margin-bottom: 30px;
  white-space: pre-line;
}

.media-section {
  margin: 30px 0;
}

.media-title {
  font-size: 1.2em;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.image-item {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.image-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.image-item img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  display: block;
}

.videos-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.video-item {
  width: 100%;
  max-width: 600px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.meta {
  margin-top: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1em;
  color: #4b5563;
}

.meta-icon {
  font-size: 1.1em;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 15px;
  margin: 30px 0;
  padding: 20px 0;
  border-top: 1px solid #e5e5e5;
}

.like-button, .collect-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: 2px solid #e0e0e0;
  border-radius: 25px;
  background: white;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.like-button:hover, .collect-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.like-button.liked {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  border-color: #ff6b6b;
  color: white;
}

.collect-button.collected {
  background: linear-gradient(135deg, #ffd93d 0%, #ff9500 100%);
  border-color: #ffd93d;
  color: white;
}

.like-button svg, .collect-button svg {
  width: 18px;
  height: 18px;
}

/* 评论区样式 */
.comments-section {
  margin-top: 30px;
  border-top: 1px solid #e5e5e5;
  padding-top: 20px;
}

.comments-title {
  font-size: 1.3em;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.comments-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 400px;
  overflow-y: auto;
}

.comment-item {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #667eea;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.comment-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.comment-user-name {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.comment-text {
  color: #555;
  line-height: 1.6;
  margin: 0;
  font-size: 14px;
}

.comment-input-container {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
}

.comment-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 25px;
  outline: none;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.comment-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.comment-submit-button {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  font-size: 14px;
}

.comment-submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}
.back-button {
  position: fixed;
  top: 20px;
  left: 200px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 100;
}
.back-button:hover {
  background-color: #f0f0f0;
  transform: translateX(-2px);
}
</style>