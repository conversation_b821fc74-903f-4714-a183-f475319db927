#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试VideoGallery后端接口
"""

import os
import sys
import django
import json
from pathlib import Path

# 设置Django环境
project_dir = 'shiguangyinji_backend-algorithm'
sys.path.insert(0, project_dir)
os.chdir(project_dir)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shiguangyinji.settings')
django.setup()

from video.models import Video, VideoPicture
from issue.models import Picture
from usermanage.models import User
from django.utils import timezone

def test_video_api_data():
    """测试视频API数据结构"""
    print("测试视频API数据结构")
    print("=" * 50)
    
    try:
        # 获取一个测试用户
        user = User.objects.first()
        if not user:
            print("❌ 没有找到用户，请先创建用户")
            return False
        
        print(f"✅ 找到测试用户: {user.username} (ID: {user.id})")
        
        # 查找用户的视频
        videos = Video.objects.filter(owner_id=user.id).order_by('-upload_at')
        print(f"✅ 找到 {videos.count()} 个视频")
        
        if videos.exists():
            for video in videos[:3]:  # 只显示前3个
                print(f"\n视频 {video.video_id}:")
                print(f"  标题: {video.title}")
                print(f"  描述: {video.description}")
                print(f"  事件标题: {video.event_title}")
                print(f"  状态: {video.status}")
                print(f"  视频URL: {video.video_url}")
                print(f"  上传时间: {video.upload_at}")
                
                # 检查关联的图片
                video_pictures = VideoPicture.objects.filter(video=video).select_related('picture')
                print(f"  关联图片数量: {video_pictures.count()}")
                
                if video_pictures.exists():
                    # 随机选择一张作为封面
                    import random
                    random_picture = random.choice(list(video_pictures))
                    if random_picture.picture and random_picture.picture.url:
                        thumbnail_url = f"/api/media/{random_picture.picture.url}"
                        print(f"  随机封面: {thumbnail_url}")
                
                # 解析事件
                if video.event_title:
                    event_titles = video.event_title.split(' | ')
                    print(f"  包含事件: {event_titles}")
                
                print("-" * 30)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_api_response():
    """模拟API响应数据"""
    print("\n模拟API响应数据")
    print("=" * 50)
    
    try:
        user = User.objects.first()
        if not user:
            print("❌ 没有找到用户")
            return
        
        videos = Video.objects.filter(owner_id=user.id).order_by('-upload_at')
        
        video_list = []
        for video in videos:
            # 获取关联的图片作为封面候选
            video_pictures = VideoPicture.objects.filter(video=video).select_related('picture')
            
            # 随机选择一张图片作为封面
            thumbnail_url = None
            events = []
            
            if video_pictures.exists():
                import random
                random_picture = random.choice(list(video_pictures))
                if random_picture.picture and random_picture.picture.url:
                    thumbnail_url = f"/api/media/{random_picture.picture.url}"
            
            # 解析事件标题
            if video.event_title:
                event_titles = video.event_title.split(' | ')
                for i, title in enumerate(event_titles):
                    if title.strip():
                        events.append({
                            'id': f"{video.video_id}_{i}",
                            'title': title.strip(),
                            'date': video.upload_at.isoformat() if video.upload_at else None
                        })

            video_data = {
                'id': video.video_id,
                'video_id': video.video_id,
                'title': video.title or '未命名视频',
                'description': video.description or '暂无描述',
                'event_title': video.event_title,
                'video_url': f"/api/media/{video.video_url}" if video.video_url else None,
                'videoUrl': f"/api/media/{video.video_url}" if video.video_url else None,
                'thumbnail': thumbnail_url,
                'date': video.upload_at.isoformat() if video.upload_at else None,
                'upload_at': video.upload_at.isoformat() if video.upload_at else None,
                'status': video.status,
                'progress': video.progress,
                'events': events
            }
            video_list.append(video_data)
        
        # 模拟API响应
        api_response = {
            'videos': video_list
        }
        
        print("API响应示例:")
        print(json.dumps(api_response, indent=2, ensure_ascii=False))
        
        return api_response
        
    except Exception as e:
        print(f"❌ 模拟API响应失败: {e}")
        return None

def check_database_structure():
    """检查数据库结构"""
    print("\n检查数据库结构")
    print("=" * 50)
    
    try:
        # 检查Video表
        print("Video表字段:")
        for field in Video._meta.get_fields():
            field_name = field.name
            field_type = type(field).__name__
            print(f"  {field_name}: {field_type}")
        
        print("\nVideoPicture表字段:")
        for field in VideoPicture._meta.get_fields():
            field_name = field.name
            field_type = type(field).__name__
            print(f"  {field_name}: {field_type}")
        
        print("\nPicture表字段:")
        for field in Picture._meta.get_fields():
            field_name = field.name
            field_type = type(field).__name__
            print(f"  {field_name}: {field_type}")
        
        # 统计数据
        video_count = Video.objects.count()
        video_picture_count = VideoPicture.objects.count()
        picture_count = Picture.objects.count()
        
        print(f"\n数据统计:")
        print(f"  Video记录数: {video_count}")
        print(f"  VideoPicture关联数: {video_picture_count}")
        print(f"  Picture记录数: {picture_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查数据库结构失败: {e}")
        return False

def test_event_title_parsing():
    """测试事件标题解析"""
    print("\n测试事件标题解析")
    print("=" * 50)
    
    test_cases = [
        "黄山登顶 | 观看日出 | 下山归程",
        "上海外滩 | 城隍庙小吃",
        "单个事件",
        "",
        None
    ]
    
    for i, event_title in enumerate(test_cases):
        print(f"测试用例 {i+1}: '{event_title}'")
        
        events = []
        if event_title:
            event_titles = event_title.split(' | ')
            for j, title in enumerate(event_titles):
                if title.strip():
                    events.append({
                        'id': f"test_{j}",
                        'title': title.strip(),
                        'date': timezone.now().isoformat()
                    })
        
        print(f"  解析结果: {events}")
        print()

def create_test_data():
    """创建测试数据"""
    print("\n创建测试数据")
    print("=" * 50)
    
    try:
        user = User.objects.first()
        if not user:
            print("❌ 没有找到用户")
            return False
        
        # 检查是否已有测试视频
        existing_videos = Video.objects.filter(owner_id=user.id, title__contains='测试')
        if existing_videos.exists():
            print(f"✅ 已存在 {existing_videos.count()} 个测试视频")
            return True
        
        # 创建测试视频
        test_video = Video.objects.create(
            owner_id=user.id,
            title='测试视频 - VideoGallery',
            description='这是一个用于测试VideoGallery界面的测试视频',
            event_title='测试事件1 | 测试事件2 | 测试事件3',
            status='completed',
            progress=100,
            video_url='video/test_video.mp4'
        )
        
        print(f"✅ 创建测试视频: {test_video.video_id}")
        
        # 如果有图片，关联一些图片
        pictures = Picture.objects.all()[:3]
        for picture in pictures:
            VideoPicture.objects.create(
                video=test_video,
                picture=picture
            )
        
        print(f"✅ 关联了 {pictures.count()} 张图片")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        return False

if __name__ == "__main__":
    print("VideoGallery后端接口测试")
    print("=" * 60)
    
    # 检查数据库结构
    check_database_structure()
    
    # 测试事件标题解析
    test_event_title_parsing()
    
    # 创建测试数据（如果需要）
    create_test_data()
    
    # 测试API数据
    test_video_api_data()
    
    # 模拟API响应
    simulate_api_response()
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print("\n建议:")
    print("1. 启动Django服务器: python manage.py runserver")
    print("2. 在前端访问VideoGallery页面测试")
    print("3. 检查浏览器控制台的API调用日志")
    print("4. 验证视频封面和事件信息是否正确显示")
