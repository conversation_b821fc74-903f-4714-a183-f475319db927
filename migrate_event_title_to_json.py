#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将event_title字段从CharField迁移到JSONField
"""

import os
import sys
import django
import json

# 设置Django环境
project_dir = 'shiguangyinji_backend-algorithm'
sys.path.insert(0, project_dir)
os.chdir(project_dir)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shiguangyinji.settings')
django.setup()

from video.models import Video

def backup_current_data():
    """备份当前数据"""
    print("备份当前event_title数据...")
    
    videos = Video.objects.all()
    backup_data = []
    
    for video in videos:
        backup_data.append({
            'video_id': video.video_id,
            'event_title': video.event_title,
            'title': video.title
        })
    
    # 保存备份
    with open('event_title_backup.json', 'w', encoding='utf-8') as f:
        json.dump(backup_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 备份完成，共 {len(backup_data)} 条记录")
    return backup_data

def convert_string_to_json_format():
    """手动转换现有数据"""
    print("\n开始转换现有数据...")
    
    videos = Video.objects.all()
    converted_count = 0
    
    for video in videos:
        if video.event_title:
            try:
                # 检查是否已经是列表格式
                if isinstance(video.event_title, list):
                    print(f"视频 {video.video_id} 已经是JSON格式，跳过")
                    continue
                
                # 如果是字符串，进行转换
                if isinstance(video.event_title, str):
                    events = []
                    
                    # 尝试解析JSON字符串
                    if video.event_title.startswith('[') and video.event_title.endswith(']'):
                        try:
                            events = json.loads(video.event_title)
                        except json.JSONDecodeError:
                            # JSON解析失败，按字符串处理
                            pass
                    
                    # 如果不是JSON或解析失败，按分隔符处理
                    if not events:
                        if ' | ' in video.event_title:
                            event_titles = video.event_title.split(' | ')
                            for i, title in enumerate(event_titles):
                                if title.strip():
                                    events.append({
                                        'id': i + 1,
                                        'title': title.strip(),
                                        'date': video.upload_at.strftime('%Y-%m-%d') if video.upload_at else '2025-01-01'
                                    })
                        else:
                            # 单个事件
                            events.append({
                                'id': 1,
                                'title': video.event_title.strip(),
                                'date': video.upload_at.strftime('%Y-%m-%d') if video.upload_at else '2025-01-01'
                            })
                    
                    # 更新数据库
                    video.event_title = events
                    video.save()
                    
                    print(f"✅ 转换视频 {video.video_id}: {len(events)} 个事件")
                    converted_count += 1
                
            except Exception as e:
                print(f"❌ 转换视频 {video.video_id} 失败: {e}")
                # 设置默认值
                video.event_title = [{
                    'id': 1,
                    'title': '美好回忆',
                    'date': '2025-01-01'
                }]
                video.save()
    
    print(f"\n✅ 转换完成，共处理 {converted_count} 个视频")

def verify_conversion():
    """验证转换结果"""
    print("\n验证转换结果...")
    
    videos = Video.objects.all()
    success_count = 0
    error_count = 0
    
    for video in videos:
        try:
            if isinstance(video.event_title, list):
                # 验证每个事件的结构
                for event in video.event_title:
                    if not isinstance(event, dict):
                        raise ValueError(f"事件不是字典格式: {event}")
                    if 'id' not in event or 'title' not in event or 'date' not in event:
                        raise ValueError(f"事件缺少必要字段: {event}")
                
                print(f"✅ 视频 {video.video_id}: {len(video.event_title)} 个事件")
                success_count += 1
            else:
                print(f"❌ 视频 {video.video_id}: event_title不是列表格式")
                error_count += 1
                
        except Exception as e:
            print(f"❌ 视频 {video.video_id} 验证失败: {e}")
            error_count += 1
    
    print(f"\n验证结果: 成功 {success_count} 个，失败 {error_count} 个")
    return error_count == 0

def create_sample_data():
    """创建示例数据"""
    print("\n创建示例数据...")
    
    try:
        from usermanage.models import User
        user = User.objects.first()
        
        if not user:
            print("❌ 没有找到用户，无法创建示例数据")
            return False
        
        # 创建示例视频
        sample_videos = [
            {
                'title': '黄山之旅',
                'description': '记录攀登黄山的美好时光',
                'events': [
                    {'id': 1, 'title': '攀登黄山', 'date': '2025-05-27'},
                    {'id': 2, 'title': '观看日出', 'date': '2025-05-28'},
                    {'id': 3, 'title': '下山归程', 'date': '2025-05-28'}
                ]
            },
            {
                'title': '上海外滩夜景',
                'description': '感受上海的繁华夜景',
                'events': [
                    {'id': 1, 'title': '外滩漫步', 'date': '2025-06-01'},
                    {'id': 2, 'title': '黄浦江游船', 'date': '2025-06-01'},
                    {'id': 3, 'title': '南京路购物', 'date': '2025-06-02'}
                ]
            }
        ]
        
        for sample in sample_videos:
            # 检查是否已存在
            if not Video.objects.filter(title=sample['title'], owner_id=user.id).exists():
                video = Video.objects.create(
                    owner_id=user.id,
                    title=sample['title'],
                    description=sample['description'],
                    event_title=sample['events'],
                    status='completed',
                    progress=100
                )
                print(f"✅ 创建示例视频: {video.title}")
            else:
                print(f"⚠️  示例视频已存在: {sample['title']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建示例数据失败: {e}")
        return False

def test_api_compatibility():
    """测试API兼容性"""
    print("\n测试API兼容性...")
    
    try:
        videos = Video.objects.all()[:3]
        
        for video in videos:
            print(f"\n视频: {video.title}")
            print(f"event_title类型: {type(video.event_title)}")
            print(f"event_title内容: {video.event_title}")
            
            # 模拟API处理逻辑
            events = []
            if video.event_title and isinstance(video.event_title, list):
                events = video.event_title
                for event in events:
                    if not event.get('id'):
                        event['id'] = len(events)
                    if not event.get('date'):
                        event['date'] = video.upload_at.strftime('%Y-%m-%d') if video.upload_at else None
            
            print(f"API返回的events: {events}")
            print("-" * 40)
        
        return True
        
    except Exception as e:
        print(f"❌ API兼容性测试失败: {e}")
        return False

if __name__ == "__main__":
    print("event_title字段迁移到JSONField")
    print("=" * 60)
    
    try:
        # 1. 备份数据
        backup_current_data()
        
        # 2. 转换现有数据
        convert_string_to_json_format()
        
        # 3. 验证转换结果
        if verify_conversion():
            print("✅ 数据转换验证通过")
        else:
            print("❌ 数据转换验证失败")
        
        # 4. 创建示例数据
        create_sample_data()
        
        # 5. 测试API兼容性
        test_api_compatibility()
        
        print("\n" + "=" * 60)
        print("迁移完成!")
        print("\n后续步骤:")
        print("1. 运行Django迁移: python manage.py migrate")
        print("2. 重启Django服务器")
        print("3. 测试前端VideoGallery页面")
        print("4. 验证事件数据显示正确")
        
    except Exception as e:
        print(f"❌ 迁移过程中出错: {e}")
        import traceback
        traceback.print_exc()
        
        print("\n如果出现问题，可以从备份恢复:")
        print("1. 检查 event_title_backup.json 文件")
        print("2. 手动恢复数据或联系开发者")
