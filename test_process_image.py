#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 process_image 函数的修改
"""

import sys
import os
sys.path.append('shiguangyinji_backend-algorithm')

from utils.detect_tools.detect import process_image

def test_process_image():
    """
    测试 process_image 函数是否正确返回 annotation 数据
    """
    # 模拟用户角色数据
    user_characters = [
        {
            "name": "张三",
            "relation": "朋友",
            "avatar": "path/to/avatar1.jpg",
            "known_encodings": []  # 这里应该是实际的人脸编码，为了测试暂时为空
        },
        {
            "name": "李四", 
            "relation": "同事",
            "avatar": "path/to/avatar2.jpg",
            "known_encodings": []
        }
    ]
    
    # 模拟图片路径（这里只是测试函数签名，不会真正处理图片）
    image_path = "test_image.jpg"
    
    try:
        # 调用 process_image 函数
        result = process_image(image_path, user_characters)
        
        # 检查返回值数量
        if len(result) == 4:
            description, detect_names, result_image, annotation = result
            print("✓ process_image 函数返回了正确数量的值 (4个)")
            print(f"✓ annotation 类型: {type(annotation)}")
            print(f"✓ annotation 内容: {annotation}")
            return True
        else:
            print(f"✗ process_image 函数返回了错误数量的值: {len(result)}")
            return False
            
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    print("开始测试 process_image 函数修改...")
    success = test_process_image()
    if success:
        print("✓ 测试通过！process_image 函数修改成功。")
    else:
        print("✗ 测试失败！需要检查 process_image 函数。")
