#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试串联回忆视频生成功能
"""

import requests
import json
import os

# 测试配置
BASE_URL = "http://localhost:8000"  # 您的Django服务器地址
API_ENDPOINT = f"{BASE_URL}/api/video/"

def test_video_generation():
    """测试视频生成功能"""
    print("开始测试串联回忆视频生成功能...")
    print("=" * 60)
    
    # 模拟登录获取token（您需要根据实际情况调整）
    # 这里假设您已经有了有效的认证token
    headers = {
        'Authorization': 'Bearer your_token_here',  # 替换为实际的token
        'Content-Type': 'application/json'
    }
    
    # 测试数据
    test_data = {
        'pictures': [1, 2, 3],  # 图片ID列表
        'title': '西湖游记',
        'description': '和家人一起游览西湖的美好回忆',
        # 'video_id': None  # 新建视频时不提供
    }
    
    try:
        # 1. 测试新建视频
        print("1. 测试新建视频...")
        response = requests.post(API_ENDPOINT, json=test_data, headers=headers)
        
        if response.status_code in [200, 201]:
            result = response.json()
            print(f"✓ 视频生成成功!")
            print(f"  视频ID: {result.get('video_id')}")
            print(f"  视频URL: {result.get('video_url')}")
            print(f"  标题: {result.get('title')}")
            print(f"  描述: {result.get('description')}")
            print(f"  事件标题: {result.get('event_title')}")
            
            video_id = result.get('video_id')
            
            # 2. 测试覆盖视频
            print(f"\n2. 测试覆盖视频 (ID: {video_id})...")
            test_data_update = {
                'pictures': [1, 2, 3, 4],  # 添加更多图片
                'title': '西湖游记 - 更新版',
                'description': '和家人一起游览西湖的美好回忆，增加了更多精彩瞬间',
                'video_id': video_id  # 提供video_id进行覆盖
            }
            
            response = requests.post(API_ENDPOINT, json=test_data_update, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✓ 视频覆盖成功!")
                print(f"  消息: {result.get('message')}")
                print(f"  更新后标题: {result.get('title')}")
            else:
                print(f"✗ 视频覆盖失败: {response.status_code}")
                print(f"  错误: {response.text}")
            
            # 3. 测试获取视频详情
            print(f"\n3. 测试获取视频详情...")
            response = requests.get(f"{API_ENDPOINT}?video_id={video_id}", headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✓ 获取视频详情成功!")
                print(f"  标题: {result.get('title')}")
                print(f"  描述: {result.get('description')}")
                print(f"  关联图片: {result.get('picture_ids')}")
            else:
                print(f"✗ 获取视频详情失败: {response.status_code}")
            
            # 4. 测试获取视频列表
            print(f"\n4. 测试获取视频列表...")
            response = requests.get(API_ENDPOINT, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                videos = result.get('videos', [])
                print(f"✓ 获取视频列表成功! 共 {len(videos)} 个视频")
                for i, video in enumerate(videos[:3]):  # 只显示前3个
                    print(f"  视频{i+1}: {video.get('title')} (ID: {video.get('video_id')})")
            else:
                print(f"✗ 获取视频列表失败: {response.status_code}")
            
            return video_id
            
        else:
            print(f"✗ 视频生成失败: {response.status_code}")
            print(f"  错误: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"✗ 网络请求失败: {e}")
        return None
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        return None

def test_video_deletion(video_id):
    """测试视频删除功能"""
    if not video_id:
        print("跳过删除测试（没有有效的视频ID）")
        return
    
    print(f"\n5. 测试删除视频 (ID: {video_id})...")
    
    headers = {
        'Authorization': 'Bearer your_token_here',  # 替换为实际的token
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.delete(f"{API_ENDPOINT}?video_id={video_id}", headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 视频删除成功!")
            print(f"  消息: {result.get('message')}")
        else:
            print(f"✗ 视频删除失败: {response.status_code}")
            print(f"  错误: {response.text}")
            
    except Exception as e:
        print(f"✗ 删除测试中出现错误: {e}")

def test_api_structure():
    """测试API接口结构"""
    print("\nAPI接口说明:")
    print("=" * 40)
    print("POST /api/video/")
    print("  功能: 生成新视频或覆盖现有视频")
    print("  参数:")
    print("    - pictures: 图片ID列表 (必需)")
    print("    - title: 视频标题 (可选)")
    print("    - description: 内容描述 (可选)")
    print("    - video_id: 视频ID (覆盖时提供)")
    print()
    print("GET /api/video/")
    print("  功能: 获取视频列表")
    print()
    print("GET /api/video/?video_id=<id>")
    print("  功能: 获取特定视频详情")
    print()
    print("DELETE /api/video/?video_id=<id>")
    print("  功能: 删除指定视频")

if __name__ == "__main__":
    print("串联回忆视频生成功能测试")
    print("=" * 60)
    
    # 显示API结构
    test_api_structure()
    
    # 执行功能测试
    video_id = test_video_generation()
    
    # 测试删除功能（可选）
    # test_video_deletion(video_id)
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print("\n主要功能:")
    print("✓ 视频生成 - 根据图片ID列表生成视频")
    print("✓ 视频覆盖 - 重新生成时覆盖原有视频文件")
    print("✓ 数据库扩展 - 新增title、description、event_title字段")
    print("✓ 文件管理 - 自动处理视频和脚本文件的保存与覆盖")
    print("✓ 完整的CRUD操作 - 创建、读取、更新、删除")
    
    print("\n使用说明:")
    print("1. 确保Django服务器正在运行")
    print("2. 替换测试脚本中的认证token")
    print("3. 确保数据库中有对应的图片记录")
    print("4. 运行测试脚本验证功能")
