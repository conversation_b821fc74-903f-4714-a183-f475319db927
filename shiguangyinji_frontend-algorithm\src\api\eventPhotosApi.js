import service from '@/utils/request'

// 从request.js中提取基础URL配置
const BASE_URL = 'http://127.0.0.1:8000'
const MEDIA_URL = `${BASE_URL}/media/`

/**
 * 获取事件详情和图片列表
 * @param {number} eventId 事件ID
 * @returns {Promise} 事件详情和图片列表
 */
export function getEventPhotos(eventId) {
  const formData = new FormData()
  formData.append('issue_id', eventId)

  return service({
    url: '/issue/issue/0/',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data: formData
  })
}

/**
 * 获取单个图片的详细信息
 * @param {number} pictureId 图片ID
 * @returns {Promise} 图片详细信息
 */
export function getPictureDetail(pictureId) {
  return service({
    url: '/issue/picture/',
    method: 'get',
    params: {
      picture_id: pictureId
    }
  })
}

/**
 * 根据box坐标截取图片
 * @param {string} imageUrl 原图片URL
 * @param {Array} box 坐标数组 [left, top, right, bottom]
 * @returns {Promise<string>} 截取后的图片base64数据
 */
export async function cropImageByBox(imageUrl, box) {
  console.log('开始截取图片，URL:', imageUrl, 'Box:', box)

  try {
    // 通过后端API代理获取图片数据，避免CORS问题
    // 将图片URL转换为相对路径
    let imagePath = imageUrl
    if (imageUrl.startsWith('http://127.0.0.1:8000/media/')) {
      imagePath = imageUrl.replace('http://127.0.0.1:8000/media/', '')
    } else if (imageUrl.startsWith('/media/')) {
      imagePath = imageUrl.replace('/media/', '')
    }

    // 通过后端API获取图片
    const response = await service({
      url: '/issue/image-proxy/',
      method: 'get',
      params: { path: imagePath },
      responseType: 'blob'
    })

    // service 返回的是 axios 响应对象
    const blob = response.data

    // 创建图片对象
    const img = new Image()
    const blobUrl = URL.createObjectURL(blob)

    return new Promise((resolve, reject) => {
      img.onload = function() {
        try {
          console.log('图片加载成功，开始截取，图片尺寸:', img.naturalWidth, 'x', img.naturalHeight)

          const [left, top, right, bottom] = box
          const width = right - left
          const height = bottom - top

          // 验证坐标有效性
          if (width <= 0 || height <= 0) {
            throw new Error(`无效的截取尺寸: width=${width}, height=${height}`)
          }

          // 确保坐标在图片范围内
          const safeLeft = Math.max(0, Math.min(left, img.naturalWidth))
          const safeTop = Math.max(0, Math.min(top, img.naturalHeight))
          const safeWidth = Math.min(width, img.naturalWidth - safeLeft)
          const safeHeight = Math.min(height, img.naturalHeight - safeTop)

          if (safeWidth <= 0 || safeHeight <= 0) {
            throw new Error(`截取区域超出图片范围: safeWidth=${safeWidth}, safeHeight=${safeHeight}`)
          }

          // 创建canvas并截取
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')

          canvas.width = safeWidth
          canvas.height = safeHeight

          // 绘制截取的部分
          ctx.drawImage(img, safeLeft, safeTop, safeWidth, safeHeight, 0, 0, safeWidth, safeHeight)

          // 转换为base64
          const croppedImageUrl = canvas.toDataURL('image/jpeg', 0.8)
          console.log('截取成功，生成base64长度:', croppedImageUrl.length)

          // 清理资源
          URL.revokeObjectURL(blobUrl)

          resolve(croppedImageUrl)
        } catch (error) {
          URL.revokeObjectURL(blobUrl)
          reject(error)
        }
      }

      img.onerror = function() {
        URL.revokeObjectURL(blobUrl)
        reject(new Error('图片加载失败'))
      }

      img.src = blobUrl
    })

  } catch (error) {
    console.error('截取过程中出错:', error)
    throw error
  }
}

/**
 * 处理face_data中的avatar显示
 * @param {Object} faceItem face_data中的单个人脸数据
 * @param {string} originalImageUrl 原图片URL
 * @returns {Promise<string>} 处理后的avatar URL
 */
export async function processFaceAvatar(faceItem, originalImageUrl) {
  console.log('处理人脸头像，原始avatar:', faceItem.avatar)

  // 如果已有avatar且不为空
  if (faceItem.avatar && faceItem.avatar.trim() !== '' &&
      !faceItem.avatar.includes('example/user.png')) {

    // 检查是否已经是完整的HTTP URL
    if (faceItem.avatar.startsWith('http://') || faceItem.avatar.startsWith('https://')) {
      console.log('已是完整URL，直接返回:', faceItem.avatar)
      return faceItem.avatar
    }

    // 如果是相对路径，转换为完整的HTTP URL
    if (faceItem.avatar.startsWith('character_db/') ||
        faceItem.avatar.startsWith('avatar/') ||
        !faceItem.avatar.startsWith('/')) {
      const fullUrl = `${MEDIA_URL}${faceItem.avatar}`
      console.log('相对路径转换为完整URL:', fullUrl)
      return fullUrl
    }

    // 如果是以 / 开头的绝对路径，添加域名和media前缀
    if (faceItem.avatar.startsWith('/')) {
      const fullUrl = `${MEDIA_URL.slice(0, -1)}${faceItem.avatar}`
      console.log('绝对路径转换为完整URL:', fullUrl)
      return fullUrl
    }

    console.log('其他情况，直接返回原avatar:', faceItem.avatar)
    return faceItem.avatar
  }

  // 如果有box坐标，尝试从原图中截取人脸区域
  if (faceItem.box && Array.isArray(faceItem.box) && faceItem.box.length === 4) {
    try {
      console.log('检测到box坐标，尝试从原图截取人脸区域:', faceItem.box)
      console.log('原图URL:', originalImageUrl)
      const croppedAvatar = await cropImageByBox(originalImageUrl, faceItem.box)
      console.log('截取成功，返回截取后的头像')
      return croppedAvatar
    } catch (error) {
      console.warn('截取人脸区域失败，使用原图:', error)
      // 截取失败时返回原图URL
      console.log('截取失败，返回原图URL:', originalImageUrl)
      return originalImageUrl
    }
  }

  // 默认返回默认头像
  console.log('没有有效的avatar和box坐标，返回默认头像')
  return new URL('@/assets/example/user.png', import.meta.url).href
}

/**
 * 获取图片尺寸
 * @param {string} imageUrl 图片URL
 * @returns {Promise<{width: number, height: number}>} 图片尺寸
 */
function getImageDimensions(imageUrl) {
  return new Promise((resolve, reject) => {
    console.log('开始获取图片尺寸，URL:', imageUrl)

    const img = new Image()

    // 先尝试不设置crossOrigin
    img.onload = function() {
      console.log('图片加载成功，尺寸:', img.naturalWidth, 'x', img.naturalHeight)
      if (img.naturalWidth === 0 || img.naturalHeight === 0) {
        console.warn('图片尺寸为0，可能是SVG或其他特殊格式')
        reject(new Error('图片尺寸为0'))
        return
      }
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      })
    }

    img.onerror = function(error) {
      console.error('图片加载失败:', error)
      console.log('尝试不使用crossOrigin重新加载...')

      // 如果失败，尝试不使用crossOrigin
      const img2 = new Image()
      img2.onload = function() {
        console.log('重新加载成功，尺寸:', img2.naturalWidth, 'x', img2.naturalHeight)
        resolve({
          width: img2.naturalWidth,
          height: img2.naturalHeight
        })
      }
      img2.onerror = function() {
        console.error('重新加载也失败了')
        reject(new Error('无法加载图片'))
      }
      img2.src = imageUrl
    }

    // 不设置crossOrigin，避免CORS问题
    img.src = imageUrl
  })
}

/**
 * 转换face_data为前端需要的annotations格式
 * @param {Array} faceData 后端返回的face_data
 * @param {string} originalImageUrl 原图片URL
 * @returns {Promise<Array>} 转换后的annotations数组
 */
export async function convertFaceDataToAnnotations(faceData, originalImageUrl) {
  if (!faceData || !Array.isArray(faceData)) {
    return []
  }

  const annotations = []

  // 获取原始图片尺寸用于坐标转换
  console.log('开始处理face_data，图片URL:', originalImageUrl)
  console.log('face_data内容:', faceData)

  let imageDimensions = null
  try {
    imageDimensions = await getImageDimensions(originalImageUrl)
    console.log('成功获取图片尺寸:', imageDimensions) // 调试信息
  } catch (error) {
    console.error('获取图片尺寸失败:', error)
    console.log('将使用估算尺寸进行坐标转换')
    // 不要直接返回空数组，继续处理但使用默认坐标
  }

  for (const faceItem of faceData) {
    try {
      console.log('处理单个人脸数据:', faceItem)
      const avatar = await processFaceAvatar(faceItem, originalImageUrl)
      console.log('处理后的avatar URL:', avatar)

      // 将像素坐标转换为百分比坐标
      let x = 50 // 默认值
      let y = 50 // 默认值

      if (typeof faceItem.x === 'number' && typeof faceItem.y === 'number') {
        if (imageDimensions) {
          // 有图片尺寸信息，进行精确转换
          x = (faceItem.x / imageDimensions.width) * 100
          y = (faceItem.y / imageDimensions.height) * 100
          console.log(`坐标转换: 像素(${faceItem.x}, ${faceItem.y}) -> 百分比(${x.toFixed(1)}, ${y.toFixed(1)})`) // 调试信息
        } else {
          // 没有图片尺寸信息，假设常见的图片尺寸进行粗略转换
          // 假设图片尺寸为1920x1080（常见尺寸）
          x = (faceItem.x / 1920) * 100
          y = (faceItem.y / 1080) * 100
          console.log(`坐标转换(估算): 像素(${faceItem.x}, ${faceItem.y}) -> 百分比(${x.toFixed(1)}, ${y.toFixed(1)})`) // 调试信息
        }
      }

      const annotation = {
        id: faceItem.id || annotations.length + 1,
        x: Math.max(0, Math.min(100, x)), // 确保坐标在0-100范围内
        y: Math.max(0, Math.min(100, y)),
        name: faceItem.name || '',
        relationship: faceItem.relationship || '',
        avatar: avatar,
        box: faceItem.box || null
      }

      annotations.push(annotation)
    } catch (error) {
      console.error('处理人脸数据失败:', error)
      // 即使处理失败也添加基本信息
      annotations.push({
        id: faceItem.id || annotations.length + 1,
        x: 50,
        y: 50,
        name: faceItem.name || '',
        relationship: faceItem.relationship || '',
        avatar: new URL('@/assets/example/user.png', import.meta.url).href,
        box: faceItem.box || null
      })
    }
  }

  return annotations
}
