<template>
  <div class="video-gallery-bg">
    <!-- 消息提示 -->
    <div v-if="showMessageFlag" :class="['message-toast', `message-${messageType}`]">
      {{ message }}
    </div>

    <div class="video-gallery-container">
      <!-- 页面标题 -->
      <header class="gallery-header">
        <div class="header-content">
          <h1 class="gallery-title">时光轨迹视频</h1>
          <p class="gallery-subtitle">珍藏您的精彩回忆</p>
        </div>
      </header>

      <!-- 搜索和筛选区域 -->
      <section class="search-section glass">
        <!-- 关键词搜索 -->
        <div class="search-row">
          <div class="search-box">
            <input
              type="text"
              v-model="searchKeyword"
              placeholder="输入关键词搜索视频..."
              @input="applyFilters"
              class="search-input"
            />
            <span class="search-icon">🔍</span>
          </div>
        </div>

        <!-- 时间范围选择 -->
        <div class="search-row">
          <div class="date-range">
            <div class="date-field">
              <label>开始日期</label>
              <input
                type="date"
                v-model="startDate"
                @change="applyFilters"
                class="date-input"
              />
            </div>
            <div class="date-separator">至</div>
            <div class="date-field">
              <label>结束日期</label>
              <input
                type="date"
                v-model="endDate"
                @change="applyFilters"
                class="date-input"
              />
            </div>
          </div>
        </div>
      </section>

      <!-- 加载状态 -->
      <section class="loading-section" v-if="loading">
        <div class="loading-container">
          <div class="loading-spinner"></div>
          <p class="loading-text">正在加载视频列表...</p>
        </div>
      </section>

      <!-- 视频轮播区域 -->
      <section class="video-carousel-section" v-if="!loading && filteredVideos.length > 0">
        <div class="carousel-container">
          <button class="carousel-nav prev-btn" @click="prevVideo" :disabled="currentIndex === 0">
            <span class="nav-icon">←</span>
          </button>

          <div class="carousel-track" ref="carouselTrack" @mousedown="startDrag" @touchstart="startDrag">
            <div
              v-for="(video, index) in filteredVideos"
              :key="video.id"
              class="carousel-slide"
              :class="{ 'active': index === currentIndex }"
              :style="{ transform: `translateX(${(index - currentIndex) * 100}%)` }"
            >
              <div class="video-card" @click="viewVideoDetail(video)">
                <div class="video-thumbnail">
                  <img :src="video.thumbnail" alt="视频缩略图" />
                  <div class="play-icon">▶</div>
                </div>
                <div class="video-info">
                  <h3 class="video-title">{{ video.title }}</h3>
                  <p class="video-date">{{ formatDate(video.date) }}</p>
                  <p class="video-description">{{ truncateText(video.description, 100) }}</p>

                  <!-- 显示视频包含的事件 -->
                  <!-- <div class="video-events" v-if="getVideoEvents(video).length > 0">
                    <h4 class="events-title">包含事件:</h4>
                    <div class="events-list">
                      <div
                        v-for="event in getVideoEvents(video)"
                        :key="event.id"
                        class="event-item"
                      >
                        <span class="event-title">{{ event.title }}</span>
                        <span class="event-date">{{ formatDate(event.date) }}</span>
                      </div>
                    </div>
                  </div> -->
                </div>
              </div>
            </div>
          </div>

          <button class="carousel-nav next-btn" @click="nextVideo" :disabled="currentIndex === filteredVideos.length - 1">
            <span class="nav-icon">→</span>
          </button>
        </div>

        <div class="carousel-indicators">
          <span
            v-for="(_, index) in filteredVideos"
            :key="index"
            class="indicator"
            :class="{ 'active': index === currentIndex }"
            @click="goToSlide(index)"
          ></span>
        </div>
      </section>

      <!-- 无视频提示 -->
      <section class="no-videos" v-if="!loading && filteredVideos.length === 0">
        <div class="no-videos-icon">🎬</div>
        <h3>暂无视频</h3>
        <p v-if="videos.length === 0">您还没有创建任何时光轨迹视频</p>
        <p v-else>没有符合筛选条件的视频</p>
        <button class="action-btn" @click="goToMakeVideo" v-if="videos.length === 0">制作视频</button>
        <button class="action-btn" @click="clearFilters" v-else>清除筛选</button>
      </section>

      <!-- 视频详情弹窗 -->
      <div class="video-detail-modal" v-if="showVideoDetail" @click.self="closeVideoDetail">
        <div class="modal-content">
          <button class="close-btn" @click="closeVideoDetail">×</button>

          <div class="video-player-container" :class="{ 'vertical-video': isVerticalVideo }">
            <video
              ref="videoPlayer"
              controls
              class="video-player"
              :src="selectedVideo.videoUrl"
              preload="metadata"
              playsinline
              @loadedmetadata="onVideoLoaded"
            ></video>
          </div>

          <div class="video-detail-info">
            <h2 class="detail-title">{{ selectedVideo.title }}</h2>
            <p class="detail-date">创建于 {{ formatDate(selectedVideo.date) }}</p>
            <p class="detail-description">{{ selectedVideo.description }}</p>

            <div class="detail-events" v-if="selectedVideo.events && selectedVideo.events.length > 0">
              <h3 class="events-title">包含的事件</h3>
              <ul class="events-list">
                <li v-for="event in selectedVideo.events" :key="event.id" class="event-item">
                  <span class="event-title">{{ event.title }}</span>
                  <span class="event-date">{{ formatDate(event.date) }}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { getVideoListSmart } from '@/api/videoApi';

const router = useRouter();

// 视频数据
const videos = ref([]);
const filteredVideos = ref([]);
const loading = ref(false);

// 轮播控制
const currentIndex = ref(0);

// 事件数据
const allEvents = ref([]);
const filteredEvents = ref([]);

// 消息提示
const message = ref('');
const messageType = ref('');
const showMessageFlag = ref(false);

// 筛选条件
const searchKeyword = ref('');
const startDate = ref('');
const endDate = ref('');

// 视频详情
const showVideoDetail = ref(false);
const selectedVideo = ref({});
const isVerticalVideo = ref(false);
const videoPlayer = ref(null);

// 拖拽相关
const isDragging = ref(false);
const startX = ref(0);
const carouselTrack = ref(null);

// 消息提示函数
function showMessage(text, type = 'info') {
  message.value = text;
  messageType.value = type;
  showMessageFlag.value = true;

  // 3秒后自动隐藏
  setTimeout(() => {
    showMessageFlag.value = false;
  }, 3000);

  // 同时在控制台输出
  console.log(`[${type.toUpperCase()}] ${text}`);
}

// 加载视频数据
async function loadVideos() {
  loading.value = true;
  try {
    console.log('正在加载视频列表...');
    const response = await getVideoListSmart();

    if (response.data && response.data.videos) {
      videos.value = response.data.videos;
      console.log(`✅ 成功加载 ${videos.value.length} 个视频`);

      // 处理数据格式，确保兼容性
      videos.value = videos.value.map(video => ({
        ...video,
        // 使用completed_at作为视频日期，如果没有则使用upload_at
        date: video.completed_at || video.upload_at,
        // 确保有默认值
        title: video.title || '未命名视频',
        description: video.description || '暂无描述',
        events: video.events || [],
        // 处理缩略图
        thumbnail: video.thumbnail || 'https://picsum.photos/800/450?random=' + video.id
      }));

      // 处理事件数据，将所有视频的事件展开为单独的条目
      processEventsData();

      showMessage(`成功加载 ${videos.value.length} 个视频`, 'success');
    } else {
      console.warn('响应数据格式异常:', response);
      videos.value = [];
      allEvents.value = [];
      showMessage('视频数据格式异常', 'warning');
    }
  } catch (error) {
    console.error('加载视频列表失败:', error);
    showMessage('加载视频列表失败: ' + error.message, 'error');
    videos.value = [];
    allEvents.value = [];
  } finally {
    loading.value = false;
    applyFilters();
  }
}

// 处理事件数据
function processEventsData() {
  const events = [];

  videos.value.forEach(video => {
    // 处理event_title字段（JSONField格式）
    let videoEvents = [];

    if (video.event_title && Array.isArray(video.event_title)) {
      // 直接使用JSONField中的数据
      videoEvents = video.event_title;
    } else if (video.events && Array.isArray(video.events)) {
      // 使用events字段作为备选
      videoEvents = video.events;
    }

    // 将每个事件转换为独立的条目
    videoEvents.forEach(event => {
      events.push({
        id: event.id,
        title: event.title,
        date: event.date,
        videoId: video.id || video.video_id,
        videoTitle: video.title,
        videoCompletedAt: video.completed_at || video.upload_at,
        videoThumbnail: video.thumbnail,
        videoUrl: video.videoUrl || video.video_url,
        originalVideo: video
      });
    });
  });

  // 按事件日期排序（最新的在前）
  allEvents.value = events.sort((a, b) => new Date(b.date) - new Date(a.date));

  console.log(`✅ 处理了 ${allEvents.value.length} 个事件`);
}

onMounted(() => {
  loadVideos();
});

// 应用筛选条件
function applyFilters() {
  let videoResult = [...videos.value];
  let eventResult = [...allEvents.value];

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();

    // 筛选视频
    videoResult = videoResult.filter(v =>
      (v.title && v.title.toLowerCase().includes(keyword)) ||
      (v.description && v.description.toLowerCase().includes(keyword))
    );

    // 筛选事件
    eventResult = eventResult.filter(e =>
      (e.title && e.title.toLowerCase().includes(keyword)) ||
      (e.videoTitle && e.videoTitle.toLowerCase().includes(keyword))
    );
  }

  // 日期范围筛选
  if (startDate.value) {
    const startDateObj = new Date(startDate.value);
    videoResult = videoResult.filter(v => new Date(v.date) >= startDateObj);
    eventResult = eventResult.filter(e => new Date(e.date) >= startDateObj);
  }
  if (endDate.value) {
    const endDateObj = new Date(endDate.value);
    endDateObj.setHours(23, 59, 59);
    videoResult = videoResult.filter(v => new Date(v.date) <= endDateObj);
    eventResult = eventResult.filter(e => new Date(e.date) <= endDateObj);
  }

  filteredVideos.value = videoResult;
  filteredEvents.value = eventResult;
  currentIndex.value = 0; // 重置轮播索引
}

// 轮播控制
function nextVideo() {
  if (currentIndex.value < filteredVideos.value.length - 1) {
    currentIndex.value++;
  }
}

function prevVideo() {
  if (currentIndex.value > 0) {
    currentIndex.value--;
  }
}

function goToSlide(index) {
  currentIndex.value = index;
}

// 拖拽功能
function startDrag(event) {
  isDragging.value = true;
  startX.value = event.type === 'mousedown' ? event.clientX : event.touches[0].clientX;

  const handleMove = (moveEvent) => {
    if (!isDragging.value) return;

    const currentX = moveEvent.type === 'mousemove' ? moveEvent.clientX : moveEvent.touches[0].clientX;
    const diff = currentX - startX.value;

    if (Math.abs(diff) > 50) { // 拖动距离超过50px才触发翻页
      if (diff > 0 && currentIndex.value > 0) {
        currentIndex.value--;
      } else if (diff < 0 && currentIndex.value < filteredVideos.value.length - 1) {
        currentIndex.value++;
      }

      isDragging.value = false;
      document.removeEventListener('mousemove', handleMove);
      document.removeEventListener('touchmove', handleMove);
    }
  };

  const handleEnd = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', handleMove);
    document.removeEventListener('touchmove', handleMove);
    document.removeEventListener('mouseup', handleEnd);
    document.removeEventListener('touchend', handleEnd);
  };

  document.addEventListener('mousemove', handleMove);
  document.addEventListener('touchmove', handleMove);
  document.addEventListener('mouseup', handleEnd);
  document.addEventListener('touchend', handleEnd);
}

// 获取视频包含的事件
function getVideoEvents(video) {
  if (video.event_title && Array.isArray(video.event_title)) {
    return video.event_title;
  } else if (video.events && Array.isArray(video.events)) {
    return video.events;
  }
  return [];
}

// 视频详情查看
function viewVideoDetail(video) {
  selectedVideo.value = video;
  showVideoDetail.value = true;
  isVerticalVideo.value = false; // 重置状态
  document.body.style.overflow = 'hidden'; // 防止背景滚动
}

function closeVideoDetail() {
  showVideoDetail.value = false;
  document.body.style.overflow = ''; // 恢复背景滚动
}

// 视频加载完成时检测方向
function onVideoLoaded() {
  if (videoPlayer.value) {
    const video = videoPlayer.value;
    const width = video.videoWidth;
    const height = video.videoHeight;

    // 判断是否为竖屏视频
    isVerticalVideo.value = height > width;

    console.log(`视频尺寸: ${width}x${height}, 竖屏: ${isVerticalVideo.value}`);
  }
}

// 跳转到制作视频页面
function goToMakeVideo() {
  router.push({ name: 'make-video' });
}

// 清除筛选条件
function clearFilters() {
  searchKeyword.value = '';
  startDate.value = '';
  endDate.value = '';
  applyFilters();
}

// 刷新视频列表
function refreshVideos() {
  loadVideos();
}

// 辅助函数
function formatDate(dateString) {
  if (!dateString) return '';

  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();

  return `${year}年${month}月${day}日`;
}

function truncateText(text, maxLength) {
  if (!text) return '';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}
</script>

<style scoped>
/* 导入字体 */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap');

/* 消息提示 */
.message-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  z-index: 9999;
  animation: slideInRight 0.3s ease-out;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.message-success {
  background: linear-gradient(135deg, #10b981, #059669);
}

.message-error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.message-warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.message-info {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 基础样式 */
.video-gallery-bg {
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #f0f4ff 0%, #e6f7ff 50%, #f0fdfa 100%);
  background-image:
    radial-gradient(circle at 10% 20%, rgba(120, 119, 198, 0.05) 0%, transparent 20%),
    radial-gradient(circle at 90% 80%, rgba(242, 202, 241, 0.08) 0%, transparent 20%),
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.9) 0%, transparent 100%);
  padding: 0;
  margin: 0;
  overflow-x: hidden;
  font-family: 'Noto Sans SC', sans-serif;
  position: relative;
}

.video-gallery-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px 60px;
  min-height: 100vh;
  width: 100%;
}

/* 页面头部 */
.gallery-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
  position: relative;
}

.header-content {
  margin-bottom: 30px;
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.gallery-title {
  font-size: 3rem;
  font-weight: 800;
  color: #1e3a8a;
  margin: 0 0 10px;
  letter-spacing: 2px;
  text-shadow: 0 4px 24px rgba(30, 58, 138, 0.08);
  background: linear-gradient(90deg, #1e3a8a, #3b82f6);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.gallery-subtitle {
  font-size: 1.25rem;
  color: #4b5563;
  margin: 0;
  font-weight: 400;
  letter-spacing: 1px;
}

/* 搜索区域 */
.search-section {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 40px;
  box-shadow: 0 8px 30px rgba(30, 58, 138, 0.08);
  animation: fadeIn 0.6s ease-out 0.2s both;
}

.search-row {
  margin-bottom: 20px;
}

.search-row:last-child {
  margin-bottom: 0;
}

.search-box {
  position: relative;
  width: 100%;
}

.search-input {
  width: 100%;
  padding: 14px 20px 14px 50px;
  font-size: 1rem;
  border: 2px solid #dbeafe;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s;
}

.search-input:focus {
  outline: none;
  border-color: #60a5fa;
  box-shadow: 0 0 0 4px rgba(96, 165, 250, 0.15);
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  color: #60a5fa;
}

.date-range {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.date-field {
  display: flex;
  flex-direction: column;
  gap: 5px;
  flex: 1;
}

.date-field label {
  font-size: 0.85rem;
  color: #4b5563;
  font-weight: 500;
}

.date-input {
  padding: 12px 16px;
  border: 2px solid #dbeafe;
  border-radius: 8px;
  font-size: 0.95rem;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s;
}

.date-input:focus {
  outline: none;
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.15);
}

.date-separator {
  margin: 0 5px;
  color: #6b7280;
  align-self: flex-end;
  margin-bottom: 10px;
  font-weight: 500;
}

/* 视频轮播区域 */
.video-carousel-section {
  margin-bottom: 60px;
}

.carousel-container {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.carousel-track {
  flex: 1;
  overflow: hidden;
  position: relative;
  height: 450px;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: transform 0.5s ease;
}

.carousel-nav {
  background: rgba(255, 255, 255, 0.8);
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  margin: 0 20px;
}

.carousel-nav:hover:not(:disabled) {
  background: white;
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.carousel-nav:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nav-icon {
  font-size: 1.5rem;
  color: #3b82f6;
}

.carousel-indicators {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #d1d5db;
  cursor: pointer;
  transition: all 0.3s;
}

.indicator.active {
  background: #3b82f6;
  transform: scale(1.2);
}

/* 视频卡片 */
.video-card {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s;
}

.video-card:hover {
  transform: scale(1.02);
}

.video-thumbnail {
  position: relative;
  height: 60%;
  overflow: hidden;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s;
}

.video-card:hover .video-thumbnail img {
  transform: scale(1.05);
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background: rgba(59, 130, 246, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  opacity: 0;
  transition: all 0.3s;
}

.video-card:hover .play-icon {
  opacity: 1;
}

.video-info {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.video-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 10px;
}

.video-date {
  font-size: 0.9rem;
  color: #6b7280;
  margin: 0 0 15px;
}

.video-description {
  font-size: 1rem;
  color: #4b5563;
  line-height: 1.5;
  margin: 0 0 15px;
}

/* 视频事件显示 */
.video-events {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e5e7eb;
}

.video-events .events-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1e40af;
  margin: 0 0 10px;
}

.video-events .events-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.video-events .event-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
  transition: all 0.2s;
}

.video-events .event-item:hover {
  background: #f1f5f9;
  transform: translateX(3px);
}

.video-events .event-title {
  font-size: 0.85rem;
  font-weight: 500;
  color: #1e293b;
  flex: 1;
}

.video-events .event-date {
  font-size: 0.8rem;
  color: #6b7280;
  white-space: nowrap;
}

/* 加载状态 */
.loading-section {
  text-align: center;
  padding: 80px 20px;
  animation: fadeIn 0.6s ease-out;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 1.1rem;
  color: #6b7280;
  margin: 0;
}

/* 事件列表区域 */
.events-list-section {
  margin-bottom: 60px;
}

.events-container {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 30px rgba(30, 58, 138, 0.08);
}

.events-section-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e3a8a;
  margin: 0 0 30px;
  text-align: center;
  letter-spacing: 1px;
}

.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.event-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  height: 120px;
}

.event-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(30, 58, 138, 0.15);
}

.event-content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.event-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  flex: 1;
  margin-right: 10px;
  line-height: 1.3;
}

.event-date {
  font-size: 0.85rem;
  color: #3b82f6;
  font-weight: 500;
  white-space: nowrap;
  background: #eff6ff;
  padding: 2px 8px;
  border-radius: 6px;
}

.event-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.video-title-ref,
.video-date-ref {
  font-size: 0.8rem;
  color: #6b7280;
}

.video-title-ref {
  font-weight: 500;
}

.event-thumbnail {
  width: 120px;
  position: relative;
  overflow: hidden;
}

.event-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.event-card:hover .event-thumbnail img {
  transform: scale(1.05);
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.event-card:hover .play-overlay {
  opacity: 1;
}

.play-overlay .play-icon {
  color: white;
  font-size: 1.5rem;
}

/* 无视频提示 */
.no-videos {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  margin: 40px 0;
}

.no-videos-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  color: #9ca3af;
}

.no-videos h3 {
  font-size: 1.5rem;
  color: #1e293b;
  margin: 0 0 10px;
}

.no-videos p {
  font-size: 1.1rem;
  color: #6b7280;
  margin: 0 0 30px;
}

.action-btn {
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.action-btn:hover {
  background: #2563eb;
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(37, 99, 235, 0.2);
}

/* 视频详情弹窗 */
.video-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
}

.close-btn {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(255, 255, 255, 0.8);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.close-btn:hover {
  background: white;
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.video-player-container {
  width: 100%;
  position: relative;
  background: #000;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  max-height: 70vh;
}

.video-player {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain; /* 保持视频比例，不裁剪 */
  background: #000;
  border-radius: 12px;
}

.video-detail-info {
  padding: 30px;
}

.detail-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 10px;
}

.detail-date {
  font-size: 1rem;
  color: #6b7280;
  margin: 0 0 20px;
}

.detail-description {
  font-size: 1.1rem;
  color: #4b5563;
  line-height: 1.6;
  margin: 0 0 30px;
}

.events-title {
  font-size: 1.3rem;
  color: #1e40af;
  margin: 0 0 15px;
  font-weight: 600;
}

.events-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.event-item {
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  transition: all 0.2s;
}

.event-item:hover {
  background: #f1f5f9;
  transform: translateX(5px);
}

.event-title {
  font-weight: 500;
  color: #1e293b;
}

.event-date {
  color: #6b7280;
  font-size: 0.9rem;
}

/* 竖屏视频特殊样式 */
.video-player-container.vertical-video {
  max-width: 400px;
  margin: 0 auto;
  aspect-ratio: 9/16;
  max-height: 80vh;
}

.video-player-container.vertical-video .video-player {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .events-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .event-card {
    height: auto;
    flex-direction: column;
  }

  .event-thumbnail {
    width: 100%;
    height: 150px;
  }

  .event-content {
    padding: 12px;
  }

  .event-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .event-title {
    margin-right: 0;
    font-size: 1rem;
  }

  .event-date {
    align-self: flex-start;
  }

  .events-section-title {
    font-size: 1.5rem;
  }

  .detail-title {
    font-size: 1.5rem;
  }

  .detail-description {
    font-size: 1rem;
  }

  .event-item {
    flex-direction: column;
    gap: 5px;
  }

  /* 移动端竖屏视频调整 */
  .video-player-container.vertical-video {
    max-width: 100%;
    max-height: 60vh;
  }

  .video-player-container {
    max-height: 50vh;
  }
}

@media (max-width: 480px) {
  .events-container {
    padding: 20px;
  }

  .event-card {
    height: auto;
  }

  .event-content {
    padding: 10px;
  }

  .event-title {
    font-size: 0.95rem;
  }

  .event-date {
    font-size: 0.8rem;
  }

  .video-title-ref,
  .video-date-ref {
    font-size: 0.75rem;
  }
}

@media (min-width: 769px) {
  /* 桌面端横屏视频优化 */
  .video-player-container:not(.vertical-video) {
    aspect-ratio: 16/9;
    max-height: 60vh;
  }
}
</style>
