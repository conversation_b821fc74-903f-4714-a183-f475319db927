# Generated by Django 4.2.20 on 2025-04-10 09:48

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import issue.models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Issue",
            fields=[
                (
                    "issue_id",
                    models.AutoField(db_column="id", primary_key=True, serialize=False),
                ),
                ("location", models.CharField(max_length=255)),
                ("date", models.DateField(blank=True, null=True)),
                ("description", models.TextField()),
                (
                    "author",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "issue",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="Picture",
            fields=[
                (
                    "picture_id",
                    models.AutoField(db_column="id", primary_key=True, serialize=False),
                ),
                (
                    "url",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to=issue.models.get_upload_path_for_original_image,
                    ),
                ),
                ("upload_at", models.DateTimeField(auto_now_add=True)),
                ("description", models.TextField(blank=True, null=True)),
                ("location", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "detected_image",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to=issue.models.get_upload_path_for_detected_image,
                    ),
                ),
                (
                    "relationship_image",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to=issue.models.get_upload_path_for_relationship_image,
                    ),
                ),
                (
                    "issue",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="issue.issue",
                    ),
                ),
            ],
            options={
                "db_table": "picture",
                "managed": True,
            },
        ),
    ]
