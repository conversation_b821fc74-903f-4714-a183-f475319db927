<template>
  <div class="make-video-bg">
    <div class="make-video-container">
      <!-- 页面标题 -->
      <div class="make-video-header">
        <h1>🎬 制作我的时光视频</h1>
      </div>

      <!-- 步骤指示器 -->
      <div class="steps-indicator">
        <div
          v-for="(step, index) in steps"
          :key="index"
          class="step"
          :class="{ 'active': currentStep === index, 'completed': currentStep > index }"
        >
          <div class="step-number">{{ index + 1 }}</div>
          <div class="step-title">{{ step }}</div>
          <div class="step-connector" v-if="index < steps.length - 1"></div>
        </div>
      </div>

      <!-- 第一步：选择事件 -->
      <div v-if="currentStep === 0" class="step-content">
        <div class="step-header">
          <h2>选择您想要包含在视频中的时光片段</h2>
          <div class="action-buttons">
            <button class="select-btn" @click="selectAll">全选</button>
            <button class="select-btn" @click="clearAll">全部移除</button>
          </div>
        </div>

        <!-- 筛选器 -->
        <div class="filters glass">
          <select v-model="selectedProvince" @change="filterEvents">
            <option value="">全部省份</option>
            <option v-for="province in provinces" :key="province">{{ province }}</option>
          </select>
          <select v-model="selectedCity" @change="filterEvents">
            <option value="">全部城市</option>
            <option v-for="city in cities" :key="city">{{ city }}</option>
          </select>
          <select v-model="sortOrder" @change="sortEvents">
            <option value="desc">时间：最新优先</option>
            <option value="asc">时间：最早优先</option>
          </select>
        </div>

        <!-- 轨迹时间范围选择 -->
        <div class="time-range-selector">
          <h3 class="time-range-title">轨迹时间范围</h3>
          <div class="time-range-inputs">
            <div class="time-field">
              <label>开始日期</label>
              <input
                type="date"
                v-model="trackStartDate"
                :max="trackEndDate || maxDate"
                @change="handleDateRangeChange"
                class="time-input"
              />
            </div>
            <div class="time-separator">至</div>
            <div class="time-field">
              <label>结束日期</label>
              <input
                type="date"
                v-model="trackEndDate"
                :min="trackStartDate || minDate"
                @change="handleDateRangeChange"
                class="time-input"
              />
            </div>
          </div>
          <div class="time-range-info" v-if="trackStartDate && trackEndDate">
            <p>当前选择的时间范围：{{ formatDate(trackStartDate) }} 至 {{ formatDate(trackEndDate) }}</p>
          </div>
        </div>

        <!-- 无事件提示 -->
        <div v-if="filteredEvents.length === 0" class="no-events">
          <div class="no-events-icon">📝</div>
          <h3>您还没有记录任何时光片段</h3>
          <p>先去记录一些美好的回忆，然后再来制作视频吧！</p>
          <button class="action-btn" @click="goToUploadEvent">记录时光片段</button>
        </div>

        <!-- 事件列表 -->
        <div v-else class="event-list">
          <div v-for="event in filteredEvents" :key="event.id" class="event-card" :class="{ 'selected': isSelected(event) }">
            <span class="checkbox" @click="toggleSelect(event)">
              <span v-if="isSelected(event)">√</span>
            </span>
            <div class="event-info">
              <h3>{{ event.title }}</h3>
              <p>{{ event.datetime }} ｜ {{ event.province }} {{ event.city }}</p>
            </div>
          </div>
        </div>

        <!-- 已选事件预览 -->
        <div class="preview-section" v-if="selectedEvents.length > 0">
          <h2>已选择 {{ selectedEvents.length }} 个事件</h2>
          <ul>
            <li v-for="event in selectedEvents" :key="event.id">
              <span>{{ event.title }}</span> ｜
              <span>{{ formatDate(event.datetime) }}</span> ｜
              <span>{{ event.province }} {{ event.city }}</span>
            </li>
          </ul>
        </div>

        <!-- 下一步按钮 -->
        <div class="step-actions">
          <button
            class="next-btn"
            :disabled="selectedEvents.length === 0"
            @click="goToNextStep"
          >
            下一步
            <span class="btn-icon">→</span>
          </button>
        </div>
      </div>

      <!-- 第二步：输入创作要求 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="step-header">
          <h2>设置视频创作要求</h2>
        </div>

        <!-- 创作要求表单 -->
        <div class="creation-form">
          <div class="form-group">
            <label>视频标题</label>
            <input
              type="text"
              v-model="videoTitle"
              placeholder="为您的视频起个标题..."
              class="form-input"
            />
          </div>

          <div class="form-group">
            <label>内容方向描述</label>
            <textarea
              v-model="videoDescription"
              placeholder="描述您希望视频呈现的风格、主题或情感..."
              class="form-textarea"
              rows="4"
            ></textarea>
          </div>

          <!-- 数字人开头选择 -->
          <div class="form-group">
            <label class="checkbox-label">
              <input
                type="checkbox"
                v-model="useDigitalHuman"
                @change="onDigitalHumanChange"
              />
              是否使用数字人开头
            </label>

            <!-- 数字人选择弹窗 -->
            <div v-if="useDigitalHuman" class="digital-human-options">
              <div class="option-group">
                <label class="radio-label">
                  <input
                    type="radio"
                    v-model="digitalHumanSource"
                    value="existing"
                    @change="loadUserCharacters"
                  />
                  使用已上传的真人头像
                </label>

                <!-- 已有头像选择 -->
                <div v-if="digitalHumanSource === 'existing'" class="avatar-selection">
                  <div v-if="userCharacters.length === 0" class="no-avatars">
                    暂无可用头像，请先在个人信息页面上传头像
                  </div>
                  <div v-else class="avatar-list">
                    <div
                      v-for="character in userCharacters"
                      :key="character.character_id"
                      class="avatar-item"
                      :class="{ 'selected': selectedCharacter?.character_id === character.character_id }"
                      @click="selectCharacter(character)"
                    >
                      <img :src="character.avatar" :alt="character.name" />
                      <span>{{ character.name }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="option-group">
                <label class="radio-label">
                  <input
                    type="radio"
                    v-model="digitalHumanSource"
                    value="upload"
                  />
                  上传新头像用于生成
                </label>

                <!-- 新头像上传 -->
                <div v-if="digitalHumanSource === 'upload'" class="avatar-upload">
                  <input
                    type="file"
                    ref="avatarFileInput"
                    @change="handleAvatarUpload"
                    accept="image/*"
                    style="display: none;"
                  />
                  <button
                    type="button"
                    class="upload-btn"
                    @click="$refs.avatarFileInput.click()"
                  >
                    选择头像文件
                  </button>
                  <div v-if="uploadedAvatar" class="uploaded-preview">
                    <img :src="uploadedAvatar.preview" alt="上传的头像" />
                    <span>{{ uploadedAvatar.name }}</span>
                  </div>
                  <div class="upload-notice">
                    <p><strong>注意事项：</strong></p>
                    <ul>
                      <li>上传的头像只能包含一个人物</li>
                      <li>人物需要正面向镜头</li>
                      <li>没有较大的肢体动作</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 图片智能动态化选择 -->
          <div class="form-group">
            <label class="checkbox-label">
              <input
                type="checkbox"
                v-model="enableSmartAnimation"
              />
              是否开启图片智能动态化
            </label>
            <p class="form-hint">开启后将自动判断图片是否适合生成动态视频效果</p>
          </div>

          <!-- 事件排序 -->
          <div class="form-group">
            <label>事件排序</label>
            <p class="form-hint">拖动事件卡片可以调整它们在视频中的顺序</p>

            <div class="event-sort-list">
              <div
                v-for="(event, index) in sortableEvents"
                :key="event.id"
                class="sortable-event-card"
                draggable="true"
                @dragstart="dragStart(index)"
                @dragover.prevent
                @dragenter.prevent
                @drop="drop(index)"
              >
                <div class="sort-handle">⋮⋮</div>
                <div class="sortable-event-info">
                  <h4>{{ event.title }}</h4>
                  <p>{{ formatDate(event.datetime) }}</p>
                </div>
                <div class="sort-number">{{ index + 1 }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤导航按钮 -->
        <div class="step-actions">
          <button class="prev-btn" @click="goToPrevStep">
            <span class="btn-icon">←</span>
            上一步
          </button>
          <button
            class="next-btn"
            :disabled="!videoTitle.trim()"
            @click="goToNextStep"
          >
            下一步
            <span class="btn-icon">→</span>
          </button>
        </div>
      </div>

      <!-- 第三步：预览和生成 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="step-header">
          <h2>预览和生成视频</h2>
        </div>

        <!-- 信息摘要 -->
        <div class="summary-section">
          <h3>视频信息</h3>
          <div class="summary-item">
            <span class="summary-label">标题：</span>
            <span class="summary-value">{{ videoTitle }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">描述：</span>
            <span class="summary-value">{{ videoDescription || '无' }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">包含事件：</span>
            <span class="summary-value">{{ sortableEvents.length }}个</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">时间范围：</span>
            <span class="summary-value">{{ trackStartDate && trackEndDate ? `${formatDate(trackStartDate)} 至 ${formatDate(trackEndDate)}` : '未设置' }}</span>
          </div>
        </div>

        <!-- 事件列表摘要 -->
        <div class="events-summary">
          <h3>事件列表</h3>
          <div class="events-summary-list">
            <div v-for="(event, index) in sortableEvents" :key="event.id" class="summary-event">
              <div class="summary-event-number">{{ index + 1 }}</div>
              <div class="summary-event-info">
                <h4>{{ event.title }}</h4>
                <p>{{ formatDate(event.datetime) }} | {{ event.province }} {{ event.city }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 视频预览区域 -->
        <div class="video-preview-section">
          <h3>视频预览</h3>

          <!-- 视频生成中状态 -->
          <div class="video-generating" v-if="isGenerating">
            <div class="generating-icon">⏳</div>
            <p class="generating-text">{{ generationStatus }}</p>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: generationProgress + '%' }"></div>
            </div>
            <p class="progress-text">{{ generationProgress }}%</p>
          </div>

          <!-- 视频预览 -->
          <div class="video-container" v-else-if="videoGenerated">
            <video controls class="video-player">
              <source :src="videoUrl" type="video/mp4">
              您的浏览器不支持视频播放。
            </video>
          </div>

          <!-- 默认占位符 -->
          <div class="video-placeholder" v-else>
            <div class="placeholder-icon">🎬</div>
            <p>点击下方按钮生成视频</p>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="step-actions">
          <button class="prev-btn" @click="goToPrevStep">
            <span class="btn-icon">←</span>
            上一步
          </button>

          <div class="final-actions">
            <button
              v-if="!videoGenerated && !isGenerating"
              class="generate-btn"
              @click="generateVideo"
            >
              一键生成视频
            </button>
            <button
              v-else-if="isGenerating"
              class="generate-btn generating"
              disabled
            >
              {{ generationStatus }}
            </button>
            <template v-else>
              <button class="regenerate-btn" @click="regenerateVideo">
                重新生成
              </button>
              <button class="save-btn" @click="saveAsTrack">
                保存为轨迹
              </button>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import provinceCityMapping from '@/assets/map/province_city_mapping.json'
import service from '@/utils/request.js'

const router = useRouter()

// 步骤管理
const steps = ['选择时光片段', '创作要求', '生成轨迹视频']
const currentStep = ref(0)

// 事件数据
const events = ref([])
const filteredEvents = ref([])
const selectedEvents = ref([])
const provinces = ref([])
const cities = ref([])
const selectedProvince = ref('')
const selectedCity = ref('')
const sortOrder = ref('desc')

// 第二步数据
const videoTitle = ref('')
const videoDescription = ref('')
const sortableEvents = ref([])

// 数字人相关数据
const useDigitalHuman = ref(false)
const digitalHumanSource = ref('existing') // 'existing' 或 'upload'
const userCharacters = ref([])
const selectedCharacter = ref(null)
const uploadedAvatar = ref(null)

// 图片智能动态化
const enableSmartAnimation = ref(true)

// 轨迹时间范围数据
const trackStartDate = ref('')
const trackEndDate = ref('')
const minDate = ref('')
const maxDate = ref('')

// 第三步数据
const videoGenerated = ref(false)
const videoUrl = ref('')

// 监听选中事件变化，更新可排序事件列表和时间范围
watch(selectedEvents, (newVal) => {
  sortableEvents.value = [...newVal]

  // 如果有选中的事件，更新时间范围
  if (newVal.length > 0) {
    updateDateRangeFromEvents(newVal)
  }
}, { deep: true })

// 加载用户事件
const fetchEvents = async () => {
  try {
    // 从后端获取事件列表
    const res = await service({
      method: 'get',
      url: '/issue/issue/0/'
    });

    const idList = res.data.event_ids || [];
    if (idList.length === 0) {
      console.log('未获取到事件数据');
      events.value = [];
      return;
    }

    // 并发请求每个事件详情
    const detailPromises = idList.map(id => {
      const formData = new FormData();
      formData.append('issue_id', id);
      return service({
        method: 'post',
        url: '/issue/issue/0/',
        headers: { 'Content-Type': 'multipart/form-data' },
        data: formData
      }).then(detailRes => {
        const d = detailRes.data;
        // 处理位置信息
        let province = '', city = '';
        if (d.location) {
          console.log('原始位置信息:', d.location);

          // 处理直辖市
          if (d.location.includes('北京') || d.location.includes('上海') ||
              d.location.includes('天津') || d.location.includes('重庆')) {
            // 使用正则表达式匹配直辖市名称和区县名称
            const match = d.location.match(/(北京市|上海市|天津市|重庆市)(.+?区|.+?县)/);
            if (match) {
              province = match[1];  // 例如：天津市
              city = match[2];      // 例如：和平区
            } else {
              // 如果没有匹配到区县，使用完整地址作为城市
              province = d.location;
              city = d.location;
            }
          } else {
            // 处理其他省份
            const match = d.location.match(/(.+?(?:省|自治区|维吾尔自治区|回族自治区|壮族自治区|特别行政区))(.+?(?:市|地区|盟|自治州))(.+?(?:区|县|旗))/);
            if (match) {
              province = match[1];  // 省份
              city = match[2] + match[3];  // 城市+区县
            } else {
              // 如果没有匹配到区县，尝试只匹配省市
              const cityMatch = d.location.match(/(.+?(?:省|自治区|维吾尔自治区|回族自治区|壮族自治区|特别行政区))(.+?(?:市|地区|盟|自治州))/);
              if (cityMatch) {
                province = cityMatch[1];  // 省份
                city = cityMatch[2];      // 城市
              } else {
                // 如果连省市都没匹配到，使用完整地址
                province = d.location;
                city = d.location;
              }
            }
          }
        }

        console.log('处理后的位置信息:', { province, city });

        return {
          id: d.issue_id,
          title: d.title,
          datetime: d.date,
          description: d.description,
          province: province,
          city: city,
          images: d.pictures ? d.pictures.map(p => 'http://127.0.0.1:8000' + p.url) : [],
          pictureIds: d.pictures ? d.pictures.map(p => p.picture_id) : [],
          videos: []
        };
      }).catch(() => null);
    });

    const details = await Promise.all(detailPromises);
    const validDetails = details.filter(e => e);
    events.value = validDetails;
    console.log('转换后的事件数据:', events.value);

    // 初始化日期范围的最小值和最大值
    if (events.value.length > 0) {
      const dates = events.value.map(e => new Date(e.datetime).getTime()).filter(Boolean)
      if (dates.length > 0) {
        const minTime = Math.min(...dates)
        const maxTime = Math.max(...dates)

        minDate.value = new Date(minTime).toISOString().split('T')[0]
        maxDate.value = new Date(maxTime).toISOString().split('T')[0]
      }
    }

    // 使用JSON文件中的完整省份列表
    provinces.value = Object.keys(provinceCityMapping)
    filterEvents()
  } catch (err) {
    console.error('获取事件列表失败', err);
    events.value = [];
  }
}

onMounted(() => {
  fetchEvents();
})

// 步骤导航函数
function goToNextStep() {
  // 如果正在生成视频或已生成完成，不允许切换步骤
  if (isGenerating.value || videoGenerated.value) {
    return
  }

  if (currentStep.value < steps.length - 1) {
    currentStep.value++
    saveStateToStorage() // 保存步骤变化
  }
}

function goToPrevStep() {
  // 如果正在生成视频或已生成完成，不允许切换步骤
  if (isGenerating.value || videoGenerated.value) {
    return
  }

  if (currentStep.value > 0) {
    currentStep.value--
    saveStateToStorage() // 保存步骤变化
  }
}

// 跳转到上传事件页面
function goToUploadEvent() {
  router.push({ name: '上传事件' })
}

// 数字人相关函数
function onDigitalHumanChange() {
  if (useDigitalHuman.value) {
    digitalHumanSource.value = 'existing'
    loadUserCharacters()
  } else {
    selectedCharacter.value = null
    uploadedAvatar.value = null
  }
}

async function loadUserCharacters() {
  try {
    // 获取用户自己的角色信息（relationship='自己'）
    const response = await service({
      method: 'get',
      url: '/character/user-character/'
    })

    if (response.data && response.data.status === 'success' && response.data.data) {
      const userData = response.data.data
      // 如果用户有头像，将其添加到列表中
      if (userData.avatar) {
        userCharacters.value = [userData]
      } else {
        userCharacters.value = []
      }
    } else {
      userCharacters.value = []
    }
  } catch (error) {
    console.error('加载用户角色失败:', error)
    userCharacters.value = []
  }
}

function selectCharacter(character) {
  selectedCharacter.value = character
}

function handleAvatarUpload(event) {
  const file = event.target.files[0]
  if (file) {
    // 创建预览URL
    const preview = URL.createObjectURL(file)
    uploadedAvatar.value = {
      file: file,
      preview: preview,
      name: file.name
    }
  }
}

// 事件筛选函数
function filterEvents() {
  let result = [...events.value]
  if (selectedProvince.value) {
    result = result.filter(e => e.province === selectedProvince.value)
    // 使用JSON文件中的城市列表，而不是从事件中动态获取
    cities.value = provinceCityMapping[selectedProvince.value] || []
  } else {
    cities.value = []
  }
  if (selectedCity.value) {
    result = result.filter(e => e.city === selectedCity.value)
  }
  sortEvents(result)
}

function sortEvents(list = filteredEvents.value) {
  const sorted = [...list].sort((a, b) => {
    return sortOrder.value === 'asc'
      ? new Date(a.datetime) - new Date(b.datetime)
      : new Date(b.datetime) - new Date(a.datetime)
  })
  filteredEvents.value = sorted
}

// 事件选择函数
function toggleSelect(event) {
  const idx = selectedEvents.value.findIndex(e => e.id === event.id)
  if (idx === -1) {
    selectedEvents.value.push(event)
  } else {
    selectedEvents.value.splice(idx, 1)
  }
}

function isSelected(event) {
  return selectedEvents.value.some(e => e.id === event.id)
}

function selectAll() {
  selectedEvents.value = [...filteredEvents.value]
}

function clearAll() {
  selectedEvents.value = []
}

// 日期格式化
function formatDate(dateString) {
  if (!dateString) return ''

  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()

  return `${year}年${month}月${day}日`
}

// 从选中事件更新日期范围
function updateDateRangeFromEvents(events) {
  if (!events || events.length === 0) return

  // 获取所有有效日期
  const dates = events.map(e => new Date(e.datetime).getTime()).filter(Boolean)
  if (dates.length === 0) return

  // 计算最早和最晚日期
  const minTime = Math.min(...dates)
  const maxTime = Math.max(...dates)

  const newStartDate = new Date(minTime).toISOString().split('T')[0]
  const newEndDate = new Date(maxTime).toISOString().split('T')[0]

  // 如果用户没有手动设置日期，或者选中的事件超出了当前范围，则更新范围
  if (!trackStartDate.value || new Date(newStartDate) < new Date(trackStartDate.value)) {
    trackStartDate.value = newStartDate
  }

  if (!trackEndDate.value || new Date(newEndDate) > new Date(trackEndDate.value)) {
    trackEndDate.value = newEndDate
  }
}

// 处理日期范围变化
function handleDateRangeChange() {
  // 如果用户清空了日期，则根据选中的事件重新设置
  if (selectedEvents.value.length > 0) {
    if (!trackStartDate.value || !trackEndDate.value) {
      updateDateRangeFromEvents(selectedEvents.value)
    }
  }
}

// 拖拽排序函数
let draggedItemIndex = null

function dragStart(index) {
  draggedItemIndex = index
}

function drop(index) {
  if (draggedItemIndex === null) return

  // 获取被拖拽的元素
  const draggedItem = sortableEvents.value[draggedItemIndex]

  // 创建新数组，避免直接修改原数组
  const newItems = [...sortableEvents.value]

  // 从原位置删除
  newItems.splice(draggedItemIndex, 1)

  // 插入到新位置
  newItems.splice(index, 0, draggedItem)

  // 更新数组
  sortableEvents.value = newItems

  // 重置拖拽索引
  draggedItemIndex = null
}

// 视频生成状态管理
const isGenerating = ref(false)
const generationProgress = ref(0)
const generationStatus = ref('')
const currentVideoId = ref(null)
let statusCheckInterval = null

// 状态持久化键名
const STORAGE_KEYS = {
  CURRENT_STEP: 'makeVideo_currentStep',
  IS_GENERATING: 'makeVideo_isGenerating',
  GENERATION_PROGRESS: 'makeVideo_generationProgress',
  GENERATION_STATUS: 'makeVideo_generationStatus',
  CURRENT_VIDEO_ID: 'makeVideo_currentVideoId',
  VIDEO_GENERATED: 'makeVideo_videoGenerated',
  VIDEO_URL: 'makeVideo_videoUrl',
  SELECTED_EVENTS: 'makeVideo_selectedEvents',
  VIDEO_TITLE: 'makeVideo_videoTitle',
  VIDEO_DESCRIPTION: 'makeVideo_videoDescription',
  SORTABLE_EVENTS: 'makeVideo_sortableEvents'
}

// 状态持久化函数
function saveStateToStorage() {
  try {
    localStorage.setItem(STORAGE_KEYS.CURRENT_STEP, currentStep.value.toString())
    localStorage.setItem(STORAGE_KEYS.IS_GENERATING, isGenerating.value.toString())
    localStorage.setItem(STORAGE_KEYS.GENERATION_PROGRESS, generationProgress.value.toString())
    localStorage.setItem(STORAGE_KEYS.GENERATION_STATUS, generationStatus.value)
    localStorage.setItem(STORAGE_KEYS.CURRENT_VIDEO_ID, currentVideoId.value || '')
    localStorage.setItem(STORAGE_KEYS.VIDEO_GENERATED, videoGenerated.value.toString())
    localStorage.setItem(STORAGE_KEYS.VIDEO_URL, videoUrl.value)
    localStorage.setItem(STORAGE_KEYS.SELECTED_EVENTS, JSON.stringify(selectedEvents.value))
    localStorage.setItem(STORAGE_KEYS.VIDEO_TITLE, videoTitle.value)
    localStorage.setItem(STORAGE_KEYS.VIDEO_DESCRIPTION, videoDescription.value)
    localStorage.setItem(STORAGE_KEYS.SORTABLE_EVENTS, JSON.stringify(sortableEvents.value))
  } catch (error) {
    console.error('保存状态到localStorage失败:', error)
  }
}

function restoreStateFromStorage() {
  try {
    // 恢复基本状态
    const savedStep = localStorage.getItem(STORAGE_KEYS.CURRENT_STEP)
    const savedIsGenerating = localStorage.getItem(STORAGE_KEYS.IS_GENERATING)
    const savedProgress = localStorage.getItem(STORAGE_KEYS.GENERATION_PROGRESS)
    const savedStatus = localStorage.getItem(STORAGE_KEYS.GENERATION_STATUS)
    const savedVideoId = localStorage.getItem(STORAGE_KEYS.CURRENT_VIDEO_ID)
    const savedVideoGenerated = localStorage.getItem(STORAGE_KEYS.VIDEO_GENERATED)
    const savedVideoUrl = localStorage.getItem(STORAGE_KEYS.VIDEO_URL)
    const savedSelectedEvents = localStorage.getItem(STORAGE_KEYS.SELECTED_EVENTS)
    const savedVideoTitle = localStorage.getItem(STORAGE_KEYS.VIDEO_TITLE)
    const savedVideoDescription = localStorage.getItem(STORAGE_KEYS.VIDEO_DESCRIPTION)
    const savedSortableEvents = localStorage.getItem(STORAGE_KEYS.SORTABLE_EVENTS)

    // 如果有保存的生成状态，恢复它们
    if (savedIsGenerating === 'true' || savedVideoGenerated === 'true') {
      console.log('检测到之前的视频生成状态，正在恢复...')

      // 恢复步骤到第三步
      currentStep.value = 2

      // 恢复生成状态
      isGenerating.value = savedIsGenerating === 'true'
      generationProgress.value = parseInt(savedProgress) || 0
      generationStatus.value = savedStatus || ''
      currentVideoId.value = savedVideoId || null
      videoGenerated.value = savedVideoGenerated === 'true'
      videoUrl.value = savedVideoUrl || ''

      // 恢复事件数据
      if (savedSelectedEvents) {
        selectedEvents.value = JSON.parse(savedSelectedEvents)
      }
      if (savedVideoTitle) {
        videoTitle.value = savedVideoTitle
      }
      if (savedVideoDescription) {
        videoDescription.value = savedVideoDescription
      }
      if (savedSortableEvents) {
        sortableEvents.value = JSON.parse(savedSortableEvents)
      }

      // 如果正在生成中，恢复状态轮询
      if (isGenerating.value && currentVideoId.value) {
        console.log('恢复视频生成状态轮询...')
        startStatusPolling()
      }

      return true // 表示恢复了状态
    }

    return false // 表示没有需要恢复的状态
  } catch (error) {
    console.error('从localStorage恢复状态失败:', error)
    return false
  }
}

function clearStoredState() {
  try {
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key)
    })
    console.log('已清除存储的状态')
  } catch (error) {
    console.error('清除存储状态失败:', error)
  }
}

// 视频生成相关函数
async function generateVideo() {
  try {
    if (selectedEvents.value.length === 0) {
      window.alert('请至少选择一个事件')
      return
    }

    // 获取选中事件的图片ID
    const pictureIds = selectedEvents.value
      .flatMap(event => event.pictureIds)
      .filter(id => !!id) // 过滤无效id

    if (pictureIds.length === 0) {
      window.alert('所选事件没有图片，无法生成视频')
      return
    }

    // 设置生成状态
    isGenerating.value = true
    generationProgress.value = 0
    generationStatus.value = '正在努力制作轨迹视频...'
    videoGenerated.value = false
    videoUrl.value = ''

    // 保存状态到localStorage
    saveStateToStorage()

    // 准备请求数据
    const requestData = {
      pictures: pictureIds,
      title: videoTitle.value || '我的轨迹视频',           // 使用用户输入的标题
      description: videoDescription.value || '美好的回忆', // 使用用户输入的描述
      use_digital_human: useDigitalHuman.value,           // 是否使用数字人开头
      enable_smart_animation: enableSmartAnimation.value  // 是否开启图片智能动态化
    }

    // 如果使用数字人，添加相关信息
    if (useDigitalHuman.value) {
      if (digitalHumanSource.value === 'existing' && selectedCharacter.value) {
        requestData.digital_human_avatar = selectedCharacter.value.avatar
        requestData.digital_human_source = 'existing'
        requestData.character_id = selectedCharacter.value.character_id
      } else if (digitalHumanSource.value === 'upload' && uploadedAvatar.value) {
        requestData.digital_human_source = 'upload'
        // 这里需要上传文件，我们使用FormData
        const formData = new FormData()
        formData.append('pictures', JSON.stringify(pictureIds))
        formData.append('title', videoTitle.value || '我的轨迹视频')
        formData.append('description', videoDescription.value || '美好的回忆')
        formData.append('use_digital_human', useDigitalHuman.value)
        formData.append('enable_smart_animation', enableSmartAnimation.value)
        formData.append('digital_human_source', 'upload')
        formData.append('digital_human_avatar', uploadedAvatar.value.file)

        // 使用FormData发送请求
        const response = await service({
          method: 'post',
          url: '/video/create/',
          data: formData,
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })

        if (response.status === 202) {
          // 异步任务启动成功
          currentVideoId.value = response.data.video_id
          generationStatus.value = response.data.message

          // 开始轮询状态
          startStatusPolling()
        } else {
          throw new Error(response.data.error || '启动视频生成失败')
        }
        return
      }
    }

    // 使用JSON发送请求
    const response = await service({
      method: 'post',
      url: '/video/create/',
      data: requestData,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (response.status === 202) {
      // 异步任务启动成功
      currentVideoId.value = response.data.video_id
      generationStatus.value = response.data.message

      // 开始轮询状态
      startStatusPolling()

    } else {
      throw new Error(response.data.error || '启动视频生成失败')
    }

  } catch (error) {
    console.error('生成视频时出错：', error)
    isGenerating.value = false
    generationStatus.value = '生成失败'
    window.alert(error.response?.data?.error || '生成视频失败，请稍后重试')
  }
}

// 开始状态轮询
function startStatusPolling() {
  if (statusCheckInterval) {
    clearInterval(statusCheckInterval)
  }

  statusCheckInterval = setInterval(async () => {
    await checkVideoStatus()
  }, 2000) // 每2秒检查一次
}

// 停止状态轮询
function stopStatusPolling() {
  if (statusCheckInterval) {
    clearInterval(statusCheckInterval)
    statusCheckInterval = null
  }
}

// 检查视频生成状态
async function checkVideoStatus() {
  if (!currentVideoId.value) return

  try {
    const response = await service({
      method: 'get',
      url: `/video/status/?video_id=${currentVideoId.value}`
    })

    if (response.status === 200) {
      const status = response.data
      generationProgress.value = status.progress
      generationStatus.value = getStatusMessage(status.status, status.progress)

      // 每次状态更新都保存到localStorage
      saveStateToStorage()

      if (status.status === 'completed') {
        // 生成完成
        stopStatusPolling()
        isGenerating.value = false
        videoGenerated.value = true
        videoUrl.value = 'http://127.0.0.1:8000' + status.video_url
        generationStatus.value = '视频生成完成！'

        // 保存完成状态
        saveStateToStorage()

        window.alert('视频生成成功！')

      } else if (status.status === 'failed') {
        // 生成失败
        stopStatusPolling()
        isGenerating.value = false
        generationStatus.value = '生成失败'

        // 保存失败状态
        saveStateToStorage()

        window.alert(status.error_message || '视频生成失败')
      }
    }

  } catch (error) {
    console.error('检查状态时出错:', error)
  }
}

// 获取状态消息
function getStatusMessage(status, progress) {
  const messages = {
    'pending': '等待开始...',
    'processing': `正在努力制作轨迹视频... ${progress}%`,
    'completed': '视频生成完成！',
    'failed': '生成失败'
  }
  return messages[status] || '未知状态'
}

// 重新生成视频
async function regenerateVideo() {
  await generateVideo()
}

// 获取用户视频列表
const fetchUserVideos = async () => {
  try {
    const response = await service({
      method: 'get',
      url: '/video/create/'
    })

    if (response.data.video_ids) {
      // 这里可以处理获取到的视频ID列表
      console.log('用户视频列表:', response.data.video_ids)
    }
  } catch (error) {
    console.error('获取视频列表失败：', error)
  }
}

// 在组件挂载时获取视频列表
onMounted(async () => {
  // 先获取事件数据
  await fetchEvents()

  // 尝试恢复之前的状态
  const stateRestored = restoreStateFromStorage()

  if (stateRestored) {
    console.log('已恢复之前的视频生成状态')
  } else {
    // 如果没有恢复状态，正常获取视频列表
    fetchUserVideos()
  }
})

// 组件卸载时清理资源
onUnmounted(() => {
  stopStatusPolling()
})

// function regenerateVideo() {
//   videoGenerated.value = false
//   generateVideo()
// }

async function saveAsTrack() {
  try {
    // 调用后端保存接口
    if (currentVideoId.value) {
      const response = await service({
        method: 'post',
        url: '/video/save/',
        data: {
          video_id: currentVideoId.value
        },
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (response.status === 200) {
        alert('视频已保存为轨迹！')

        // 清除存储的状态
        clearStoredState()

        // 重置所有状态到初始值
        resetAllStates()

        // 重新获取事件数据
        await fetchEvents()

      } else {
        alert('保存失败，请重试')
      }
    } else {
      alert('没有可保存的视频')
    }
  } catch (error) {
    console.error('保存视频失败:', error)
    alert('保存失败，请重试')
  }
}

// 重置所有状态到初始值
function resetAllStates() {
  currentStep.value = 0
  isGenerating.value = false
  generationProgress.value = 0
  generationStatus.value = ''
  currentVideoId.value = null
  videoGenerated.value = false
  videoUrl.value = ''
  selectedEvents.value = []
  videoTitle.value = ''
  videoDescription.value = ''
  sortableEvents.value = []
  trackStartDate.value = ''
  trackEndDate.value = ''

  // 停止状态轮询
  stopStatusPolling()
}
</script>

<style scoped>
/* 导入字体 */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap');

/* 基础样式 */
.make-video-bg {
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #e0e7ff 0%, #f0fdfa 100%);
  background-image:
    radial-gradient(circle at 10% 20%, rgba(120, 119, 198, 0.05) 0%, transparent 20%),
    radial-gradient(circle at 90% 80%, rgba(242, 202, 241, 0.08) 0%, transparent 20%),
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.9) 0%, transparent 100%);
  padding: 0;
  margin: 0;
  border-radius: 20px;
  font-family: 'Noto Sans SC', sans-serif;
}

.make-video-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 40px 32px 60px;
  min-height: 100vh;
}

.make-video-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.make-video-header h1 {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1e3a8a;
  margin: 0;
  background: linear-gradient(90deg, #1e3a8a, #3b82f6);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

/* 步骤指示器 */
.steps-indicator {
  display: flex;
  justify-content: space-between;
  margin: 3rem 0;
  position: relative;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  flex: 1;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e0e7ff;
  color: #4f46e5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  border: 2px solid #c7d2fe;
  transition: all 0.3s ease;
}

.step-title {
  font-size: 1rem;
  color: #6b7280;
  font-weight: 500;
  transition: all 0.3s ease;
}

.step-connector {
  position: absolute;
  top: 20px;
  left: 50%;
  right: -50%;
  height: 2px;
  background: #e0e7ff;
  z-index: 1;
}

.step.active .step-number {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
  transform: scale(1.1);
  box-shadow: 0 0 15px rgba(79, 70, 229, 0.4);
}

.step.active .step-title {
  color: #4f46e5;
  font-weight: 700;
}

.step.completed .step-number {
  background: #10b981;
  color: white;
  border-color: #10b981;
}

.step.completed .step-connector {
  background: #10b981;
}

/* 步骤内容区域 */
.step-content {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.step-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f3f4f6;
}

.step-header h2 {
  font-size: 1.5rem;
  color: #1e3a8a;
  margin: 0;
  font-weight: 700;
}

.action-buttons {
  display: flex;
  gap: 0.8rem;
}

/* 按钮样式 */
.select-btn, .action-btn {
  padding: 0.6rem 1.2rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.select-btn:hover, .action-btn:hover {
  background: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
}

/* 筛选器 */
.filters {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.2rem;
  border-radius: 0.8rem;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(30, 58, 138, 0.08);
  box-shadow: 0 4px 20px rgba(30, 58, 138, 0.05);
}

.filters select {
  padding: 0.7rem 1.2rem;
  font-size: 0.95rem;
  border-radius: 0.5rem;
  border: 1px solid #d1d5db;
  background: white;
  transition: all 0.2s;
  min-width: 150px;
  cursor: pointer;
}

.filters select:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* 轨迹时间范围选择器 */
.time-range-selector {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(30, 58, 138, 0.08);
  border-radius: 0.8rem;
  padding: 1.2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(30, 58, 138, 0.05);
}

.time-range-title {
  font-size: 1.1rem;
  color: #1e40af;
  margin-top: 0;
  margin-bottom: 1rem;
  font-weight: 600;
}

.time-range-inputs {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.time-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
  min-width: 150px;
}

.time-field label {
  font-size: 0.9rem;
  color: #4b5563;
  font-weight: 500;
}

.time-input {
  padding: 0.7rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.95rem;
  background: white;
  transition: all 0.2s;
}

.time-input:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.time-separator {
  font-weight: 500;
  color: #6b7280;
  margin-top: 1.5rem;
}

.time-range-info {
  margin-top: 1rem;
  padding-top: 0.8rem;
  border-top: 1px solid rgba(219, 234, 254, 0.5);
}

.time-range-info p {
  margin: 0;
  font-size: 0.9rem;
  color: #4b5563;
}

/* 无事件提示 */
.no-events {
  text-align: center;
  padding: 3rem 2rem;
  background: #f9fafb;
  border-radius: 0.8rem;
  margin: 2rem 0;
}

.no-events-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #9ca3af;
}

.no-events h3 {
  font-size: 1.3rem;
  color: #4b5563;
  margin-bottom: 0.5rem;
}

.no-events p {
  color: #6b7280;
  margin-bottom: 1.5rem;
}

/* 事件列表 */
.event-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.event-card {
  display: flex;
  align-items: flex-start;
  background: #f9fafb;
  border-radius: 0.8rem;
  padding: 1.2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  border: 2px solid transparent;
}

.event-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.event-card.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.checkbox {
  width: 1.8rem;
  height: 1.8rem;
  border: 2px solid #3b82f6;
  border-radius: 0.4rem;
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  background: white;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s;
  flex-shrink: 0;
}

.checkbox span {
  color: #10b981;
  font-weight: bold;
}

.checkbox:hover {
  border-color: #10b981;
  background: #f0fdfa;
}

.event-info {
  flex: 1;
}

.event-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  color: #1e293b;
  font-weight: 700;
}

.event-info p {
  color: #64748b;
  font-size: 0.9rem;
  margin: 0;
}

/* 预览区域 */
.preview-section {
  margin: 2rem 0;
  background: #f8fafc;
  border-radius: 0.8rem;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.preview-section h2 {
  font-size: 1.2rem;
  color: #1e40af;
  margin-top: 0;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.preview-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 300px;
  overflow-y: auto;
}

.preview-section li {
  padding: 0.8rem;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.95rem;
  color: #1e293b;
}

.preview-section li:last-child {
  border-bottom: none;
}

/* 步骤导航按钮 */
.step-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

.prev-btn, .next-btn {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.prev-btn {
  background: #f3f4f6;
  color: #4b5563;
}

.prev-btn:hover {
  background: #e5e7eb;
  transform: translateX(-2px);
}

.next-btn {
  background: #3b82f6;
  color: white;
}

.next-btn:hover:not(:disabled) {
  background: #2563eb;
  transform: translateX(2px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
}

.next-btn:disabled {
  background: #cbd5e1;
  cursor: not-allowed;
}

.btn-icon {
  font-size: 1.2rem;
}

/* 第二步样式 */
.creation-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: #1e293b;
  font-size: 1rem;
}

.form-hint {
  font-size: 0.9rem;
  color: #6b7280;
  margin: 0 0 0.8rem 0;
}

.form-input, .form-textarea {
  padding: 0.8rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: all 0.2s;
}

.form-input:focus, .form-textarea:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

/* 排序列表 */
.event-sort-list {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  margin-top: 1rem;
}

.sortable-event-card {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  cursor: grab;
  transition: all 0.2s;
  position: relative;
}

.sortable-event-card:hover {
  background: #f9fafb;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.sortable-event-card:active {
  cursor: grabbing;
}

.sort-handle {
  color: #9ca3af;
  font-size: 1.2rem;
  margin-right: 1rem;
  cursor: grab;
}

.sortable-event-info {
  flex: 1;
}

.sortable-event-info h4 {
  margin: 0 0 0.3rem 0;
  font-size: 1rem;
  color: #1e293b;
}

.sortable-event-info p {
  margin: 0;
  font-size: 0.9rem;
  color: #6b7280;
}

.sort-number {
  width: 1.8rem;
  height: 1.8rem;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

/* 第三步样式 */
.summary-section, .events-summary {
  background: #f8fafc;
  border-radius: 0.8rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.summary-section h3, .events-summary h3, .video-preview-section h3 {
  font-size: 1.2rem;
  color: #1e40af;
  margin-top: 0;
  margin-bottom: 1rem;
}

.summary-item {
  display: flex;
  margin-bottom: 0.8rem;
  padding-bottom: 0.8rem;
  border-bottom: 1px solid #e5e7eb;
}

.summary-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.summary-label {
  font-weight: 600;
  color: #4b5563;
  width: 80px;
  flex-shrink: 0;
}

.summary-value {
  color: #1e293b;
  flex: 1;
}

.events-summary-list {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  max-height: 300px;
  overflow-y: auto;
}

.summary-event {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 0.5rem;
  padding: 0.8rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.summary-event-number {
  width: 1.8rem;
  height: 1.8rem;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  margin-right: 1rem;
  flex-shrink: 0;
}

.summary-event-info {
  flex: 1;
}

.summary-event-info h4 {
  margin: 0 0 0.3rem 0;
  font-size: 1rem;
  color: #1e293b;
}

.summary-event-info p {
  margin: 0;
  font-size: 0.9rem;
  color: #6b7280;
}

.video-preview-section {
  background: #f8fafc;
  border-radius: 0.8rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.video-container {
  width: 100%;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.video-player {
  width: 100%;
  display: block;
}

.video-placeholder {
  height: 300px;
  background: #e5e7eb;
  border-radius: 0.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

.placeholder-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.final-actions {
  display: flex;
  gap: 1rem;
}

.generate-btn, .regenerate-btn, .save-btn {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.generate-btn {
  background: linear-gradient(90deg, #3b82f6, #10b981);
  color: white;
}

.generate-btn:hover {
  background: linear-gradient(90deg, #2563eb, #059669);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
}

.regenerate-btn {
  background: #3b82f6;
  color: white;
}

.regenerate-btn:hover {
  background: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
}

.save-btn {
  background: #10b981;
  color: white;
}

.save-btn:hover {
  background: #059669;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .step-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .event-list {
    grid-template-columns: 1fr;
  }

  .step-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .prev-btn, .next-btn {
    width: 100%;
    justify-content: center;
  }

  .final-actions {
    flex-direction: column;
    width: 100%;
  }

  .generate-btn, .regenerate-btn, .save-btn {
    width: 100%;
    justify-content: center;
  }
}

/* 视频生成中状态样式 */
.video-generating {
  height: 300px;
  background: #f8fafc;
  border-radius: 0.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #495057;
}

.generating-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  animation: pulse 2s infinite;
}

.generating-text {
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
  color: #6c757d;
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  max-width: 300px;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  margin: 0 auto 0.8rem;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #10b981);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 1rem;
  color: #3b82f6;
  font-weight: 600;
}

.generate-btn.generating {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.7;
}

.generate-btn.generating:hover {
  background: #6c757d;
  transform: none;
  box-shadow: none;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 数字人选择样式 */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.digital-human-options {
  margin-top: 1rem;
  padding: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.option-group {
  margin-bottom: 1rem;
}

.option-group:last-child {
  margin-bottom: 0;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  margin-bottom: 0.5rem;
}

.radio-label input[type="radio"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.avatar-selection {
  margin-left: 1.5rem;
  margin-top: 0.5rem;
}

.no-avatars {
  color: #666;
  font-style: italic;
  padding: 1rem;
  text-align: center;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.avatar-list {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.avatar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem;
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: white;
}

.avatar-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.avatar-item.selected {
  border-color: #409eff;
  background-color: #f0f8ff;
}

.avatar-item img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 0.5rem;
}

.avatar-item span {
  font-size: 0.9rem;
  text-align: center;
}

.avatar-upload {
  margin-left: 1.5rem;
  margin-top: 0.5rem;
}

.upload-btn {
  padding: 0.5rem 1rem;
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.upload-btn:hover {
  background-color: #337ecc;
}

.uploaded-preview {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
  padding: 0.5rem;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.uploaded-preview img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.upload-notice {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  color: #856404;
}

.upload-notice p {
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.upload-notice ul {
  margin: 0;
  padding-left: 1.5rem;
}

.upload-notice li {
  margin-bottom: 0.25rem;
}
</style>