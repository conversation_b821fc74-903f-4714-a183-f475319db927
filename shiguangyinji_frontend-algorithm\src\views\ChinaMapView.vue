<template>
  <main class="map-view-wrapper">
    <div class="map-container">
    <!-- 欢迎消息 -->
    <div class="welcome-banner animate-fadeIn">
      <div class="welcome-icon">🌏</div>
      <p class="welcome-message">
        {{ welcomeMessage }}
      </p>
    </div>

    <!-- 地图区域 -->
    <div class="map-section">
      <div class="map-wrapper">
        <!-- 地图切换按钮 -->
        <button class="map-toggle-button" @click="toggleMapView">
          <span class="map-toggle-icon">
            <i class="fa fa-exchange"></i>
          </span>
          <span class="map-toggle-text">
            {{ buttonText }}
          </span>
        </button>

        <!-- 加载指示器 -->
        <div v-if="isLoadingMapEvents || isLoadingFriendsEvents" class="loading-overlay">
          <div class="loading-spinner"></div>
          <p class="loading-text">正在加载数据...</p>
        </div>

        <!-- 地图组件 -->
        <component
          v-else
          :is="currentMapComponent"
          :key="isChinaMap ? 'china-map' : 'friends-map'"
          :events="currentEvents"
          :highlighted-provinces="highlightedProvinces"
          class="map-component"
        />
      </div>

      <!-- 统计卡片 -->
      <section class="summary-cards">
        <div class="card events-card">
          <div class="card-icon">📊</div>
          <div class="card-content">
            <h2>{{ totalEvents }}</h2>
            <p>记录的事件</p>
          </div>
        </div>

        <div
          class="card province-card"
          @mouseover="handleCardHover(true)"
          @mouseout="handleCardHover(false)"
          @click="handleProvinceClick"
        >
          <div class="card-icon">🗺️</div>
          <div class="card-content">
            <h2>{{ uniqueProvinces }}</h2>
            <p>{{ provinceLabel }}</p>
          </div>
        </div>
      </section>
    </div>

    <!-- 事件列表 -->
    <section class="event-list-section">
      <div class="section-header">
        <h2 class="list-title">{{ listTitle }}</h2>
        <div class="section-divider"></div>
      </div>

      <ul class="event-grid">
        <li v-for="(event, index) in currentEvents" :key="index" class="event-item">
          <div class="event-image-container">
            <img :src="event.image" alt="event image" class="event-image" />
            <div class="event-location-badge">
              <span>{{ locationText(event) }}</span>
            </div>
          </div>

          <div class="event-info">
            <h3 class="event-title">{{ eventTitle(event) }}</h3>
            <p class="event-description">{{ event.description }}</p>

            <button class="event-detail-btn" @click="showEventDetail(event)">
              <span class="btn-text">查看详细</span>
              <span class="btn-icon">→</span>
            </button>
          </div>
        </li>
      </ul>
    </section>

    <!-- 事件详情弹窗 -->
    <div v-if="selectedEvent" class="event-detail-modal" @click="closeEventDetail">
      <div class="modal-content" @click.stop>
        <button class="modal-close-btn" @click="closeEventDetail">×</button>

        <div class="modal-image">
          <img :src="selectedEvent.image" alt="Event image" />
        </div>

        <div class="modal-body">
          <h2 class="modal-title">{{ eventTitle(selectedEvent) }}</h2>
          <div class="modal-location">
            <span class="location-icon">📍</span>
            <span>{{ locationText(selectedEvent) }}</span>
          </div>

          <div class="modal-divider"></div>

          <p class="modal-description">{{ selectedEvent.description }}</p>

          <div class="modal-footer">
            <button class="modal-action-btn">分享</button>
            <button class="modal-action-btn primary">收藏</button>
          </div>
        </div>
      </div>
    </div>
    </div>
  </main>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import ChinaMap from '@/components/ChinaMap.vue'
import FriendsMap from '@/components/FriendsMap.vue'
import service from '@/utils/request.js'

// 状态管理
const isChinaMap = ref(true)
const isCardHovered = ref(false)
const highlightedProvinces = ref([])
const selectedEvent = ref(null)
const mapEvents = ref([])
const friendsEvents = ref([])

// 添加加载状态管理
const isLoadingMapEvents = ref(false)
const isLoadingFriendsEvents = ref(false)
const mapEventsLoaded = ref(false)
const friendsEventsLoaded = ref(false)

// Mock数据
const mockFriendsEvents = [
  {
    id: 101,
    friend_name: '旅行者小明',
    city_location: '拉萨市',
    province_location: '西藏自治区',
    description: '这次西藏之行让我深深震撼，从拉萨的布达拉宫到纳木错的湖光山色，每一处风景都是大自然的杰作。在海拔4000多米的高原上，我感受到了前所未有的宁静与纯净。',
    date: '2023-05-20',
    image: new URL('@/assets/covers/tibet.jpg', import.meta.url).href
  },
  {
    id: 102,
    friend_name: '青春记录者小红',
    city_location: '武汉市',
    province_location: '湖北省',
    description: '四年大学时光即将结束，和朋友们拍下了最后一张合影。那些一起熬夜、一起哭笑的日子，都定格在这张照片里。愿友谊长存，青春不老。',
    date: '2023-05-21',
    image: new URL('@/assets/covers/graduation.jpg', import.meta.url).href
  },
  {
    id: 103,
    friend_name: '浪漫主义者小李',
    city_location: '三亚市',
    province_location: '海南省',
    description: '在三亚的海边，我和她手牵手看着夕阳西下，海浪轻抚着沙滩，微风吹过发梢。那一刻，时间仿佛静止了，只有我们两个人和这片金色的海洋。',
    date: '2023-05-22',
    image: new URL('@/assets/covers/beach.jpg', import.meta.url).href
  },
  {
    id: 104,
    friend_name: '户外爱好者小王',
    city_location: '泰安市',
    province_location: '山东省',
    description: '经过6小时的艰难攀登，终于站在了泰山之巅。虽然双腿酸痛，但看到日出那一刻的壮丽景色，所有的疲惫都烟消云散。这次登山让我明白，坚持就是胜利。',
    date: '2023-06-05',
    image: new URL('@/assets/covers/mountain.jpg', import.meta.url).href
  }
]

// 转换事件数据格式的函数
const convertEventsFormat = (events) => {
    return events.map(event => ({
        id: event.id,
        city_location: event.city,
        province_location: event.province,
        title: event.title,
        description: event.description,
        date: event.date,
        image: event.coverImage || ''
    }));
}

// 添加获取事件列表的方法
const fetchEvents = async () => {
    if (isLoadingMapEvents.value) return; // 防止重复请求

    isLoadingMapEvents.value = true;
    mapEventsLoaded.value = false;

    try {
        // 从后端获取
        const res = await service({
            method: 'get',
            url: '/issue/issue/0/'
        });

        const idList = res.data.event_ids || [];
        if (idList.length === 0) {
            mapEvents.value = [];
            mapEventsLoaded.value = true;
            return;
        }

        // 并发请求每个事件详情
        const detailPromises = idList.map(id => {
            const formData = new FormData();
            formData.append('issue_id', id);
            return service({
                method: 'post',
                url: '/issue/issue/0/',
                headers: { 'Content-Type': 'multipart/form-data' },
                data: formData
            }).then(detailRes => {
                const d = detailRes.data;
                // 处理位置信息
                let province = '', city = '';
                if (d.location) {
                    console.log('原始位置信息:', d.location);

                    // 处理直辖市
                    if (d.location.includes('北京') || d.location.includes('上海') ||
                        d.location.includes('天津') || d.location.includes('重庆')) {
                        // 使用正则表达式匹配直辖市名称和区县名称
                        const match = d.location.match(/(北京市|上海市|天津市|重庆市)(.+?区|.+?县)/);
                        if (match) {
                            province = match[1];  // 例如：天津市
                            city = match[2];      // 例如：和平区
                        } else {
                            // 如果没有匹配到区县，使用完整地址作为城市
                            province = d.location;
                            city = d.location;
                        }
                    } else {
                        // 处理其他省份
                        const match = d.location.match(/(.+?(?:省|自治区|维吾尔自治区|回族自治区|壮族自治区|特别行政区))(.+?(?:市|地区|盟|自治州))/);
                        if (match) {
                            province = match[1];  // 省份
                            city = match[2];      // 城市
                        } else {
                            // 如果没有匹配到完整格式，尝试只匹配省份
                            const provinceMatch = d.location.match(/(.+?(?:省|自治区|维吾尔自治区|回族自治区|壮族自治区|特别行政区))/);
                            if (provinceMatch) {
                                province = provinceMatch[1];
                                city = d.location;
                            } else {
                                // 如果连省份都没匹配到，使用完整地址
                                province = d.location;
                                city = d.location;
                            }
                        }
                    }
                }

                console.log('处理后的位置信息:', { province, city });

                return {
                    id: d.issue_id,
                    title: d.title,
                    date: d.date,
                    description: d.description,
                    province_location: province,
                    city_location: city,
                    image: (d.pictures && d.pictures.length > 0) ? 'http://127.0.0.1:8000' + d.pictures[0].url : '',
                    pictures: d.pictures || [],
                    author: d.author
                };
            }).catch(() => null);
        });

        const details = await Promise.all(detailPromises);
        const validDetails = details.filter(e => e);

        // 使用 nextTick 确保数据更新完成后再设置状态
        await nextTick();
        mapEvents.value = validDetails;
        mapEventsLoaded.value = true;
        console.log('转换后的事件数据:', mapEvents.value);
    } catch (err) {
        console.error('获取事件列表失败', err);
        mapEvents.value = [];
        mapEventsLoaded.value = true;
    } finally {
        isLoadingMapEvents.value = false;
    }
}

// 获取朋友活动列表
const fetchFriendsEvents = async () => {
    if (isLoadingFriendsEvents.value) return; // 防止重复请求

    isLoadingFriendsEvents.value = true;
    friendsEventsLoaded.value = false;

    try {
        // 直接使用mock数据展示好友足迹
        console.log('直接使用mock数据展示好友足迹');
        await nextTick();
        friendsEvents.value = mockFriendsEvents;
        friendsEventsLoaded.value = true;
        console.log('好友足迹数据已加载:', friendsEvents.value);
    } catch (err) {
        console.error('加载好友足迹数据出错:', err);
        await nextTick();
        friendsEvents.value = mockFriendsEvents;
        friendsEventsLoaded.value = true;
    } finally {
        isLoadingFriendsEvents.value = false;
    }
}

// 在组件挂载时获取事件列表
onMounted(async () => {
    // 并行加载数据，提高性能
    await Promise.all([
        fetchEvents(),
        fetchFriendsEvents()
    ]);
})

// 计算属性
const currentMapComponent = computed(() =>
  isChinaMap.value ? ChinaMap : FriendsMap
)

const currentEvents = computed(() => {
  // 确保数据已加载完成
  const isCurrentDataLoaded = isChinaMap.value ? mapEventsLoaded.value : friendsEventsLoaded.value;
  if (!isCurrentDataLoaded) return [];

  return isChinaMap.value ? mapEvents.value : friendsEvents.value;
})

const welcomeMessage = computed(() =>
  isChinaMap.value
    ? '欢迎回来，今天也一起走过世界的一角吧 👣'
    : '看看朋友们都去了哪里 👀'
)

const buttonText = computed(() =>
  isChinaMap.value ? '切换到好友地图' : '切换到我的足迹'
)

const totalEvents = computed(() => {
  // 确保数据已加载完成再计算
  const isCurrentDataLoaded = isChinaMap.value ? mapEventsLoaded.value : friendsEventsLoaded.value;
  if (!isCurrentDataLoaded) return 0;

  return currentEvents.value.length;
})

const uniqueProvinces = computed(() => {
  // 确保数据已加载完成再计算
  const isCurrentDataLoaded = isChinaMap.value ? mapEventsLoaded.value : friendsEventsLoaded.value;
  if (!isCurrentDataLoaded) return 0;

  return new Set(currentEvents.value.map(e => e.province_location)).size;
})

const provinceLabel = computed(() =>
  isChinaMap.value ? '去过的省份' : '朋友去过的省份'
)

const listTitle = computed(() =>
  isChinaMap.value ? '我的足迹记录' : '好友动态'
)

// 方法
const toggleMapView = async () => {
  // 防止在数据加载过程中切换
  if (isLoadingMapEvents.value || isLoadingFriendsEvents.value) {
    console.log('数据正在加载中，请稍后再切换地图');
    return;
  }

  // 确保目标数据已加载
  const targetDataLoaded = !isChinaMap.value ? mapEventsLoaded.value : friendsEventsLoaded.value;
  if (!targetDataLoaded) {
    console.log('目标数据尚未加载完成，正在加载...');
    if (!isChinaMap.value && !mapEventsLoaded.value) {
      await fetchEvents();
    } else if (isChinaMap.value && !friendsEventsLoaded.value) {
      await fetchFriendsEvents();
    }
  }

  // 使用 nextTick 确保状态更新完成
  await nextTick();
  isChinaMap.value = !isChinaMap.value;
  highlightedProvinces.value = [];
  // 关闭任何打开的弹窗
  selectedEvent.value = null;
}

const eventTitle = (event) => {
  return isChinaMap.value ? event.title : `${event.friend_name}的动态`
}

const locationText = (event) => {
  // 统一使用"省-市"格式
  return `${event.province_location} - ${event.city_location}`;
}

const handleProvinceClick = () => {
  if (highlightedProvinces.value.length > 0) {
    highlightedProvinces.value = []
  } else {
    const provinces = Array.from(
      new Set(currentEvents.value.map(e => e.province_location))
    )
    highlightedProvinces.value = provinces
  }
}

const handleCardHover = (isHovered) => {
  isCardHovered.value = isHovered
}

// 事件详情弹窗相关方法
const showEventDetail = (event) => {
  selectedEvent.value = event
  // 添加禁止滚动的类到body
  document.body.classList.add('modal-open')
}

const closeEventDetail = () => {
  selectedEvent.value = null
  // 移除禁止滚动的类
  document.body.classList.remove('modal-open')
}

</script>

<style scoped>
/* 地图页面局部样式 */
.map-view-wrapper {
  /* 地图页面特定的CSS变量 */
  --map-primary-color: #3b82f6;
  --map-primary-dark: #1d4ed8;
  --map-primary-light: #60a5fa;
  --map-secondary-color: #10b981;
  --map-secondary-dark: #059669;
  --map-accent-color: #f59e0b;
  --map-accent-dark: #d97706;
  --map-bg-light: #f3f4f6;
  --map-bg-white: #ffffff;
  --map-bg-blue-light: #e0e7ff;
  --map-bg-green-light: #d1fae5;
  --map-text-dark: #111827;
  --map-text-medium: #4b5563;
  --map-text-light: #9ca3af;
  --map-shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
  --map-shadow-md: 0 4px 8px rgba(0,0,0,0.1);
  --map-shadow-lg: 0 8px 16px rgba(0,0,0,0.1);
  --map-shadow-xl: 0 12px 24px rgba(0,0,0,0.15);
  --map-radius-sm: 0.5rem;
  --map-radius-md: 1rem;
  --map-radius-lg: 1.5rem;
  --map-radius-xl: 2rem;
  --map-transition-fast: 0.2s;
  --map-transition-normal: 0.3s;
  --map-transition-slow: 0.5s;
}

/* 主容器 */
.map-container {
  max-width: 96rem;
  margin: 2rem auto;
  padding: 2.5rem 2rem 3rem 2rem;
  border-radius: var(--map-radius-xl);
  background: var(--map-bg-blue-light);
  background-image: linear-gradient(135deg, rgba(224, 231, 255, 0.95) 0%, rgba(239, 246, 255, 0.95) 100%);
  box-shadow: var(--map-shadow-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.map-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, var(--map-primary-color), var(--map-secondary-color));
  z-index: 1;
}

/* 欢迎横幅 */
.welcome-banner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem 2rem;
  background-color: var(--map-bg-white);
  border-radius: var(--map-radius-lg);
  box-shadow: var(--map-shadow-md);
  width: 100%;
  max-width: 40rem;
}

.welcome-icon {
  font-size: 2rem;
  animation: pulse 2s infinite;
}

.welcome-message {
  text-align: center;
  color: var(--map-text-medium);
  font-size: 1.125rem;
  font-weight: 500;
  margin: 0;
}

/* 地图区域 */
.map-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 3rem;
}

.map-wrapper {
  position: relative;
  width: 100%;
  max-width: 1200px;
  margin-bottom: 2rem;
  border-radius: var(--map-radius-lg);
  overflow: hidden;
  box-shadow: var(--map-shadow-xl);
}

.map-component {
  border-radius: var(--map-radius-lg);
  overflow: hidden;
}

/* 地图切换按钮 */
.map-toggle-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(90deg, var(--map-primary-color) 0%, var(--map-primary-light) 100%);
  color: white;
  padding: 0.7rem 1.5rem;
  border-radius: 9999px;
  font-size: 1rem;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all var(--map-transition-normal) ease;
  white-space: nowrap;
  border: none;
  cursor: pointer;
  overflow: hidden;
  max-width: 3.5rem;
}

.map-toggle-button:hover {
  background: linear-gradient(90deg, var(--map-primary-dark) 0%, var(--map-primary-color) 100%);
  max-width: 15rem;
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
  transform: translateY(-2px);
}

.map-toggle-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: white;
  color: var(--map-primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 1.2rem;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.map-toggle-text {
  opacity: 0;
  transition: opacity var(--map-transition-normal);
  max-width: 0;
}

.map-toggle-button:hover .map-toggle-text {
  opacity: 1;
  max-width: 200px;
}

/* 统计卡片 */
.summary-cards {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1rem;
  width: 100%;
  max-width: 40rem;
}

.card {
  background-color: var(--map-bg-white);
  border-radius: var(--map-radius-lg);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  box-shadow: var(--map-shadow-md);
  transition: all var(--map-transition-normal);
  cursor: pointer;
}

.events-card {
  border-left: 4px solid var(--map-primary-color);
}

.province-card {
  border-left: 4px solid var(--map-secondary-color);
}

.card:hover {
  transform: translateY(-8px);
  box-shadow: var(--map-shadow-xl);
}

.card-icon {
  font-size: 2rem;
  color: var(--map-primary-color);
  background-color: rgba(59, 130, 246, 0.1);
  width: 3.5rem;
  height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  flex-shrink: 0;
}

.province-card .card-icon {
  color: var(--map-secondary-color);
  background-color: rgba(16, 185, 129, 0.1);
}

.card-content {
  flex-grow: 1;
}

.card h2 {
  font-size: 1.75rem;
  color: var(--map-text-dark);
  margin: 0 0 0.25rem 0;
  font-weight: 700;
}

.card p {
  color: var(--map-text-medium);
  margin: 0;
  font-size: 0.95rem;
}

/* 事件列表区域 */
.event-list-section {
  width: 100%;
  max-width: 1200px;
}

.section-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 2rem;
}

.list-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--map-text-dark);
  margin: 0 0 0.75rem 0;
  text-align: center;
}

.section-divider {
  width: 6rem;
  height: 4px;
  background: linear-gradient(90deg, var(--map-primary-color), var(--map-secondary-color));
  border-radius: 2px;
}

.event-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  padding: 0;
  margin: 0;
  list-style: none;
}

.event-item {
  background: var(--map-bg-white);
  border-radius: var(--map-radius-lg);
  overflow: hidden;
  box-shadow: var(--map-shadow-md);
  transition: all var(--map-transition-normal);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.event-item:hover {
  transform: translateY(-8px);
  box-shadow: var(--map-shadow-xl);
}

.event-image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.event-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--map-transition-normal);
}

.event-item:hover .event-image {
  transform: scale(1.05);
}

.event-location-badge {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 9999px;
  font-size: 0.8rem;
  font-weight: 500;
  backdrop-filter: blur(4px);
}

.event-info {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.event-title {
  margin: 0 0 0.75rem 0;
  font-size: 1.25rem;
  color: var(--map-text-dark);
  font-weight: 600;
  line-height: 1.4;
}

.event-description {
  color: var(--map-text-medium);
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0 0 1.5rem 0;
  flex-grow: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.event-detail-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem 0;
  background: linear-gradient(90deg, var(--map-primary-color) 0%, var(--map-primary-light) 100%);
  color: white;
  border: none;
  border-radius: var(--map-radius-md);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--map-transition-fast);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  margin-top: auto;
}

.event-detail-btn:hover {
  background: linear-gradient(90deg, var(--map-primary-dark) 0%, var(--map-primary-color) 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.btn-icon {
  font-size: 1.2rem;
  transition: transform var(--map-transition-fast);
}

.event-detail-btn:hover .btn-icon {
  transform: translateX(4px);
}

/* 事件详情弹窗 */
.event-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
  backdrop-filter: blur(4px);
  animation: fadeIn var(--map-transition-fast) ease-out;
}

.modal-content {
  background-color: var(--map-bg-white);
  border-radius: var(--map-radius-lg);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  position: relative;
  box-shadow: var(--map-shadow-xl);
  animation: slideUp var(--map-transition-normal) ease-out;
}

.modal-close-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background-color: rgba(255, 255, 255, 0.8);
  color: var(--map-text-dark);
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  z-index: 10;
  transition: all var(--map-transition-fast);
  backdrop-filter: blur(4px);
}

.modal-close-btn:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: rotate(90deg);
}

.modal-image {
  width: 100%;
  height: 300px;
  overflow: hidden;
}

.modal-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.modal-body {
  padding: 2rem;
}

.modal-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--map-text-dark);
  margin: 0 0 1rem 0;
  line-height: 1.3;
}

.modal-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--map-text-medium);
  font-size: 1rem;
  margin-bottom: 1.5rem;
}

.location-icon {
  color: var(--map-primary-color);
}

.modal-divider {
  height: 1px;
  background-color: rgba(0, 0, 0, 0.1);
  margin: 1.5rem 0;
}

.modal-description {
  color: var(--map-text-medium);
  font-size: 1.1rem;
  line-height: 1.7;
  margin: 0 0 2rem 0;
}

.modal-footer {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.modal-action-btn {
  padding: 0.75rem 1.5rem;
  border-radius: var(--map-radius-md);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--map-transition-fast);
  border: 1px solid var(--map-primary-color);
  background-color: transparent;
  color: var(--map-primary-color);
}

.modal-action-btn:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

.modal-action-btn.primary {
  background-color: var(--map-primary-color);
  color: white;
}

.modal-action-btn.primary:hover {
  background-color: var(--map-primary-dark);
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.animate-fadeIn {
  animation: fadeIn var(--map-transition-normal) ease-out forwards;
}

/* 禁止滚动 */
body.modal-open {
  overflow: hidden;
}

/* 加载指示器样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: var(--map-radius-lg);
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 4px solid var(--map-bg-light);
  border-top: 4px solid var(--map-primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.loading-text {
  color: var(--map-text-medium);
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .map-container {
    padding: 1.5rem 1rem 2rem 1rem;
  }

  .event-grid {
    grid-template-columns: 1fr;
  }

  .summary-cards {
    flex-direction: column;
    gap: 1rem;
  }

  .welcome-banner {
    flex-direction: column;
    padding: 1rem;
  }

  .modal-content {
    max-width: 95%;
  }

  .modal-image {
    height: 200px;
  }

  .modal-body {
    padding: 1.5rem;
  }
}
</style>