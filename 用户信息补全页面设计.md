# 用户信息补全页面设计

## 功能概述

当用户登录后，如果检测到character表中没有belongs_to_id为用户id且relationship为"自己"的条目，页面将跳转到UserPage并提示用户补全个人信息。

## 页面结构

### 1. 欢迎横幅
- 🎉 欢迎图标（带动画效果）
- 欢迎文字和说明
- 关闭按钮
- 渐变背景设计

### 2. 用户信息展示区域

#### 头部信息
- 用户头像（来自user表）
- 用户名（不可更改）
- 昵称
- 邮箱
- "完善信息"按钮

#### 信息卡片
**账户信息卡片**
- 用户名（只读）
- 邮箱（只读）
- 手机号（可编辑）
- 昵称（可编辑）
- 个性签名（可编辑）

**真实身份信息卡片**
- 真实姓名（必填*）
- 性别（必填*）
- 生日（可选）
- 真人头像（必填*）

### 3. 编辑模态框

#### 账户信息区域
- 用户头像上传
- 昵称输入框
- 手机号输入框
- 个性签名文本域

#### 真实身份信息区域
- 真人头像上传（必填）
- 真实姓名输入框（必填）
- 性别选择器（必填）
- 生日日期选择器

## 数据结构

### User表字段（来自后端）
```javascript
userInfo = {
  username: String,    // 用户名（不可更改）
  email: String,       // 邮箱（只读）
  phone: String,       // 手机号（可编辑）
  nickname: String,    // 昵称（可编辑）
  introduction: String, // 个性签名（可编辑）
  avatar: String       // 用户头像（可编辑）
}
```

### Character表字段（需要创建）
```javascript
characterInfo = {
  realName: String,    // 真实姓名（必填）
  gender: String,      // 性别（必填：male/female）
  birthday: String,    // 生日（可选）
  realAvatar: File     // 真人头像（必填）
}
```

## 表单验证规则

### 必填项验证
1. **真实姓名**
   - 不能为空
   - 长度不超过10个字符

2. **性别**
   - 必须选择（male/female）

3. **真人头像**
   - 必须上传
   - 支持JPG、PNG格式
   - 文件大小不超过5MB

### 可选项验证
1. **昵称**
   - 长度不超过20个字符

2. **手机号**
   - 11位数字
   - 符合中国手机号格式

3. **个性签名**
   - 长度不超过200个字符

4. **用户头像**
   - 支持JPG、PNG格式
   - 文件大小不超过2MB

5. **生日**
   - 日期格式验证
   - 不能是未来日期

## 界面特色

### 1. 视觉设计
- 浅色渐变背景
- 卡片式布局
- 圆角设计
- 阴影效果
- 现代化UI风格

### 2. 交互效果
- 欢迎横幅动画
- 按钮悬停效果
- 模态框滑入动画
- 表单焦点效果
- 加载状态指示

### 3. 用户体验
- 清晰的必填项标识（红色*）
- 实时表单验证
- 友好的错误提示
- 图片预览功能
- 响应式设计

## 技术实现

### 1. Vue 3 特性
- Composition API
- 响应式数据
- 计算属性
- 生命周期钩子

### 2. 表单处理
- 双向数据绑定
- 文件上传处理
- 表单验证逻辑
- 错误状态管理

### 3. 样式技术
- CSS Grid布局
- Flexbox布局
- CSS动画
- 渐变背景
- 响应式设计

## 状态管理

### 页面状态
```javascript
const isCompleteMode = ref(true)     // 是否为信息补全模式
const showWelcome = ref(true)        // 是否显示欢迎提示
const isEditing = ref(false)         // 是否在编辑状态
const isLoading = ref(false)         // 是否在加载中
```

### 数据状态
```javascript
const userInfo = ref({})             // 用户基本信息
const characterInfo = ref({})        // 用户角色信息
const editUserInfo = ref({})         // 编辑中的信息
const formErrors = ref({})           // 表单验证错误
```

## API集成计划

### 1. 获取用户信息
```javascript
// 现有API
const profileData = await GetUserProfile()
const avatarData = await GetUserAvatar()

// 需要新增的API
const characterData = await GetUserCharacter()
```

### 2. 保存用户信息
```javascript
// 需要新增的API
await UpdateUserProfile(userInfo)
await CreateUserCharacter(characterInfo)
await UploadUserAvatar(avatarFile)
await UploadRealAvatar(realAvatarFile)
```

### 3. 验证逻辑
```javascript
// 检查是否需要补全信息
const needsCompletion = computed(() => {
  return !characterInfo.value.realName || 
         !characterInfo.value.gender || 
         !characterInfo.value.realAvatar
})
```

## 后续功能扩展

### 1. 信息完善度指示
- 进度条显示
- 完成度百分比
- 分步骤引导

### 2. 头像处理
- 图片裁剪功能
- 人脸检测验证
- 多尺寸生成

### 3. 社交功能
- 好友推荐
- 隐私设置
- 信息公开度控制

## 安全考虑

### 1. 数据验证
- 前端表单验证
- 后端数据校验
- 文件类型检查
- 文件大小限制

### 2. 隐私保护
- 真实信息加密存储
- 头像访问权限控制
- 敏感信息脱敏显示

### 3. 用户体验
- 优雅的错误处理
- 数据丢失防护
- 操作确认机制

## 总结

这个用户信息补全页面设计：

1. **功能完整** - 涵盖了用户基本信息和身份验证信息
2. **体验友好** - 清晰的界面引导和交互反馈
3. **验证严格** - 完善的表单验证和错误处理
4. **设计现代** - 美观的视觉效果和动画
5. **扩展性强** - 易于添加新功能和API集成

通过这个页面，用户可以方便地完善个人信息，为后续的人脸识别和个性化功能提供必要的数据基础。
