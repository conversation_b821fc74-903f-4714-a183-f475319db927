{"name": "shiguangyinji_frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@vueup/vue-quill": "^1.2.0", "axios": "^1.7.9", "coordtransform": "^2.1.2", "html-docx-js": "^0.3.1", "html2canvas": "^1.4.1", "html2json": "^1.0.2", "jspdf": "^2.5.2", "leaflet": "^1.9.4", "leaflet.markercluster": "^1.5.3", "quill": "latest", "quill-delta": "^5.1.0", "quill-image-drop-module": "latest", "quill-image-extend-module": "latest", "quill-image-resize-module": "latest", "utils": "^0.3.1", "vue": "^3.5.12", "vue-leaflet": "^0.1.0", "vue-quill-editor": "latest", "vue-router": "^4.4.5", "vue-toastification": "^2.0.0-rc.5", "vue3-quill": "^0.3.1", "vueleaflet": "^3.0.2", "vuex": "next"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "sass-embedded": "^1.80.6", "tailwindcss": "^4.1.6", "vite": "^5.4.10"}}