#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试视频处理修复效果
"""

import os
import sys
import subprocess
import tempfile
from pathlib import Path

# 设置Django环境
project_dir = 'shiguangyinji_backend-algorithm'
sys.path.insert(0, project_dir)
os.chdir(project_dir)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shiguangyinji.settings')

import django
django.setup()

def test_moviepy_compatibility():
    """测试MoviePy版本兼容性"""
    print("测试MoviePy版本兼容性")
    print("=" * 50)

    try:
        import moviepy
        print(f"MoviePy版本: {moviepy.__version__}")

        # 测试VideoFileClip的write_videofile参数
        from moviepy import VideoFileClip, ImageClip, AudioFileClip

        # 创建一个简单的测试视频
        test_image = "media/tmp/test_compatibility.jpg"
        test_audio = "media/tmp/test_compatibility.mp3"
        test_output = "media/tmp/test_compatibility.mp4"

        os.makedirs(os.path.dirname(test_image), exist_ok=True)

        # 创建测试图片
        if not os.path.exists(test_image):
            try:
                from PIL import Image
                import numpy as np
                img_array = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
                img = Image.fromarray(img_array)
                img.save(test_image)
                print(f"✅ 创建测试图片: {test_image}")
            except Exception as e:
                print(f"❌ 创建测试图片失败: {e}")
                return False

        # 创建测试音频
        if not os.path.exists(test_audio):
            try:
                cmd = [
                    'ffmpeg', '-y', '-f', 'lavfi', '-i', 'anullsrc=channel_layout=stereo:sample_rate=44100',
                    '-t', '2', '-c:a', 'mp3', test_audio
                ]
                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"✅ 创建测试音频: {test_audio}")
                else:
                    print(f"❌ 创建测试音频失败: {result.stderr}")
                    return False
            except Exception as e:
                print(f"❌ 创建测试音频失败: {e}")
                return False

        # 测试不同的write_videofile参数组合
        try:
            audio_clip = AudioFileClip(test_audio)
            image_clip = ImageClip(test_image, duration=audio_clip.duration)
            video_clip = image_clip.with_audio(audio_clip)

            print("测试write_videofile参数兼容性...")

            # 测试1: 使用verbose参数（可能在新版本中失败）
            try:
                video_clip.write_videofile(
                    test_output,
                    codec="libx264",
                    audio_codec="aac",
                    verbose=False,
                    logger=None
                )
                print("✅ verbose参数兼容")
                verbose_compatible = True
            except TypeError as e:
                if 'verbose' in str(e):
                    print("⚠️  verbose参数不兼容")
                    verbose_compatible = False
                else:
                    raise e

            # 测试2: 不使用verbose参数
            if not verbose_compatible:
                try:
                    video_clip.write_videofile(
                        test_output,
                        codec="libx264",
                        audio_codec="aac",
                        logger=None
                    )
                    print("✅ 无verbose参数兼容")
                except Exception as e:
                    print(f"❌ 无verbose参数也失败: {e}")
                    return False

            # 清理测试文件
            for file_path in [test_image, test_audio, test_output]:
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                except:
                    pass

            return True

        except Exception as e:
            print(f"❌ MoviePy兼容性测试失败: {e}")
            return False

    except Exception as e:
        print(f"❌ MoviePy导入失败: {e}")
        return False

def test_font_availability():
    """测试字体可用性"""
    print("\n测试字体可用性")
    print("=" * 50)

    font_paths = [
        'C:/Windows/Fonts/simhei.ttf',      # 黑体
        'C:/Windows/Fonts/simsun.ttc',      # 宋体
        'C:/Windows/Fonts/msyh.ttc',        # 微软雅黑
        'C:/Windows/Fonts/STXINGKA.TTF',    # 华文行楷
        '/System/Library/Fonts/PingFang.ttc',  # macOS
        '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux
    ]

    available_fonts = []

    for font_path in font_paths:
        if os.path.exists(font_path):
            print(f"✅ 找到字体: {font_path}")
            available_fonts.append(font_path)
        else:
            print(f"❌ 字体不存在: {font_path}")

    if available_fonts:
        print(f"\n推荐使用字体: {available_fonts[0]}")
        return available_fonts[0]
    else:
        print("⚠️  未找到任何中文字体")
        return None

def test_textclip_compatibility():
    """测试TextClip兼容性"""
    print("\n测试TextClip兼容性")
    print("=" * 50)

    try:
        from moviepy import TextClip

        # 获取可用字体
        font_path = test_font_availability()

        test_text = "测试字幕"

        # 测试不同的TextClip参数组合
        test_configs = [
            {
                'name': '完整参数',
                'params': {
                    'text': test_text,
                    'font': font_path,
                    'font_size': 36,
                    'color': 'white',
                    'method': 'label',
                    'size': (800, None),
                    'duration': 2
                }
            },
            {
                'name': '简化参数',
                'params': {
                    'text': test_text,
                    'font_size': 36,
                    'color': 'white',
                    'duration': 2
                }
            },
            {
                'name': '最小参数',
                'params': {
                    'text': test_text,
                    'duration': 2
                }
            }
        ]

        for config in test_configs:
            try:
                if config['name'] == '完整参数' and not font_path:
                    print(f"⚠️  跳过{config['name']}测试（无可用字体）")
                    continue

                text_clip = TextClip(**config['params'])
                print(f"✅ {config['name']}兼容")

                # 测试位置设置
                try:
                    positioned_clip = text_clip.with_position(('center', 'bottom'))
                    print(f"  ✅ 位置设置兼容")
                except Exception as e:
                    print(f"  ⚠️  位置设置问题: {e}")

            except Exception as e:
                print(f"❌ {config['name']}不兼容: {e}")

        return True

    except Exception as e:
        print(f"❌ TextClip测试失败: {e}")
        return False

# GPU相关测试已移除，现在使用纯MoviePy方式

def test_handle_functions():
    """测试handle.py中的修复函数"""
    print("\n测试handle.py修复函数")
    print("=" * 50)

    try:
        from utils.handle import add_text_dynamic

        # 创建一个简单的测试视频
        test_image = "media/tmp/test_handle.jpg"
        test_audio = "media/tmp/test_handle.mp3"

        os.makedirs(os.path.dirname(test_image), exist_ok=True)

        # 创建测试文件
        if not os.path.exists(test_image):
            from PIL import Image
            import numpy as np
            img_array = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
            img = Image.fromarray(img_array)
            img.save(test_image)

        if not os.path.exists(test_audio):
            cmd = [
                'ffmpeg', '-y', '-f', 'lavfi', '-i', 'anullsrc=channel_layout=stereo:sample_rate=44100',
                '-t', '2', '-c:a', 'mp3', test_audio
            ]
            subprocess.run(cmd, capture_output=True, text=True)

        # 测试字幕添加函数
        from moviepy import ImageClip, AudioFileClip

        audio_clip = AudioFileClip(test_audio)
        image_clip = ImageClip(test_image, duration=audio_clip.duration)
        video_clip = image_clip.with_audio(audio_clip)

        test_text = "这是一个测试字幕"
        result_clip = add_text_dynamic(video_clip, test_text)

        if result_clip:
            print("✅ add_text_dynamic函数正常工作")
        else:
            print("❌ add_text_dynamic函数返回None")

        # 清理测试文件
        for file_path in [test_image, test_audio]:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except:
                pass

        return True

    except Exception as e:
        print(f"❌ handle函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_comprehensive_test():
    """运行综合测试"""
    print("视频处理修复效果测试")
    print("=" * 60)

    test_results = {}

    # 运行各项测试
    test_results['moviepy_compatibility'] = test_moviepy_compatibility()
    test_results['font_availability'] = test_font_availability() is not None
    test_results['textclip_compatibility'] = test_textclip_compatibility()
    # GPU相关测试已移除
    # test_results['gpu_acceleration'] = test_gpu_acceleration()
    # test_results['video_config'] = test_video_config()
    test_results['handle_functions'] = test_handle_functions()

    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print("=" * 60)

    passed_tests = 0
    total_tests = len(test_results)

    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_tests += 1

    print(f"\n总体结果: {passed_tests}/{total_tests} 项测试通过")

    if passed_tests == total_tests:
        print("🎉 所有测试通过！修复效果良好")
    elif passed_tests >= total_tests * 0.8:
        print("⚠️  大部分测试通过，系统基本可用")
    else:
        print("❌ 多项测试失败，需要进一步检查")

    # 提供建议
    print("\n建议:")
    if not test_results['moviepy_compatibility']:
        print("- 检查MoviePy版本，考虑升级或降级")
    if not test_results['font_availability']:
        print("- 安装中文字体文件")
    if not test_results['gpu_acceleration']:
        print("- 检查GPU驱动和FFmpeg配置")

    print("- 生成一个测试视频验证实际效果")
    print("- 检查日志输出确认修复是否生效")

if __name__ == "__main__":
    run_comprehensive_test()
